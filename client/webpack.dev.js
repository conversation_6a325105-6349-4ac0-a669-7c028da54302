/* eslint-disable */
const path = require('path');
const CommonConfig = require('./webpack.common.js');
const WorkersConfig = require('./webpack-workers.js');
const CopyPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const merge = require('webpack-merge');

const serverProxy = 'http://localhost:8100';

const config = [];

const es7Config = CommonConfig.find(({ jsTarget }) => jsTarget === 'es7');
delete es7Config.jsTarget;

config.push(merge(es7Config, {
  mode: 'development',
  devServer: {
    static: {  directory: path.join(__dirname, 'dist') },
    compress: true,
    https: true,
    port: 9001,
    historyApiFallback: true,
    webSocketServer: false,
    proxy: {
      '/stocks/edis-redirect': serverProxy,
      '/stocks/profile/margin-pledge/redirect': serverProxy,
      '/stocks/ipo-bank-redirect': serverProxy,
      '/stocks/get-version': serverProxy,
      '/stocks/time': serverProxy,
      '/stocks/static': {
        target: serverProxy,
        bypass: (req) => {
          if (req.url.indexOf('js') !== -1 && req.url.indexOf('css') !== -1) {
            return req.url;
          }
        }
      },
      '/workers': {
        target: serverProxy,
        bypass: (req) => {
          if (req.url.indexOf('shared-worker.js') !== -1) {
            return '/stocks/static/workers/shared-worker.js';
          }
        }
      },
    },
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: 'css/[name].css',
      ignoreOrder: true,
    }),
    new CopyPlugin({
      patterns: [
        { from: 'static/index.html', to: 'views/index.handlebars' },
      ],
    }),
  ]
}));

config.push(WorkersConfig);

module.exports = config;
