/* eslint-disable */
require('core-js/stable');
require('core-js/features/promise/finally');
require('regenerator-runtime/runtime');
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const { ModuleFederationPlugin } = require('webpack').container;
const { InjectManifest } = require('workbox-webpack-plugin');
const { getEquitiesStaticURL } = require('../config/ENUMS');
const { dependencies } = require('../package.json');

const jsTargets = [
  {
    target: 'es7',
    polyfills: ['core-js/features/promise/finally'],
    plugins: [
      new InjectManifest({
        swSrc: './src/sw-src.js',
        swDest: 'workers/service-worker.js',
        include: [/\.css$/, /\.js$/, /\.svg$/],
        exclude: [/\.DS*/],
        precacheManifestFilename: 'workers/precache-manifest.[manifestHash].js',
      }),
      new HtmlWebpackPlugin({
        template: './views/partials/es7-scripts.handlebars',
        filename: 'views/partials/es7-scripts.handlebars',
        inject: false,
      }),
    ],
  },
  {
    target: 'es5',
    polyfills: ['core-js/stable', 'regenerator-runtime/runtime'],
    presets: [
      [
        '@babel/preset-env',
        {
          targets: '> 0.25%, not dead',
        },
      ],
    ],
    plugins: [
      new HtmlWebpackPlugin({
        template: './views/index.handlebars',
        filename: 'views/index.handlebars',
        inject: false,
      }),
    ],
  },
];

const config = [];

for (const targetConfig of jsTargets) {
  config.push({
    entry: {
      app: (targetConfig.polyfills || []).concat(['./src/layout/App/index.jsx']),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@common': path.resolve(__dirname, 'src/components/common'),
        '@modules': path.resolve(__dirname, 'src/components/modules'),
        '@pages': path.resolve(__dirname, 'src/components/pages'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@service': path.resolve(__dirname, 'src/services'),
        '@commonStyles': path.resolve(__dirname, 'src/styles/common'),
        '@constants': path.resolve(__dirname, 'src/constants'),
        '@layout': path.resolve(__dirname, 'src/layout'),
        '@config': path.resolve(__dirname, '../config'),
      },
      modules: [path.resolve(__dirname, 'src'), 'node_modules', path.resolve(__dirname, '../node_modules')],
      extensions: ['.js', '.jsx'],
      fallback: { "querystring": require.resolve("querystring-es3") },
    },
    output: {
      filename: `js/${targetConfig.target}/[name].js`,
      chunkFilename: `js/${targetConfig.target}/[name].js`,
      path: path.resolve(__dirname, 'dist'),
      publicPath: getEquitiesStaticURL(),
    },
    module: {
      rules: [
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader',
          ],
          exclude: /\.module\.css$/,
        },
        {
          test: /\.scss$/,
          use: [
            MiniCssExtractPlugin.loader,
            {
              loader: 'css-loader',
              options: {
                importLoaders: 1,
                modules: true,
              }
            },
            'sass-loader',
          ],
        },
        {
          test: /\.jsx?$/,
          exclude: /(node_modules)/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: (targetConfig.presets || []).concat(['@babel/preset-react']),
              plugins: ['@babel/plugin-syntax-dynamic-import', '@babel/plugin-proposal-object-rest-spread'],
            },
          }
        },
      ],
    },
    plugins: [
      new CopyPlugin({
        patterns: [
          { from: 'static', to: '' },
          { from: 'views/EDSLRedirect.handlebars', to: 'views/' },
          { from: 'views/PledgeRedirect.handlebars', to: 'views/' },
          { from: 'views/HniAndEmployeeRedirect.handlebars', to: 'views/' },
          { from: 'views/partials/metaTags.handlebars', to: 'views/partials/' },
          { from: 'static/index.html', to: '', transform: content => getEquitiesStaticURL(content) },
          { from: 'static/manifest.json', to: '', transform: content => getEquitiesStaticURL(content) },
        ],
      }),
      new ModuleFederationPlugin({
        name: 'Host',
        filename: 'moduleEntry.js',
        remotes: {
          CorporateActions: ['staging', 'development'].includes(process.env.NODE_ENV)
           ? `CorporateActions@https://h5-stocks-staging.paytmmoney.com/desktop-moduleEntry.js`
            : `CorporateActions@https://h5-stocks.paytmmoney.com/desktop-moduleEntry.js`,
        },
        exposes: {},
        shared: {
          react: {
            singleton: true,
            requiredVersion: dependencies.react,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: dependencies['react-dom'],
          },
        },
      }),
    ].concat(
      targetConfig.plugins || []
    ),
    jsTarget: targetConfig.target,
  })
}

module.exports = config;
