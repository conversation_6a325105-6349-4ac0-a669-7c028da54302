import axios from 'axios';
import { REQUEST_METHODS, ENDPOINTS } from '@constants/api-constants';
import { v1 as uuid } from 'uuid';
import { COOKIES } from '@utils/enum';
// eslint-disable-next-line import/no-cycle
import { log } from '@layout/App/api';
import { sendBucket } from '@common/bridge';
import { WEB_VIEW_CONSTANTS } from '@constants/web-view-constants';
import LocalStorage from '@service/LocalStorage';
import { LOCAL_STORAGE_KEYS } from '@layout/LoggedInLayout/enums';
import {
  getCookieValue, deleteCookie, getUrlParameter, isBrowser, isWebView, paramsToObject,
} from '@utils';

import CONFIG from '../config';

const { CancelToken } = axios;
export const CancelRequestCode = 'PM_WEB_CANCELLED_REQUEST';
export const CancelTimeoutRequest = 'ECONNABORTED';
export const Authorization = 'Basic cGF5dG0tbW9uZXk6c05VcnBaMnc3MjJJdXZVMnBnNnhPTThhbGRnWGtSOW0=';

export function getMessage(response) {
  return response?.meta?.displayMessage || response?.message;
}

const isMerchantLoginFlow = isBrowser() && getUrlParameter('isMerchantLogin', window.location.search);
const accessToken = isBrowser() && getUrlParameter('accessToken', window.location.search);
const requestToken = isBrowser() && getUrlParameter('requestToken', window.location.search);
const merchantFlow = accessToken || requestToken;
const isMerchantFlow = (merchantFlow || (isBrowser() && getUrlParameter('requestToken', getUrlParameter('returnUrl', window.location.search)))) && !isMerchantLoginFlow;
const pmlEnv = isBrowser() && getUrlParameter('pmlEnv', window.location.search);
const apiKey = isBrowser() && getUrlParameter('apiKey', window.location.search);

const basicHeaders = {
  accept: 'application/json',
  'Content-Type': 'application/json',
  'x-pmmodule-name': 'paytmmoney',
  'x-pmngx-key': 'paytmmoney',
};

export const handle419Errors = () => {
  deleteCookie(COOKIES.TWOFA_TOKEN);
  if (isWebView() && !LocalStorage.get(LOCAL_STORAGE_KEYS.REFRESH_2FA_TOKEN)) {
    LocalStorage.set(LOCAL_STORAGE_KEYS.REFRESH_2FA_TOKEN, true);
    sendBucket(WEB_VIEW_CONSTANTS.REFRESH_2FA_TOKEN);
  } else {
    window.location.href = `${window.location.origin}/stocks/passcode?returnUrl=${encodeURIComponent(window.location.href)}`;
  }
};

export const handle401ForPublisher = () => {
  const target_url = isBrowser() && decodeURIComponent(getUrlParameter('target_url', window.location.search));
  if (target_url) {
    const urlParams = new URLSearchParams(target_url.split('?')[1]);
    const params = paramsToObject(urlParams.entries());
    window.location.href = `${CONFIG.AUTH}/auto-login?apiKey=${apiKey}&pmlEnv=publisherApi&dataValues=${JSON.stringify(params)}`;
  } else {
    window.location.href = `${CONFIG.AUTH}?pmlEnv=${pmlEnv}`;
  }
};

// eslint-disable-next-line no-extend-native
Promise.prototype.cancel = () => {};

function makeRequest({
  endpoint, method, shouldRemoveBasicHeaders, isMerchant, maskedUrl, ...customOptions
}, urlVariable, params) {
  const variableList = Array.isArray(urlVariable) ? urlVariable : [urlVariable];
  const url = endpoint(...variableList);
  let twoFaTokenHeader = {};
  if (getCookieValue(COOKIES.M_TWOFA_TOKEN) && isMerchantFlow && pmlEnv !== 'publisherApi') {
    twoFaTokenHeader = {
      [COOKIES.TWOFA_TOKEN]: getCookieValue(COOKIES.M_TWOFA_TOKEN),
    };
  } else if (getCookieValue(COOKIES.TWOFA_TOKEN)) {
    twoFaTokenHeader = {
      [COOKIES.TWOFA_TOKEN]: getCookieValue(COOKIES.TWOFA_TOKEN),
    };
  }
  const options = {
    url,
    method,
    ...customOptions,
    headers: {
      ...(!shouldRemoveBasicHeaders ? {
        ...basicHeaders,
        [COOKIES.SSO_TOKEN]: (isMerchantFlow && pmlEnv !== 'publisherApi' ? getCookieValue(COOKIES.M_SSO_TOKEN) : getCookieValue(COOKIES.SSO_TOKEN)),
        ...(twoFaTokenHeader),
        [COOKIES.USER_AGENT]: (isMerchantFlow && pmlEnv !== 'publisherApi' ? getCookieValue(COOKIES.M_USER_AGENT)
          : getCookieValue(COOKIES.USER_AGENT)),
        'x-request-id': uuid(),
      } : {}),
      ...customOptions.headers,
    },
  };
  switch (method) {
    case REQUEST_METHODS.GET:
    case REQUEST_METHODS.DELETE:
      options.params = params;
      options.data = {};
      break;

    case REQUEST_METHODS.POST:
    case REQUEST_METHODS.PUT:
      options.data = params;
      break;

    default:
      options.params = params;
  }

  let cancel;
  options.cancelToken = new CancelToken((c) => {
    cancel = c.bind(null, CancelRequestCode);
  });

  const reqPromise = axios
    .request(options)
    .then((response) => response.data)
    .catch((error) => {
      if (error && error.message === CancelRequestCode) {
        return Promise.reject(CancelRequestCode);
      }
      const filteredOptions = {
        url: maskedUrl ? maskedUrl(...variableList) : options?.url,
        method: options?.method,
        'x-request-id': options?.headers['x-request-id'],
      };
      if (error.response) {
        filteredOptions.status = error.response?.status;
        filteredOptions.data = error.response.data;
      } else {
        filteredOptions.message = error?.message;
        filteredOptions.timeout = error?.config?.timeout;
      }
      if (error && error.code === CancelTimeoutRequest && customOptions?.timeout) {
        return Promise.reject(CancelTimeoutRequest);
      }
      try {
        if (options.url !== ENDPOINTS.LOG_ERROR.endpoint()) {
          log([{
            level: 'error',
            key: 'api-error',
            timestamp: new Date().toISOString(),
            version: window.pmVersion,
            data: JSON.stringify({
              request: filteredOptions,
              url: window.location.href,
            }),
          }]);
        }
      } catch (err) {}
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (errorData.status === 401
          || (errorData.status === 403 && (getMessage(errorData) === 'x_user_agent not passed correctly'))) {
          deleteCookie(COOKIES.SSO_TOKEN);
          deleteCookie(COOKIES.USER_AGENT);
          deleteCookie(COOKIES.TWOFA_TOKEN);
          if (isWebView()) {
            sendBucket(WEB_VIEW_CONSTANTS.TOKEN_EXPIRED);
          } else if (pmlEnv === 'publisherApi') {
            handle401ForPublisher();
          } else {
            window.location.href = `${CONFIG.AUTH}?returnUrl=${encodeURIComponent(window.location.href)}`;
          }
        }
        if (errorData.status === 419 || error.response.status === 419) {
          handle419Errors();
        }
      }
      return Promise.reject(error.response && error.response.data);
    });

  reqPromise.cancel = cancel;
  return reqPromise;
}

export default makeRequest;
