import React, { useEffect } from 'react';
import { primarySort, transformPositionData } from '@modules/Positions/utils';
import useSort from '@common/useSort';
import { POSITIONS_SORT } from '@utils/enum';
import TableHeader from '@common/Table/Header';
import LocalStorage from '@service/LocalStorage';
import If from '@common/If';
import { mobileBrowser } from '@utils';
import styles from './styles';
import TableRow from './partials/Position';
import { INTEROPERABILITY_MODES, POSITIONS_TAB } from '../enums';
import usePositionFeed from '../usePositionFeed';
import { useMultipleExitContext } from '../../../pages/Positions/MultipleExitContext';

function PositionList({
  options, onClick, positionType, tableData, openedMarketDepth, setOpenedMarketDepth,
  setShowControl, showControl, activeSwitch, interoperability,
}) {
  const requiredStreams = usePositionFeed();

  const {
    data, activeSort, isLoading, handleSorting,
  } = useSort({
    rawData: options,
    transformFxn: transformPositionData,
    sortingFxn: primarySort,
    requiredFeedFxn: requiredStreams,
    defaultSort: LocalStorage.get(POSITIONS_SORT, false),
    disableLoading: interoperability === INTEROPERABILITY_MODES[1].id,
  });
  const { updateCurrentPositions } = useMultipleExitContext();
  useEffect(() => {
    updateCurrentPositions(data);
  }, [data, updateCurrentPositions]);
  return (
    <>
      <If test={!mobileBrowser()}>
        <TableHeader
          options={tableData}
          active={activeSort}
          onClick={(selectedTab) => {
            LocalStorage.set(POSITIONS_SORT, selectedTab, '', false);
            handleSorting(selectedTab);
          }}
          className={styles.tableRow}
        />
      </If>
      <div className={styles.positionsListWrapper}>
        {!isLoading && data.map((positionData, index) => (
          <TableRow
            showClosedTag={positionType === POSITIONS_TAB.ALL}
            key={index}
            index={index}
            rowData={tableData}
            positionData={positionData}
            onClick={onClick}
            openedMarketDepth={openedMarketDepth}
            setOpenedMarketDepth={setOpenedMarketDepth}
            showControl={showControl}
            setShowControl={setShowControl}
            activeSwitch={activeSwitch}
            interoperability={interoperability}
          />
        ))}
      </div>
    </>
  );
}

export default PositionList;
