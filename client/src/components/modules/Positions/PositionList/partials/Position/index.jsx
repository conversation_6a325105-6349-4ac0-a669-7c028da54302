import React, { useMemo, useContext } from 'react';
import PropTypes from 'prop-types';
import { UserContext } from '@layout/App/UserContext';
import { classNames as cx } from '@utils/style';
import { Percent, Change } from '@common/Prices';
import { resultDecider, positionActions } from '@modules/Positions/utils';
import { formatPrice, mobileBrowser, isBondInstrument } from '@utils/index';
import { ICON_NAME, staticImagePath } from '@common/Icon/enums';
import Button from '@common/Table/Button';
import { SEGMENT_TYPES, TRANSACTION_TYPES } from '@utils/enum';
import Checkbox from '@common/Checkbox';
import Tooltip, { TOOLTIP_DIRECTION } from '@common/Tooltip';
import If from '@common/If';
import { useMultipleExitContext } from '@pages/Positions/MultipleExitContext';
import { RETURNS_TYPE } from '@pages/Portfolio/partials/PortfolioTable/config';
import { table } from '@commonStyles';
import { sendEvent } from '@service/AnalyticsService';
import MarketDepthContainer from '../../../common/MarketDepthContainer/index';
import DropdownOptions from '../../../common/DropdownOptions/index';
import styles from '../../styles';
import {
  CONTROLS, TOOL_TIPS, COLUMN_TYPE, ACTION_TYPE,
  POSITIONS_STATUS, MORE_OPTIONS, BOND_OPTIONS, INTEROPERABILITY_MODES,
} from '../../../enums';

function Position({
  rowData, positionData, onClick, showClosedTag, index, openedMarketDepth,
  setOpenedMarketDepth, showControl = -1, setShowControl, activeSwitch, interoperability,
}) {
  const {
    display_name, exchange, security_id, segment, instrument, isin_code, tick_size, instrument_type,
    lot_size, quantity, display_pos_type, display_product,
  } = positionData;
  const isMarketDepthOpen = openedMarketDepth === index;
  const shouldShowControls = showControl === index;
  const handleMobileRowClick = (e) => {
    e.stopPropagation();
    setShowControl(shouldShowControls ? -1 : index);
  };
  const isBond = isBondInstrument(instrument_type);
  const {
    select, unselect, isExitable,
    isChecked, shouldShowRow, shouldShow, interopsShow,
  } = useMultipleExitContext();
  const {
    mtfFeatureFlag,
  } = useContext(UserContext);

  const handleSelectForExit = (e) => {
    e.stopPropagation();
    if (isChecked(positionData)) {
      unselect(positionData);
    } else {
      select(positionData);
    }
  };

  const isClosedPosition = useMemo(
    () => showClosedTag && positionData.display_pos_status === POSITIONS_STATUS.CLOSED,
    [positionData.display_pos_status, showClosedTag],
  );

  const positionListStyles = cx([styles.positionsList, table.stripedTableRow, styles.tableRow], {
    [styles.marketDepthOpen]: isMarketDepthOpen,
    [styles.rowSelected]: isChecked(positionData),
  });

  const actionDecider = (type, actionIndex) => {
    let actionDetails = {
      message: '',
      type: '',
      icon: '',
    };
    switch (type) {
      case ACTION_TYPE.ADD:
        actionDetails = {
          message: TOOL_TIPS.ADD,
          type: CONTROLS.ADD,
          icon: ICON_NAME.ADD,
        };
        break;
      case ACTION_TYPE.EXIT:
        actionDetails = {
          message: TOOL_TIPS.EXIT,
          type: CONTROLS.EXIT,
          icon: ICON_NAME.EXIT,
        };
        break;
      case ACTION_TYPE.CONVERT:
        actionDetails = {
          message: TOOL_TIPS.CONVERT,
          type: CONTROLS.CONVERT,
          icon: ICON_NAME.CONVERT,
        };
        break;
      case ACTION_TYPE.DETAILS:
        actionDetails = {
          message: TOOL_TIPS.DETAILS,
          type: CONTROLS.DETAILS,
          icon: ICON_NAME.INFO,
        };
        break;
      case ACTION_TYPE.MARKET_DEPTH:
        actionDetails = {
          message: TOOL_TIPS.MARKET_DEPTH,
          type: CONTROLS.MARKET_DEPTH,
          icon: ICON_NAME.ORDERS_MARKET_DEPTH,
        };
        break;
      case ACTION_TYPE.WATCHLIST:
        actionDetails = {
          message: TOOL_TIPS.WATCHLIST,
          type: CONTROLS.MARKET_DEPTH,
          icon: ICON_NAME.NOT_BOOKMARKED,
        };
        break;
      default:
        break;
    }
    return (
      <div>
        <Button
          key={actionIndex}
          message={actionDetails.message}
          onClick={(e) => {
            e.stopPropagation();
            if (type === ACTION_TYPE.ADD || type === ACTION_TYPE.EXIT) {
              if (type === ACTION_TYPE.ADD) {
                sendEvent({
                  event_category: 'order',
                  event_action: 'order_entry_from_positions_add',
                  event_label: 'orders',
                  vertical_name: 'stocks',
                  screenName: '/stocks_orders',
                });
              } else {
                sendEvent({
                  event_category: 'order',
                  event_action: 'order_entry_from_positions_exit',
                  event_label: 'orders',
                  vertical_name: 'stocks',
                  screenName: '/stocks_orders',
                });
              }
              sendEvent({
                event_category: 'order',
                event_action: 'order_entry_from_positions',
                event_label: 'orders',
                vertical_name: 'stocks',
                screenName: '/stocks_orders',
              });
            }
            onClick({
              type: actionDetails.type,
              positionData: { ...positionData },
            });
          }}
          icon={actionDetails.icon}
        />
        {mobileBrowser() && <div>{actionDetails.message}</div>}
      </div>
    );
  };

  const renderActions = (columnValue) => {
    const actions = positionActions({
      status: columnValue.display_pos_status,
      positionType: columnValue.product,
      mtfFeatureFlag,
    });
    actions.push(ACTION_TYPE.DETAILS);
    return actions.map((action, actionIndex) => actionDecider(action, actionIndex));
  };

  const renderExitSelect = () => (
    <span className={styles.checkbox}>
      <If test={isExitable(positionData)}>
        <Checkbox
          checked={isChecked(positionData)}
          onChange={handleSelectForExit}
        />
      </If>
      <If test={!isExitable(positionData)}>
        <Tooltip
          className={styles.tooltip}
          message={(
            <div className={styles.tooltip}>
              <img src={`${staticImagePath}/positions/exit.svg`} alt="exit" />
              Exit Multiple is possible only for Intraday and Overnight Positions
            </div>
  )}
          tooltipDirection={TOOLTIP_DIRECTION.TOP}
        >
          <Checkbox disabled />
        </Tooltip>
      </If>
    </span>
  );
  const renderColumns = (columnType) => {
    const { id } = columnType;
    const filterOptions = (e) => (!((e.name === 'SIP' && segment !== SEGMENT_TYPES.CASH)));
    switch (id) {
      case COLUMN_TYPE.DISPLAY_NAME:
        return (
          <div
            className={`${styles.name} ${table.rowControlsHolder}`}
            key={id}
          >
            <If test={!interopsShow}>
              <If test={shouldShow}>
                <If test={shouldShowRow(positionData)}>
                  {renderExitSelect()}
                </If>
                <If test={!shouldShowRow(positionData)}>
                  <span className={styles.emptySelect} />
                </If>
              </If>
              <If test={!shouldShow}>
                <span />
              </If>
            </If>
            <div>
              <span>{display_name}</span>
              <span>{ interoperability === INTEROPERABILITY_MODES[0].id ? exchange : ''}</span>
            </div>
            <div
              className={cx(styles.closedTag, {
                [styles.displayClosedTag]: isClosedPosition,
              })}
            >
              Closed
            </div>
            <div className={table.rowControls}>
              {renderActions(positionData)}
              {setOpenedMarketDepth && (
              <DropdownOptions
                setOpenedMarketDepth={setOpenedMarketDepth}
                isMarketDepthOpen={isMarketDepthOpen}
                options={isBond ? BOND_OPTIONS : MORE_OPTIONS.filter(filterOptions)}
                security_id={security_id}
                exchange={exchange}
                segment={segment}
                instrumentType={instrument}
                index={index}
                tick_size={tick_size}
                displayName={display_name}
                lot_size={lot_size || quantity}
                isin={isin_code}
                transaction_type={display_pos_type === 'B' ? 'S' : 'B'}
              />
              )}
            </div>
          </div>
        );
      case COLUMN_TYPE.CHANGE:
        return (
          <div key={id}>
            {resultDecider({
              value: positionData[id],
              component: <Percent value={positionData[id]} withSign />,
            })}
          </div>
        );
      case COLUMN_TYPE.PROFIT:
      case COLUMN_TYPE.OVERALL_PROFIT:
        return (
          <div key={id}>
            {resultDecider({
              value: positionData[id],
              component: <Change value={positionData[id]} withSign withRupee />,
            })}
          </div>
        );
      case COLUMN_TYPE.AVG_PRICE:
        return (
          <div key={id}>
            {resultDecider({
              value: positionData.display_pos_status === POSITIONS_STATUS.CLOSED
                ? undefined : positionData[id],
              component: positionData[id] && formatPrice(positionData[id]),
            })}
          </div>
        );
      case COLUMN_TYPE.CURRENT_PRICE:
        return (
          <div key={id}>
            {resultDecider({
              value: positionData[id],
              component: positionData[id] && formatPrice(positionData[id]),
            })}
          </div>
        );
      default:
        return (
          <div key={id}>
            {resultDecider({
              value: positionData[id],
              component: positionData[id],
            })}
          </div>
        );
    }
  };

  const getNetPrice = () => {
    if (positionData.display_pos_type === TRANSACTION_TYPES.BUY) return positionData.buy_avg;
    return positionData.sell_avg;
  };

  if (mobileBrowser()) {
    const getRowContent = () => (
      <>
        <div className={cx([styles.stockName, styles.stockExchange])}>
          <span>{display_name}</span>
          <span className={styles.exchange}>
            {exchange}
          </span>
        </div>

        <div className={styles.stockName}>
          <Change
            value={(isClosedPosition || activeSwitch === RETURNS_TYPE.OVERALL)
              ? positionData.overallProfit : positionData.profit}
            withSign
            withRupee
            className={styles.positionPrice}
          />
        </div>

        <div className={styles.tags}>
          <span className={styles.tag}>
            {display_product}
          </span>
          <span className={styles.qtyFraction}>
            {positionData.display_pos_status === POSITIONS_STATUS.CLOSED ? 'Qty 0 x 0.0' : `Qty ${positionData?.net_qty} x ${getNetPrice()}`}
          </span>
        </div>

        {isClosedPosition
          ? <div className={`${styles.closedTag} ${styles.displayClosedTag}`}>Closed</div>
          : (
            <div className={cx([styles.noWrap, styles.pTag])}>
              <span className={styles.qtyFraction}>LIVE</span>
              {` ${formatPrice(positionData.last_traded_price, 2)}`}
            </div>
          )}
      </>
    );
    return (
      <>
        <div
          role="presentation"
          onClick={handleMobileRowClick}
          className={cx([styles.tableRow, table.plainTableRow, styles.rowDivider], {
            [styles.marketDepthOpen]: isMarketDepthOpen,
          })}
        >
          {getRowContent()}
        </div>
        {(isMarketDepthOpen)
        && (
          <>
            <div className={styles.dummyRow} />
            <div
              style={{ top: `${6 + 5.75 * index}rem` }}
              className={styles.mobileMarketDepthContainer}
            >
              <MarketDepthContainer
                {...positionData}
                index={index}
                setOpenedMarketDepth={setOpenedMarketDepth}
              />
            </div>
          </>
        )}
        {
        shouldShowControls
        && (
          <>
            <div className={styles.dummyRow1} />
            <div className={styles.rowControls}>
              {renderActions(positionData)}
            </div>
          </>
        )
      }
      </>
    );
  }

  return (
    <>
      <div
        role="presentation"
        className={positionListStyles}
        onClick={onClick.bind(null, {
          type: CONTROLS.DETAILS,
          positionData: { ...positionData },
        })}
      >
        {
        rowData.map((columnOrder) => renderColumns(columnOrder))
      }
      </div>
      {(isMarketDepthOpen)
      && (
        <>
          <div className={styles.dummyRow} />
          <MarketDepthContainer
            {...positionData}
            top={`${4.6 + 4.5 * index}rem`}
            setOpenedMarketDepth={setOpenedMarketDepth}
          />
        </>
      )}
    </>
  );
}

Position.propTypes = {
  positionData: PropTypes.objectOf(Object).isRequired,
  onClick: PropTypes.func.isRequired,
};

export default Position;
