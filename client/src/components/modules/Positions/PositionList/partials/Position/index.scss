@import '../../../../../../styles/main';

$select-color: rgba(0, 67, 147, .1);
$tooltip-left: 14.5rem;

.positionWrapper {
  background-color: $default;

  @include box-shadow(0, 2px, 6px);
}

.positionsListWrapper {
  position: relative;
  background-color: $default;
}

.positionsList {
  cursor: pointer;

  &:hover {
    .name > div:first-child {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      max-width: 15rem;
    }

    .name > .closedTag {
      display: none;
    }
  }
}

.name {
  display: flex;
  justify-content: space-between;
  color: $grey1;
  position: relative;

  > div:nth-child(2) {
    > span:nth-child(2) {
      margin-left: .5rem;
      color: $grey3;

      @include typography(h8);
    }
  }
}

.tableRow {
  @include respond(phone) {
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1.2rem;
  }

  > * {
    min-width: 11rem;
    justify-content: flex-end;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;

    @include respond(phone) {
      width: auto;
      max-width: 60%;
      align-items: flex-end;
    }
  }

  > div:nth-child(2) {
    justify-content: flex-start;

    @include respond(phone) {
      justify-content: flex-end;
    }
  }
}

.closedTag {
  color: $yellow;
  display: none;
  margin-left: auto;

  @include typography(h7);
}

.displayClosedTag {
  display: initial;
}

.dummyRow {
  height: 27.5rem;
}

.dummyRow1 {
  height: 5rem;

  @include respond(phone) {
    height: auto;
  }
}

.tooltip {
  width: 32rem;
  display: flex;
  justify-content: space-between;
  left: $tooltip-left;

  &::after {
    left: calc(50% - #{$tooltip-left});
  }
}

.checkbox {
  padding-right: 1rem;
}

.emptySelect {
  width: 2.3rem;
}

.rowSelected {
  /* stylelint-disable-next-line declaration-no-important */
  background-color: $select-color !important;
}

.exchange {
  color: $grey3;
  padding: 0 .5rem;

  @include typography(h7);
}

.rowDivider {
  /* stylelint-disable-next-line declaration-no-important */
  background-color: $default !important;

  > div > *:first-child {
    padding-bottom: .5rem;

    @include respond(phone) {
      padding-bottom: 0;
    }
  }
}

.tag {
  display: flex;
  color: $grey3;
  justify-content: center;
  align-items: center;
  height: 2rem;
  padding: 0 1rem;
  border-radius: .24rem;
  border: solid .1rem $grey3;
  margin-right: 1rem;

  @include typography(h8);

  @include respond(phone) {
    padding: 0 .5rem;
    height: 1.8rem;
  }
}

.tags {
  display: flex;
  align-items: center;
}

.qtyFraction {
  color: $grey3;

  @include typography(h7);

  @include respond(phone) {
    @include typography(h7);
  }
}

.pTag {
  color: $grey3;

  @include typography(h7);

  @include respond(phone) {
    column-gap: .6rem;
  }
}

.noWrap {
  white-space: nowrap;
}

.rowControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $grey5;
  padding: .5rem 2rem;
  position: absolute;
  width: 100%;
  height: 5rem;

  @include respond(phone) {
    position: static;
    height: auto;
    padding: 1rem 2rem;
  }

  > div {
    display: flex;
    align-items: center;
    white-space: nowrap;
    gap: .5rem;

    > div:last-child {
      color: $grey0;

      @include typography(h8);
    }
  }

  button {
    @include respond(phone) {
      /* stylelint-disable-next-line declaration-no-important */
      background-color: $grey5 !important;
    }
  }
}

.mobileMarketDepthContainer {
  display: flex;
  justify-content: space-between;
  padding: 0 2.5rem 0 1.5rem;
  position: absolute;
  width: 100%;
  height: 27.5rem;
  background-color: $default;

  @include respond(phone) {
    padding: 0;
  }

  > div {
    position: static;
    height: auto;

    > div > div {
      width: auto;
    }
  }
}

.stockName {
  @include typography(h6, semibold);
}

.stockExchange {
  /* stylelint-disable-next-line declaration-no-important */
  display: block !important;
}

.positionPrice {
  @include respond(phone) {
    @include typography(h6, semibold);
  }
}
