import { MODAL_TYPES } from '@common/Modal/enums';
import {
  TRANSACTION_TYPES, SORTING_ORDER, PRODUCT_TYPES,
} from '@utils/enum';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { log } from '@layout/App/api';
import { roundValue, mobileBrowser } from '@utils';
import {
  POSITIONS_TAB, POSITIONS_STATUS, CONTROLS, ACTION_TYPE, INTEROPS_CONVERT_ORDER,
} from './enums';

const calculateProfitAndLoss = ({
  ltp,
  netQty,
  realisedProfit,
  pClose,
  tot_buy_qty_cf,
  tot_sell_qty_cf,
  buy_avg,
  sell_avg,
}) => {
  let unrealizedProfit = 0;
  if (tot_buy_qty_cf === 0 && tot_sell_qty_cf === 0) {
    if (netQty < 0) {
      unrealizedProfit += roundValue(ltp - sell_avg) * netQty;
    } else if (netQty > 0) {
      unrealizedProfit += roundValue(ltp - buy_avg) * netQty;
    }
  } else {
    unrealizedProfit += roundValue(ltp - pClose) * netQty;
  }
  return realisedProfit + unrealizedProfit;
};

const calculateOverallProfitAndLoss = ({
  ltp,
  netQty,
  realisedProfit,
  buy_avg,
  sell_avg,
}) => {
  let unrealizedProfit = 0;
  if (netQty !== 0) unrealizedProfit = roundValue(ltp - (netQty > 0 ? buy_avg : sell_avg)) * netQty;
  return realisedProfit + unrealizedProfit;
};

const getPercentageChange = ({
  netQty,
  ltp,
  avg_price,
  posStatus,
}) => {
  if (posStatus === POSITIONS_STATUS.CLOSED) {
    return null;
  }
  const change = ((ltp - avg_price) / avg_price) * 100;
  return netQty > 0 ? change : -1 * change;
};

const transformPositionData = (updatedPosition, initialData) => {
  if (updatedPosition?.ltp) {
    const { ltp, pClose } = updatedPosition;
    const {
      net_qty,
      display_pos_status,
      avg_price,
      realised_profit,
      instrument,
      tot_buy_qty_cf,
      tot_buy_qty_day,
      tot_sell_qty_day,
      tot_sell_qty_cf,
      buy_avg,
      sell_avg,
      product,
      cost_price,
      segment,
    } = initialData;
    return {
      ...initialData,
      last_traded_price: ltp.toFixed(2),
      profit: calculateProfitAndLoss({
        ltp,
        netQty: net_qty,
        avg_price,
        posStatus: display_pos_status,
        realisedProfit: realised_profit,
        instrument,
        tot_buy_qty_cf,
        tot_buy_qty_day,
        tot_sell_qty_day,
        tot_sell_qty_cf,
        buy_avg,
        sell_avg,
        product,
        pClose,
        segment,
      }),
      overallProfit: calculateOverallProfitAndLoss({
        ltp,
        netQty: net_qty,
        avg_price,
        posStatus: display_pos_status,
        realisedProfit: realised_profit,
        instrument,
        tot_buy_qty_cf,
        tot_buy_qty_day,
        tot_sell_qty_day,
        tot_sell_qty_cf,
        buy_avg,
        sell_avg,
        product,
        cost_price,
        pClose,
      }),
      change: getPercentageChange({
        netQty: net_qty,
        ltp,
        avg_price,
        posStatus: display_pos_status,
      }),
    };
  }
  return initialData;
};

const updateHeaderValues = (positionsStream, positionsData) => positionsStream
  ?.reduce((accumulatedResult, position, index) => {
    if (position && positionsData[index]) {
      const { ltp, pClose } = position;
      const currentPosition = positionsData[index];
      const {
        net_qty,
        display_pos_status,
        avg_price,
        realised_profit,
        tot_buy_val,
        tot_sell_val,
        instrument,
        tot_buy_qty_cf,
        tot_buy_qty_day,
        tot_sell_qty_day,
        tot_sell_qty_cf,
        buy_avg,
        sell_avg,
        product,
        cost_price,
        segment,
      } = currentPosition;
      return {
        PL: accumulatedResult.PL + calculateProfitAndLoss({
          ltp,
          netQty: net_qty,
          avg_price,
          posStatus: display_pos_status,
          realisedProfit: realised_profit,
          instrument,
          tot_buy_qty_cf,
          tot_buy_qty_day,
          tot_sell_qty_day,
          tot_sell_qty_cf,
          buy_avg,
          sell_avg,
          product,
          pClose,
          segment,
        }),
        overallPL: accumulatedResult.overallPL + calculateOverallProfitAndLoss({
          ltp,
          netQty: net_qty,
          avg_price,
          posStatus: display_pos_status,
          realisedProfit: realised_profit,
          instrument,
          tot_buy_qty_cf,
          tot_buy_qty_day,
          tot_sell_qty_day,
          tot_sell_qty_cf,
          buy_avg,
          sell_avg,
          product,
          cost_price,
          pClose,
        }),
        TV: accumulatedResult.TV + (tot_buy_val + tot_sell_val),
      };
    }
    return accumulatedResult;
  }, {
    PL: 0,
    overallPL: 0,
    TV: 0,
  });

const shapePositionData = (position) => {
  let avg_price;
  const {
    display_name,
    display_product,
    net_qty,
    net_avg,
    security_id,
    exchange,
    display_pos_status,
    product,
    tot_buy_qty,
    tot_buy_val,
    buy_avg,
    tot_sell_qty,
    tot_sell_val,
    sell_avg,
    display_pos_type,
    segment,
    realised_profit,
    isin,
    tick_size,
    lot_size,
    instrument,
    tot_buy_qty_cf,
    tot_sell_qty_cf,
    tot_sell_val_day,
    cost_price,
    tot_buy_val_day,
    ...keys
  } = position;

  if (net_qty > 0) {
    avg_price = buy_avg;
  } else {
    avg_price = sell_avg;
  }

  return {
    display_name,
    display_product,
    net_qty,
    net_avg,
    avg_price,
    security_id,
    exchange,
    display_pos_status,
    tot_buy_qty,
    tot_buy_val,
    buy_avg,
    tot_sell_qty,
    tot_sell_val,
    sell_avg,
    display_pos_type,
    product,
    segment,
    realised_profit,
    isin,
    tick_size,
    lot_size,
    instrument,
    tot_buy_qty_cf,
    tot_sell_qty_cf,
    tot_sell_val_day,
    cost_price,
    tot_buy_val_day,
    ...keys,
  };
};

const shapeInitialData = (data) => {
  const tabs = [{
    id: 'All',
    count: 0,
    label: POSITIONS_TAB.ALL,
  }, {
    id: 'Open',
    count: 0,
    label: POSITIONS_TAB.OPEN,
  }, {
    id: 'Closed',
    count: 0,
    label: POSITIONS_TAB.CLOSE,
  }];

  const positions = data.reduce((acc, position) => {
    let temp;
    const normalizedPosition = shapePositionData(position);
    if (position.display_pos_status === POSITIONS_STATUS.OPEN) {
      tabs[1].count += 1;
      temp = {
        ...acc,
        [POSITIONS_TAB.OPEN]: [
          ...acc[POSITIONS_TAB.OPEN],
          normalizedPosition,
        ],
      };
    } else {
      tabs[2].count += 1;
      temp = {
        ...acc,
        closed: acc.closed + 1,
        [POSITIONS_TAB.CLOSE]: [
          ...acc[POSITIONS_TAB.CLOSE],
          normalizedPosition,
        ],
      };
    }
    tabs[0].count += 1;
    return {
      ...temp,
      [POSITIONS_TAB.ALL]: [
        ...acc[POSITIONS_TAB.ALL],
        normalizedPosition,
      ],
    };
  }, {
    [POSITIONS_TAB.ALL]: [],
    [POSITIONS_TAB.OPEN]: [],
    [POSITIONS_TAB.CLOSE]: [],
  });
  return {
    ...positions,
    tabs,
  };
};

const actionControls = ({
  type, positionData, handleControls, activeTab,
  components: { PositionsModal, PlaceOrder, ConvertOrder },
  addToast, openModal, openDraggableModal, openedMarketDepth, setOpenedMarketDepth,
  handleExpansion, pmlId, closeModal,
}) => {
  const isMarketDepthOpen = openedMarketDepth === positionData.security_id;
  if (!positionData?.security_id) {
    log([{
      level: 'info',
      key: 'positions_security_id',
      timestamp: new Date().toISOString(),
      version: window.pmVersion,
      data: JSON.stringify({ actionType: type, positionData }),
    }]);
  }
  switch (type) {
    case CONTROLS.DETAILS: {
      if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
      openModal({
        Component: PositionsModal,
        componentProps: {
          data: positionData,
          handleControls,
        },
      });
      break;
    }
    case CONTROLS.ADD:
    case CONTROLS.EXIT: {
      const {
        display_name,
        net_qty,
        security_id,
        exchange,
        product,
        isin,
        segment,
        tick_size,
        lot_size,
        instrument,
        scrip_ids,
        instrument_type,
      } = positionData;
      if (product === PRODUCT_TYPES.BRACKET_ORDER) {
        addToast('Exit Bracket Order from the orders section', APPEARANCE_TYPES.INFO);
        break;
      }
      if (product === PRODUCT_TYPES.COVER_ORDER) {
        addToast('Exit Cover Order from the orders section', APPEARANCE_TYPES.INFO);
        break;
      }
      let siblingSecurityId = null;
      let siblingTickSize = null;
      if (scrip_ids && scrip_ids.length > 1) {
        scrip_ids.forEach((scrip) => {
          if (scrip?.exchange !== exchange) {
            siblingSecurityId = Number(scrip?.security_id);
            siblingTickSize = scrip?.tick_size;
          }
        });
      }
      let componentProps = {
        name: display_name,
        exchange,
        securityId: parseInt(security_id, 10),
        segment,
        transactionType: net_qty > 0 ? TRANSACTION_TYPES.BUY : TRANSACTION_TYPES.SELL,
        productType: product,
        isin,
        tickSize: tick_size / 100,
        lotSize: lot_size,
        instrumentType: instrument,
        forceExchangeSelect: !!(scrip_ids && scrip_ids.length > 1),
        siblingSecurityId,
        siblingTickSize: siblingTickSize / 100,
        instrument_type,
        activeTab,
        fromPositions: product === PRODUCT_TYPES.MTF,
      };
      if (type === CONTROLS.EXIT) {
        componentProps = {
          ...componentProps,
          posControlType: type,
          showPnL: true,
          netQty: net_qty,
          transactionType: net_qty > 0 ? TRANSACTION_TYPES.SELL : TRANSACTION_TYPES.BUY,
          initialQuantity: Math.abs(net_qty),
        };
      }
      if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
      if (mobileBrowser()) {
        openModal({
          type: MODAL_TYPES.BOTTOM_SHEET,
          Component: PlaceOrder,
          componentProps,
        });
      } else {
        openDraggableModal({
          type: MODAL_TYPES.POPUP,
          Component: PlaceOrder,
          componentProps,
        });
      }
      break;
    }
    case CONTROLS.CONVERT: {
      const {
        display_name, exchange, security_id, last_traded_price, change, net_qty, product, display_pos_type,
        segment, lot_size, isin, scrip_ids, instrument_type, avg_price,
      } = positionData;
      if (scrip_ids) {
        addToast(INTEROPS_CONVERT_ORDER, APPEARANCE_TYPES.INFO);
        break;
      }
      if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
      openModal({
        type: mobileBrowser() ? MODAL_TYPES.BOTTOM_SHEET : MODAL_TYPES.POPUP,
        Component: ConvertOrder,
        componentProps: {
          isin,
          name: display_name,
          exchange,
          securityId: parseInt(security_id, 10),
          lotSize: lot_size,
          instrument_type,
          avg_price,
          data: {
            display_name,
            exchange,
            last_traded_price,
            change,
            product,
            net_qty,
            security_id,
            details: { display_pos_type, segment },
          },
        },
      });
      break;
    }
    case CONTROLS.WATCHLIST: {
      handleExpansion(pmlId, mobileBrowser());
      closeModal();
      break;
    }
    default:
      break;
  }
};

const secondarySort = (a, b) => {
  const a_avgPrice = a.net_qty > 0 ? a.buy_avg : a.sell_avg;
  const b_avgPrice = b.net_qty > 0 ? b.buy_avg : b.sell_avg;

  if (a.product > b.product) {
    return 1;
  } if (a.product < b.product) {
    return -1;
  }
  if (a.realised_profit > b.realised_profit) {
    return 1;
  } if (a.realised_profit < b.realised_profit) {
    return -1;
  }
  if (a.net_qty > b.net_qty) {
    return 1;
  } if (a.net_qty < b.net_qty) {
    return -1;
  }
  if (a.display_name > b.display_name) {
    return 1;
  } if (a.display_name < b.display_name) {
    return -1;
  }
  if ((a_avgPrice * a.net_qty) > (b_avgPrice * b.net_qty)) {
    return 1;
  } if ((a_avgPrice * a.net_qty) < (b_avgPrice * b.net_qty)) {
    return -1;
  } if (a.display_name > b.display_name) {
    return 1;
  } if (a.display_name < b.display_name) {
    return -1;
  }
  return 1;
};

const primarySort = ({
  activeSort, data,
}) => {
  if (Object.keys(activeSort).length) {
    const sortElement = activeSort.id;
    const sortedList = data.sort((a, b) => {
      if (a[sortElement] > b[sortElement]) {
        return 1;
      }
      if (a[sortElement] === b[sortElement]) {
        return secondarySort(a, b);
      }
      return -1;
    });
    if (activeSort.sortingOrder === SORTING_ORDER.DESCENDING) {
      return sortedList.reverse();
    }
    return sortedList;
  }
  return data;
};

const resultDecider = ({ value, component }) => {
  if (value || value === 0) {
    return component;
  }
  return '-';
};

const positionActions = ({ status, positionType, showWatchlist }) => {
  const isPositionOpen = status === POSITIONS_STATUS.OPEN;
  switch (positionType) {
    case PRODUCT_TYPES.DELIVERY:
    case PRODUCT_TYPES.MARGIN:
    case PRODUCT_TYPES.INTRADAY:
      if (isPositionOpen) {
        if (showWatchlist) return [ACTION_TYPE.ADD, ACTION_TYPE.EXIT, ACTION_TYPE.CONVERT, ACTION_TYPE.WATCHLIST];
        return [ACTION_TYPE.ADD, ACTION_TYPE.EXIT, ACTION_TYPE.CONVERT];
      } if (showWatchlist) return [ACTION_TYPE.ADD, ACTION_TYPE.WATCHLIST];
      return [ACTION_TYPE.ADD];
    case PRODUCT_TYPES.COVER_ORDER:
    case PRODUCT_TYPES.BRACKET_ORDER:
      if (isPositionOpen) {
        if (showWatchlist) return [ACTION_TYPE.EXIT, ACTION_TYPE.WATCHLIST];
        return [ACTION_TYPE.EXIT];
      } if (showWatchlist) return [ACTION_TYPE.WATCHLIST];
      return [];
    case PRODUCT_TYPES.MTF:
      if (isPositionOpen) {
        if (showWatchlist) return [ACTION_TYPE.WATCHLIST];
        return [ACTION_TYPE.EXIT, ACTION_TYPE.ADD];
      } return [ACTION_TYPE.ADD];
    default:
      return [];
  }
};

const hasOvernightPosition = (positions) => !!(positions.find(({
  tot_buy_qty_cf, tot_sell_qty_cf,
}) => (tot_buy_qty_cf || tot_sell_qty_cf)));

export {
  calculateProfitAndLoss,
  calculateOverallProfitAndLoss,
  transformPositionData,
  updateHeaderValues,
  shapeInitialData,
  actionControls,
  resultDecider,
  primarySort,
  positionActions,
  hasOvernightPosition,
};
