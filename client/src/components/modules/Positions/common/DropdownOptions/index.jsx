import React, {
  useEffect, useState, useCallback, useContext,
} from 'react';
import { useHistory } from 'react-router-dom';
import Popper from '@common/Popper';
import Icon from '@common/Icon';
import { classNames as cx } from '@utils/style';
import { MODAL_TYPES } from '@common/Modal/enums';
import ShareModal from '@common/ShareModal';
import { useModal } from '@layout/App/ModalContext';
import { DEFAULT_TICK } from '@modules/OrderFlow/useBasicFields';
import { fetchPmlId } from '@modules/Positions/positionsApi';
import GttAddEditModal from '@modules/GTT/GttAddEditModal';
import useGetApi from '@common/UseApi/useGetApi';
import { ROUTE_NAME, INSTRUMENTS, PRODUCT_TYPES } from '@utils/enum';
import { DEEP_LINKS } from '@pages/Company/useSecurity';
import { ICON_NAME } from '@common/Icon/enums';
import { getExpiryDate } from '@modules/GTT/api';
import LocalStorage from '@service/LocalStorage';
import BasketOrderDetails from '@modules/BasketOrderDetails';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { sendEvent } from '@service/AnalyticsService';
import { mobileBrowser } from '@utils';
import { EQUITY_BASE_URL } from '@config/ENUMS';
import Routes from '../../../../../routes';
import styles from './index.scss';

const INSTRUMENT = 'EQUITY';

function DropdownOptions({
  options,
  setOpenedMarketDepth,
  isMarketDepthOpen,
  security_id,
  exchange,
  segment,
  index,
  txn_type,
  price,
  product,
  order_type,
  trigger_price,
  pmlId,
  iconSize,
  instrumentType,
  popperClassName,
  menuClassName,
  lot_size,
  isin,
  tick_size,
  displayName,
  quantity,
  sl_abstick_value,
  pr_abstick_value,
  transaction_type = null,
  handleSelectOption = () => {},
}) {
  const { makeRequest } = useGetApi();
  const history = useHistory();
  const { openModal, closeModal } = useModal();
  const getRouteUrl = () => (instrumentType && instrumentType !== INSTRUMENTS.STOCK && instrumentType !== INSTRUMENT
    ? Routes[ROUTE_NAME[instrumentType.toUpperCase()]].url
    : Routes[ROUTE_NAME.COMPANY_PAGE].url);
  const [expiryDate, setExpiryDate] = useState();
  const [action, setAction] = useState(null);
  const { handleExpansion } = useContext(WatchlistContext);
  const getProps = useCallback(
    (id) => ({
      exchange,
      id,
      pml_id: id,
      lot_size,
      tick: (tick_size / 100) || DEFAULT_TICK,
      instrument_type: instrumentType,
      name: displayName,
      segment,
      isin,
      security_id,
      expiry_date: expiryDate,
      transaction_type,
    }),
    [exchange, expiryDate, instrumentType, isin, lot_size, displayName, security_id,
      segment, tick_size, transaction_type],
  );

  useEffect(() => {
    if (action === DEEP_LINKS.GTT) {
      getExpiryDate(pmlId).then(
        ({ data: { expiry_date } = {} }) => setExpiryDate(expiry_date),
      ).catch(() => {});
    }
  }, [action, pmlId]);

  const replaceUrl = ({ replace, url }) => replace(url);

  const isComapnyPageRoute = () => (window.location.pathname.includes(
    Routes[ROUTE_NAME.COMPANY_PAGE].url,
  ) || window.location.pathname.includes(
    Routes[ROUTE_NAME[instrumentType]].url,
  ));

  const isPositionRoute = () => (window.location.pathname.includes(
    Routes[ROUTE_NAME.POSITIONS].url,
  ));

  const navigateToTargetPage = (eventAction, pml_id) => {
    if (eventAction === 'viewAllGtt') {
      history.push(
        Routes[ROUTE_NAME.GTT].url,
      );
    } else if (eventAction === 'createGtt') {
      if (isPositionRoute()) {
        sendEvent({
          event_category: 'gtt',
          event_action: 'gtt_ordersview_postorderplacement',
          event_label: 'gtt',
          vertical_name: 'stocks',
          screenName: '/gtt',
        });
      }
      openModal({
        Component: GttAddEditModal,
        componentProps: getProps(pml_id),
      });
    } else if (eventAction === 'createSip') {
      const url = `${getRouteUrl()}/${pml_id}?action=${DEEP_LINKS.SIP}`;
      replaceUrl({ replace: (isComapnyPageRoute() ? history.replace : history.push), url });
    } else if (eventAction === 'createPriceAlert') {
      const url = `${getRouteUrl()}/${pml_id}?action=${DEEP_LINKS.PRICE_ALERT}`;
      replaceUrl({ replace: (isComapnyPageRoute() ? history.replace : history.push), url });
    } else if (eventAction === 'scripDetails') {
      history.push(
        `${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${pml_id}`,
      );
    } else if (eventAction === 'chart') {
      history.push(
        `${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${
          pml_id
        }?toggleDetails=true&fullScreen=true`,
      );
    } else if (eventAction === 'share') {
      const shareChartSubject = 'Share Orders';
          // eslint-disable-line
      const profit_value = Number(pr_abstick_value);
      const stoploss_value = Number(sl_abstick_value);
      let deeplinkParams = '';
      if (product === PRODUCT_TYPES.BRACKET_ORDER) {
        if (trigger_price === 0) {
          deeplinkParams = `stoploss_value=${stoploss_value}&profit_value=${profit_value}`;
        } else deeplinkParams = `trigger_price=${trigger_price}&stoploss_value=${stoploss_value}&profit_value=${profit_value}`;
      } else deeplinkParams = `trigger_price=${trigger_price}`;
      const shareUrl = encodeURIComponent(`${window.location.hostname}${EQUITY_BASE_URL}${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${pml_id}?action=place-order&txn_type=${txn_type}&price=${price}&product=${product}&order_type=${order_type}&exchange=${exchange}&security_id=${security_id}&quantity=${quantity}&${deeplinkParams}`);

      openModal({
        Component: ShareModal,
        componentProps: { shareUrl, shareChartSubject, closeModal },
        type: MODAL_TYPES.LARGE_POPUP,
        disableClose: true,
      });
    } else if (eventAction === 'createBasket') {
      const props = getProps(pml_id);
      const basketIntroVisited = 'basketIntroVisited';
      const modalType = LocalStorage.get(basketIntroVisited, false) ? MODAL_TYPES.POPUP : MODAL_TYPES.SIDEPANE;
      openModal({
        Component: BasketOrderDetails,
        componentProps: { props, closeModal },
        type: modalType,
        disableClose: true,
      });
    } else if (eventAction === 'marginPledge') {
      history.push(
        Routes[ROUTE_NAME.MARGIN_PLEDGE].url,
      );
    } else if (eventAction === 'watchlist') {
      handleExpansion(pml_id, mobileBrowser());
    }
  };

  const openScripDetails = async (eventAction) => {
    if (eventAction === 'marketDepth') {
      setOpenedMarketDepth(isMarketDepthOpen ? -1 : index);
    } else if (pmlId) {
      navigateToTargetPage(eventAction, pmlId);
    } else {
      try {
        const {
          data: { results },
        } = await makeRequest(fetchPmlId(security_id, exchange, segment));
        navigateToTargetPage(eventAction, results[0].id);
      } catch (err) {}
    }
  };
  return (
    <div className={styles.mainMenuContainer} role="presentation" onClick={(e) => e.stopPropagation()}>
      <Popper
        showDropDownIcon={false}
        className={cx(styles.popper, {
          [popperClassName]: popperClassName,
        })}
        iconName={ICON_NAME.MORE_OPTION}
        activeIconName={ICON_NAME.MORE_OPTION}
        iconSize={iconSize || 4.2}
        iconStyles={styles.popperIcon}
      >
        <div className={styles.mainMenu}>
          <Icon name={ICON_NAME.DROP_DOWN} className={styles.selectorArrow} />
          {options.map(({
            id, name, iconName, iconSize: size,
          }, i) => (
            <div
              key={i}
              className={cx(styles.menuOptions, {
                [menuClassName]: menuClassName,
              })}
              role="presentation"
              tabIndex={i}
              onClick={(e) => {
                e.stopPropagation();
                openScripDetails(id);
                setAction(id);
                handleSelectOption(id);
              }}
            >
              <Icon name={iconName} size={size || 2.4} className={styles.iconWrapper} />
              <span>{name}</span>
            </div>
          ))}
        </div>
      </Popper>
    </div>
  );
}

export default DropdownOptions;
