import React, {
  useContext, useMemo, useEffect, useState, useCallback,
} from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';

import { ROUTE_NAME } from '@utils/enum';
import { UserContext } from '@layout/App/UserContext';
import { classNames as cx } from '@utils/style';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import GAElement from '@common/GAElement';
import { HOME_PAGE_GA } from '@constants/ga-events';
import Popper from '@common/Popper';
import { OrderFeedContext } from '@layout/App/OrderFeedContext';
import { REQUEST_TYPES, RESPONSE_TYPES } from '@service/dataConfig';
import If from '@common/If';
import { useModal } from '@layout/App/ModalContext';
import { MODAL_TYPES } from '@common/Modal/enums';
import SonarEmitter from '@common/SonarEmitter';
import LocalStorage from '@service/LocalStorage';
import { mobileBrowser } from '@utils';
import styles from './index.scss';
import STOCK_NOTCH from './config';
import ROUTES, {
  MAIN_MENU_OPTIONS,
  IPO_ROUTES,
  IPO,
  STOCKS,
  SGB_ROUTES,
  TRADIND_IDEAS_LISTING_ROUTE,
} from './enums';
import PinnedIndex from './PinnedIndex';
import Routes from '../../../routes';
import InstrumentSwitch from './InstrumentSwitch';
import { SWITCH_CONSTANTS, SWITCH_OPTIONS } from './InstrumentSwitch/enums';
import CallUs from './CallUs';

const orderResponseTypes = [RESPONSE_TYPES.ORDER];
const isMobile = mobileBrowser();

const TradingViewNudge = () => (
  <div className={styles.modalWrapper}>
    <SonarEmitter
      iconName={ICON_NAME.MAIN_MENU_ICON_ACTIVE}
      iconClassName={styles.investmentIcon}
      wrapperClassName={styles.sonarWrapper}
      iconSize={2}
    />
    <div className={styles.dash}>{STOCK_NOTCH.DASH}</div>
    <div className={styles.information}>
      <div className={styles.heading}>{STOCK_NOTCH.HEADING}</div>
      <div className={styles.subHeading}>
        {STOCK_NOTCH.SUBHEADING}
      </div>
    </div>
  </div>
);

function Header({ isIPO = false, isSGB = false }) {
  const location = useLocation();
  const history = useHistory();
  const {
    displayName,
    displayImage,
    isInvestmentReadyFO,
  } = useContext(UserContext);
  const isRouteTpin = useMemo(() => Routes[ROUTE_NAME.TPIN_AUTH].url === location.pathname, [location.pathname]);
  const [newOrderBroadcast, setNewOrderBroadcast] = useState(false);
  const { orderFeed } = useContext(OrderFeedContext);
  const [selectedInstrument, setInstrument] = useState(SWITCH_OPTIONS[0]);
  const [moreOptionsToggle, updateMoreOptionsToggle] = useState(false);
  const { openModal } = useModal();
  const tradingChartIntroVisited = LocalStorage.get(STOCK_NOTCH.STOCK_NOTCH_INTRO, false);

  useEffect(() => {
    if (!tradingChartIntroVisited && !isMobile) {
      LocalStorage.set(STOCK_NOTCH.STOCK_NOTCH_INTRO, true, '', false);
      openModal({
        type: MODAL_TYPES.POPUP,
        Component: TradingViewNudge,
        componentProps: {},
        disableClose: true,
        noAnimationOnOut: true,
        modalContainerStyles: styles.modalTradingChart,
      });
    }
  }, [openModal, tradingChartIntroVisited]);

  const isOrdersLinkActive = useCallback(() => {
    const { link } = ROUTES.find((route) => route.name.toLowerCase() === ROUTE_NAME.ORDERS.toLowerCase());
    let isActive = false;
    if (Array.isArray(link)) {
      isActive = link.includes(location.pathname);
    }

    return isActive;
  }, [location.pathname]);

  const handleInstrumentChange = (instrument) => {
    history.push(
      instrument === SWITCH_CONSTANTS.EQUITY ? Routes[ROUTE_NAME.HOME].url : Routes[ROUTE_NAME.FNO_DASHBOARD].url,
    );
  };

  const getRouteState = (route) => {
    const { link, defaultLink } = route;
    let isActive = false;
    let routeLink;
    if (Array.isArray(link)) {
      isActive = (link.includes(location.pathname));
      routeLink = defaultLink;
    } else {
      isActive = link === location.pathname;
      routeLink = link;
    }
    return { isActive, routeLink };
  };

  useEffect(() => {
    const orderStatusFeed = orderFeed?.getStream(REQUEST_TYPES.ORDER, null, orderResponseTypes)[0].feed;
    const subscription = orderStatusFeed?.subscribe(() => {
      if (!isOrdersLinkActive()) {
        setNewOrderBroadcast(true);
      }
    });
    return () => subscription?.unsubscribe();
  }, [orderFeed, setNewOrderBroadcast, isOrdersLinkActive]);

  useEffect(() => {
    if (isOrdersLinkActive()) {
      setNewOrderBroadcast(false);
    }
  }, [isOrdersLinkActive, setNewOrderBroadcast]);

  useEffect(() => {
    if (location.pathname.includes(Routes[ROUTE_NAME.FNO].url)) {
      setInstrument(SWITCH_OPTIONS[1]);
    } else if (location.pathname === Routes[ROUTE_NAME.HOME].url) {
      setInstrument(SWITCH_OPTIONS[0]);
    }
  }, [location.pathname]);

  const routesForSgbOrIpo = () => {
    if (isIPO) return IPO_ROUTES;

    if (isSGB) return SGB_ROUTES;

    return ROUTES;
  };
  const linkData = [{
    to: IPO.link,
    test: !isIPO,
    name: IPO.name,
    iconName: IPO.iconName,
  }, {
    to: STOCKS.link,
    test: isIPO || isSGB,
    name: STOCKS.name,
    iconName: STOCKS.iconName,
  }];
  const clickhandler = () => {
    window.location.href = `${window.location.origin}/dashboard`;
  };
  const stickyHeader = location.pathname.includes(TRADIND_IDEAS_LISTING_ROUTE);
  return (
    <div className={cx(styles.header, {
      [styles.stickyHeader]: stickyHeader,
    })}
    >
      <If test={!isMobile}>
        <div className={styles.equityContainer}>
          <div role="presentation" onClick={clickhandler} className={styles.logo}>
            <div className={styles.pmlLogo} />
          </div>
          <If test={!isIPO && !isRouteTpin && !isSGB && !isMobile}>
            <PinnedIndex />
          </If>
          <If test={!isIPO && !isRouteTpin && !isSGB && !isMobile && isInvestmentReadyFO}>
            <InstrumentSwitch
              selectedInstrument={selectedInstrument}
              handleInstrumentChange={handleInstrumentChange}
            />
          </If>
        </div>
        <div className={styles.routesContainer}>
          <div>
            <If test={!isRouteTpin}>
              {
                routesForSgbOrIpo().map((route) => {
                  const {
                    name, link, fnoLink, defaultLink, defaultFnoLink,
                  } = route;
                  const isFnoLink = fnoLink && (selectedInstrument.id === SWITCH_CONSTANTS.FUTURE_AND_OPTIONS);
                  const { isActive, routeLink } = getRouteState(
                    isFnoLink ? { link: fnoLink, defaultLink: defaultFnoLink } : { link, defaultLink },
                  );
                  const routeClass = cx(styles.route, {
                    [styles.activeRoute]: isActive,
                    [styles.highlightOrderTab]:
                      name.toLowerCase() === ROUTE_NAME.ORDERS.toLowerCase()
                      && newOrderBroadcast,
                  });
                  return (
                    <Link
                      key={name}
                      to={routeLink}
                      className={routeClass}
                    >
                      {name === 'Funds'
                        ? (
                          <GAElement
                            gaEventCategory={HOME_PAGE_GA.HOME_TOP_LINK_FUNDS_CLICKED.EVENT_CATEGORY}
                            gaEventAction={HOME_PAGE_GA.HOME_TOP_LINK_FUNDS_CLICKED.EVENT_ACTION}
                            eventName={HOME_PAGE_GA.HOME_TOP_LINK_FUNDS_CLICKED.EVENT_NAME}
                            vertical_name={HOME_PAGE_GA.HOME_TOP_LINK_FUNDS_CLICKED.VERITCAL_NAME}
                            screenName={HOME_PAGE_GA.HOME_TOP_LINK_FUNDS_CLICKED.SCREEN_NAME}
                          >
                            {name}
                          </GAElement>
                        ) : (
                          <GAElement
                            gaEventCategory={HOME_PAGE_GA.HOME_TOP_LINK_CLICKED.EVENT_CATEGORY}
                            gaEventAction={HOME_PAGE_GA.HOME_TOP_LINK_CLICKED.EVENT_ACTION}
                            gaEventLabel={HOME_PAGE_GA.HOME_TOP_LINK_CLICKED.EVENT_LABEL(name.toLowerCase())}
                          >
                            {name}
                          </GAElement>
                        )}
                    </Link>
                  );
                })
              }
            </If>
          </div>
          <CallUs />
          <div className={styles.divider} />
          <Link to={Routes[ROUTE_NAME.PROFILE].url} className={styles.profileLink}>
            <div className={styles.profile}>
              <span className={styles.userName}>{`${displayName ? displayName.trim().split(' ')[0] : ''}`}</span>
              <img src={displayImage} alt="displayImage" />
            </div>
          </Link>
          <div className={cx(styles.mainMenuContainer, { [styles.active]: moreOptionsToggle })}>
            <Popper
              showDropDownIcon={false}
              className={styles.popper}
              titleClassName={styles.moreTitle}
              iconName={ICON_NAME.MAIN_MENU_ICON}
              iconSize={2}
              title="More"
              containerStyles={styles.popperContainer}
              onToggle={(status) => updateMoreOptionsToggle(status)}
              handleClose={() => updateMoreOptionsToggle(false)}
            >
              <div className={styles.mainMenu}>
                <Icon
                  name={ICON_NAME.DROP_DOWN}
                  className={styles.selectorArrow}
                />
                {MAIN_MENU_OPTIONS.map(({ name, iconName, link }, index) => (
                  <a key={index} className={styles.menuOptions} href={link} rel="noopener noreferrer">
                    <Icon name={iconName} size={4} className={styles.iconWrapper} />
                    <span>{name}</span>
                  </a>
                ))}
                {
                  linkData.map((data) => (
                    <If test={data.test}>
                      <Link to={data.to} className={styles.menuOptions}>
                        <Icon name={data.iconName} size={4} className={styles.iconWrapper} />
                        <span>{data.name}</span>
                      </Link>
                    </If>
                  ))
                }
              </div>
            </Popper>
          </div>
        </div>
      </If>
      <If test={isMobile}>
        <div className={styles.mobileContainer}>
          <Icon name={ICON_NAME.PAYTM_MONEY_LOGO_SMALL} width={75} onIconClick={clickhandler} />
        </div>
        <div className={styles.profileQuickAccessWrapper}>
          <Link to={Routes[ROUTE_NAME.PROFILE].url} className={styles.profileLink}>
            <div className={styles.profile}>
              <div className={styles.user}>
                <div className={styles.userName}>Hello</div>
                <div className={styles.displayName}>{displayName}</div>
              </div>
              <img src={displayImage} alt="displayImage" />
            </div>
          </Link>
          <div className={cx(styles.mainMenuContainer, { [styles.active]: moreOptionsToggle })}>
            <Popper
              showDropDownIcon={false}
              className={styles.popper}
              titleClassName={styles.moreTitle}
              iconName={ICON_NAME.MAIN_MENU_ICON}
              iconSize={2}
              title=""
              containerStyles={styles.popperContainer}
              onToggle={(status) => updateMoreOptionsToggle(status)}
              handleClose={() => updateMoreOptionsToggle(false)}
            >
              <div className={styles.mainMenu}>
                <Icon
                  name={ICON_NAME.DROP_DOWN}
                  className={styles.selectorArrow}
                />
                {MAIN_MENU_OPTIONS.map(({ name, iconName, link }, index) => (
                  <a key={index} className={styles.menuOptions} href={link} rel="noopener noreferrer">
                    <Icon name={iconName} size={4} className={styles.iconWrapper} />
                    <span className={styles.optionsName}>{name}</span>
                  </a>
                ))}
                {
                  linkData.map((data) => (
                    <If test={data.test}>
                      <Link to={data.to} className={styles.menuOptions}>
                        <Icon name={data.iconName} size={4} className={styles.iconWrapper} />
                        <span>{data.name}</span>
                      </Link>
                    </If>
                  ))
                }
              </div>
            </Popper>
          </div>
        </div>
      </If>
    </div>
  );
}

export default Header;

