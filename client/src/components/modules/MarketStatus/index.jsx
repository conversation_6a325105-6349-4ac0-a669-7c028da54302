import React, {
  createContext, useState, useEffect, useContext, useCallback,
  useRef,
} from 'react';
import { Subject } from 'rxjs';
import { throttleTime } from 'rxjs/operators';
import { useToast } from '@common/Toast';
import { useCallbackForEvents } from '@utils/react';
import { DataFeedContext } from '@layout/App/DataFeedContext';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { log } from '@layout/App/api';
import {
  SEGMENT_TYPES, EXCHANGE, PRODUCT_TYPES, ORDER_TYPES,
} from '@utils/enum';
import { LoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { isEmpty } from 'lodash';
import { getTimeToEvent, getFeedByReqType } from '@utils';
import { REQUEST_TYPES, RESPONSE_TYPES } from '@service/';
import { getMarketStatus } from './market-status-api';
import CONFIG from '../../../config';

const requiredFeedResponse = [RESPONSE_TYPES.MARKET_STATUS];

const fallbackConfig = {
  [SEGMENT_TYPES.CASH]: {
    [EXCHANGE.NSE]: {
      exchange: EXCHANGE.NSE,
      segment: 'E',
      status: 1,
      message: '',
      amo: false,
      [PRODUCT_TYPES.DELIVERY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [
        ORDER_TYPES.LMT,
      ],
    },
    [EXCHANGE.BSE]: {
      exchange: EXCHANGE.BSE,
      segment: 'E',
      status: 1,
      message: '',
      amo: false,
      [PRODUCT_TYPES.DELIVERY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [
        ORDER_TYPES.LMT,
      ],
    },
  },
  [SEGMENT_TYPES.DERIVATIVES]: {
    [EXCHANGE.NSE]: {
      exchange: EXCHANGE.NSE,
      segment: 'D',
      status: 1,
      message: '',
      [PRODUCT_TYPES.MARGIN]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.LMT,
      ],
    },
    [EXCHANGE.BSE]: {
      exchange: EXCHANGE.BSE,
      segment: 'D',
      status: 1,
      message: '',
      [PRODUCT_TYPES.MARGIN]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.INTRADAY]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.SLM,
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.BRACKET_ORDER]: [
        ORDER_TYPES.LMT,
        ORDER_TYPES.SL,
      ],
      [PRODUCT_TYPES.COVER_ORDER]: [
        ORDER_TYPES.MKT,
        ORDER_TYPES.LMT,
      ],
    },
  },
};

const marketCallTimings = [
  '09:00:05',
  '09:08:05',
  '09:15:05',
  '15:30:05',
  '15:40:05',
  '16:00:05',
];

const logTimings = [
  '09:15:05',
  '16:00:05',
];

const timers = [];

export const MarketStatusContext = createContext({
  getMsData: () => null,
  marketStatus: fallbackConfig,
});

export const marketStatusErrorSubject = new Subject();

function MarketStatus({ children }) {
  const [marketStatus, setMarketStatus] = useState(fallbackConfig);
  const { dataFeed } = useContext(DataFeedContext);
  const { currDateTime } = useContext(LoggedInContext);
  const { addToast } = useToast();
  const lastCallTimeStamp = useRef(null);
  const broadcastDataOriginal = useRef([]);
  const [mktExchangeStatusConfig, setMktExchangeStatusConfig] = useState({});
  const packetsReceived = useRef([]);
  const missedPackets = useRef({});
  const incorrectPackets = useRef({});

  const fetchMarketStatus = useCallbackForEvents((message) => {
    let count = 0;
    async function getMarketStatusFromAPI() {
      try {
        const marketData = await getMarketStatus();
        const config = {};
        marketData.forEach((exchangeConfig) => {
          if (!config[exchangeConfig.segment]) config[exchangeConfig.segment] = {};
          if (exchangeConfig[PRODUCT_TYPES.BRACKET_ORDER]) {
            // eslint-disable-next-line no-param-reassign
            exchangeConfig[PRODUCT_TYPES.BRACKET_ORDER] = exchangeConfig.B.filter(
              (orderType) => orderType !== ORDER_TYPES.MKT && orderType !== ORDER_TYPES.SLM,
            );
          }

          config[exchangeConfig.segment][exchangeConfig.exchange] = {
            ...exchangeConfig,
            amo: exchangeConfig.status === 2,
          };
        });
        setMarketStatus(config);
        lastCallTimeStamp.current = new Date().getTime();
        if (message) addToast(message, APPEARANCE_TYPES.INFO);
      } catch (err) {
        count += 1;
        if (count < 10) {
          getMarketStatusFromAPI();
        }
      }
    }
    getMarketStatusFromAPI();
  }, []);

  useEffect(() => {
    const subscription = getFeedByReqType(
      dataFeed?.getStream(REQUEST_TYPES.MARKET_STATUS, null, requiredFeedResponse) || [],
      RESPONSE_TYPES.MARKET_STATUS,
    ).pipe(throttleTime(1000)).subscribe((data) => {
      if (data?.marketType === '1') {
        fetchMarketStatus();
        broadcastDataOriginal.current.push(data);
      }
    });
    return () => subscription.unsubscribe();
  }, [dataFeed, fetchMarketStatus]);

  useEffect(() => {
    if (currDateTime) {
      marketCallTimings.forEach((time) => {
        const { mktTime, timeToEvent } = getTimeToEvent(currDateTime, time, true);

        if (timeToEvent > 0) {
          timers.push(setTimeout(() => {
            const currentMktTime = mktTime;
            const mktConfigLength = mktExchangeStatusConfig[currentMktTime]?.length;
            mktExchangeStatusConfig[currentMktTime].forEach((pkt) => {
              broadcastDataOriginal.current.forEach((item) => {
                if (pkt.exgSegment === item.exchangeId) {
                  packetsReceived.current.push(item);
                }
                if (pkt.exgSegment === item.exchangeId && pkt.status !== item.marketStatus) {
                  if (incorrectPackets.current[currentMktTime]) {
                    incorrectPackets.current[currentMktTime].push({ marketStatus: item.marketStatus, ...pkt });
                  } else {
                    incorrectPackets.current[currentMktTime] = [{ marketStatus: item.marketStatus, ...pkt }];
                  }
                }
              });
            });
            broadcastDataOriginal.current.length = 0;
            if (mktConfigLength !== packetsReceived.current.length) {
              // if any packet missing
              const missingPkt = mktExchangeStatusConfig[currentMktTime]?.filter((localPkt) => {
                let matched = false;
                packetsReceived.current.forEach((pkt) => {
                  matched = matched || localPkt.exgSegment === pkt.exchangeId;
                });
                return !matched;
              });
              if (!isEmpty(missingPkt)) {
                missedPackets.current[time] = missingPkt;
              }
            }
            if (logTimings.includes(time) && (!isEmpty(missedPackets.current) || !isEmpty(incorrectPackets.current))) {
              const logTimeout = Math.random() * 10;
              setTimeout(() => {
                log([{
                  level: 'error',
                  key: 'marketstatus-packets-web',
                  timestamp: new Date().toISOString(),
                  version: window.pmVersion,
                  data: JSON.stringify({
                    'marketstatus-packets-web-incorrect': incorrectPackets.current,
                    'marketstatus-packets-web-missed': missedPackets.current,
                    url: window.location.href,
                  }),
                }]);
                missedPackets.current = {};
                incorrectPackets.current = {};
              }, logTimeout * 60 * 1000);
            }
            packetsReceived.current.length = 0;
            if (!lastCallTimeStamp.current || (new Date().getTime() - lastCallTimeStamp.current > 10000)) {
              fetchMarketStatus();
            }
          }, timeToEvent));
        }
      });
    }
    return () => {
      timers.forEach((timer) => clearTimeout(timer));
    };
  }, [currDateTime, fetchMarketStatus, mktExchangeStatusConfig]);

  useEffect(() => {
    fetch(`https://static.paytmmoney.com/data/v1/${CONFIG.ENV}/mkt_status_packets.json`).then((res) => res.json()).then(({ data }) => setMktExchangeStatusConfig(data));
  }, []);

  useEffect(() => {
    const subscription = marketStatusErrorSubject.pipe(throttleTime(1000)).subscribe((message) => {
      fetchMarketStatus(message || 'Market status has been updated. Please place order now');
    });
    return () => subscription.unsubscribe();
  }, [fetchMarketStatus]);

  const [msDataFetched, setMsDataFetched] = useState(false);
  const getMsData = useCallback(() => {
    if (!msDataFetched) {
      setMsDataFetched(true);
    }
  }, [msDataFetched]);

  useEffect(() => {
    if (msDataFetched) fetchMarketStatus();
  }, [fetchMarketStatus, msDataFetched]);

  return (
    // eslint-disable-next-line react/jsx-no-constructed-context-values
    <MarketStatusContext.Provider value={{ marketStatus, getMsData, msDataFetched }}>
      {children}
    </MarketStatusContext.Provider>
  );
}

function useMarketStatus() {
  const ctx = useContext(MarketStatusContext);
  const { getMsData, msDataFetched } = ctx;

  useEffect(() => {
    if (!msDataFetched && getMsData) {
      getMsData();
    }
  }, [getMsData, msDataFetched]);

  return ctx;
}

export default MarketStatus;
export { useMarketStatus };
