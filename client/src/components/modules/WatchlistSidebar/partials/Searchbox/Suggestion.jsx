import React, { useContext } from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';

import usePlaceOrder from '@common/usePlaceOrder';
import { INSTRUMENTS, SEGMENT_TYPES, ROUTE_NAME } from '@utils/enum';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { highlightText } from '@modules/WatchlistSidebar/utils';
import GAElement from '@common/GAElement';
import { COMMON_PAGE_GA } from '@constants/ga-events';
import StockControls from '@common/StockControls';
import If from '@common/If';
import { useIrData } from '@layout/App/UserContext';
import { sendEvent } from '@service/AnalyticsService';
import { getInstrumentLink, getSourceNameFromRoute, getUrlParameter } from '@utils';
import { WatchlistContext } from '../../watchlistContext';
import Routes from '../../../../../routes';
import styles from './index.scss';
import { RECENT_STOCKS } from './enums';
import ROUTES from '../../../Header/enums';

export const BookmarkedIcon = ({ size = 3, onClick = () => { } }) => (
  <Icon
    className={styles.icon}
    name={ICON_NAME.BOOKMARKED}
    size={size}
    onIconClick={onClick}
  />
);
export const NotbookmarkedIcon = ({ size = 3, onClick = () => { } }) => (
  <Icon
    className={styles.icon}
    name={ICON_NAME.NOT_BOOKMARKED}
    size={size}
    onIconClick={onClick}
  />
);

const Suggestion = ({
  id, name, exchange, bookmarkClick, suggestionClick, searchQuery, matched_identifier, instrumentType,
  stockSuggestionTitle, positionInList, securityId, isin, tick_size, segment, symbol,
  lot_size, isStockControlShown = true, isBookmarkRequired, orderCount = 0, placeOrderInBasket = null,
  updateSearchFocused, expiryDate,
}) => {
  const {
    stocksToWatchlistMap, isMultiGrid, checkMultiGridInitChartsData, isAdvancedChartSelected,
  } = useContext(WatchlistContext);
  const isAddedInWatchlist = !!stocksToWatchlistMap[id];
  const processStockName = highlightText({ name, searchQuery });
  const { pathname, search } = useLocation();
  const history = useHistory();
  const { isInvestmentReady, isInvestmentReadyFO } = useIrData();
  const gttStitch = decodeURIComponent(getUrlParameter('gttStitch', search));

  const isFNOScrip = [INSTRUMENTS.FUTIDX, INSTRUMENTS.FUTSTK,
    INSTRUMENTS.OPTIDX, INSTRUMENTS.OPTSTK].includes(instrumentType);

  const onBookmarkClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    bookmarkClick(e, {
      id, name, exchange, matched_identifier, instrumentType, segment, lot_size,
    });
  };

  const onSuggestionClick = () => {
    suggestionClick();
  };

  const { buy, sell } = usePlaceOrder(name, exchange, securityId, {
    isin,
    segment,
    tickSize: tick_size / 100,
    lotSize: lot_size,
    instrumentType,
    instrument_Id: id,
    isWatchlist: true,
    orderCount,
    placeOrderInBasket,
    exch_symbol: symbol,
  });

  const closeSuggestionContainer = () => {
    if (placeOrderInBasket && ((segment === SEGMENT_TYPES.DERIVATIVES && isInvestmentReadyFO)
      || (segment === SEGMENT_TYPES.CASH && isInvestmentReady))) {
      updateSearchFocused(false);
    }
  };

  const suggestionMultigridRowAction = () => {
    if (!isAdvancedChartSelected) {
      history.push(
        `${Routes[ROUTE_NAME.INDEX_PAGE].url}/${id}?toggleDetails=true`,
      );
    } else {
      checkMultiGridInitChartsData({
        name,
        exchange,
        security_id: securityId,
        id,
        instrument_type: instrumentType,
        segment,
      });
    }
  };

  const getSuggestionContent = () => (
    <GAElement
      gaEventAction={
          stockSuggestionTitle === RECENT_STOCKS
            ? COMMON_PAGE_GA.RECENT_INDEX_CLICKED.EVENT_ACTION
            : COMMON_PAGE_GA.SEARCHED_RESULT_CLICKED.EVENT_ACTION
        }
      gaEventCategory={
          stockSuggestionTitle === RECENT_STOCKS
            ? COMMON_PAGE_GA.RECENT_INDEX_CLICKED.EVENT_CATEGORY
            : COMMON_PAGE_GA.SEARCHED_RESULT_CLICKED.EVENT_CATEGORY
        }
      gaEventLabel={
          stockSuggestionTitle === RECENT_STOCKS
            ? COMMON_PAGE_GA.RECENT_INDEX_CLICKED.EVENT_LABEL(getSourceNameFromRoute(ROUTES, pathname),
              name.toLowerCase(),
              positionInList)
            : COMMON_PAGE_GA.SEARCHED_RESULT_CLICKED.EVENT_LABEL(getSourceNameFromRoute(ROUTES, pathname),
              name.toLowerCase(),
              positionInList)
        }
    >
      <div key={id} className={styles.stockSuggestion} role="presentation" onClick={onSuggestionClick}>
        <div className={styles.stockDetails}>
          <div className={styles.stockName}>
            {
                processStockName.map(({ value, select }, index) => {
                  if (select) {
                    return <div className={styles.bold} key={index}>{value}</div>;
                  }
                  return <div className={styles.text} key={index}>{value}</div>;
                })
              }
          </div>
          <div className={styles.stockDescription}>
            {`${exchange}${matched_identifier ? `: ${matched_identifier}` : ''}`}
            <If test={isFNOScrip}>
              <span className={styles.expiryContainer}>
                Expiry:
                <span className={styles.expiryDate}>{expiryDate}</span>
              </span>
            </If>
          </div>
        </div>
        <div className={styles.stockAction} role="presentation">
          {instrumentType !== INSTRUMENTS.INDEX && isStockControlShown
              && (
                <StockControls
                  buy={(e) => {
                    e.nativeEvent.stopImmediatePropagation();
                    buy();
                    sendEvent({
                      event_category: 'order',
                      event_action: 'order_entry_from_search_buy',
                      event_label: 'orders',
                      vertical_name: 'stocks',
                      screenName: '/stocks_orders',
                    });
                    closeSuggestionContainer();
                  }}
                  sell={(e) => {
                    e.nativeEvent.stopImmediatePropagation();
                    sell();
                    sendEvent({
                      event_category: 'order',
                      event_action: 'order_entry_from_search_sell',
                      event_label: 'orders',
                      vertical_name: 'stocks',
                      screenName: '/stocks_orders',
                    });
                    closeSuggestionContainer();
                  }}
                  className={styles.controls}
                  changeCtaText={!!placeOrderInBasket}
                />
              )}
          <If test={isBookmarkRequired}>
            <div
              role="presentation"
              onClick={(e) => {
                e.nativeEvent.stopImmediatePropagation();
              }}
              data-testid="bookmark-button"
            >
              <GAElement
                gaEventAction={COMMON_PAGE_GA.BOOKMARK_ICON_CLICKED.EVENT_ACTION}
                gaEventCategory={COMMON_PAGE_GA.BOOKMARK_ICON_CLICKED.EVENT_CATEGORY}
                gaEventLabel={
                    COMMON_PAGE_GA.BOOKMARK_ICON_CLICKED.EVENT_LABEL(
                      getSourceNameFromRoute(ROUTES, pathname),
                      name.toLowerCase(),
                      positionInList,
                    )
                  }
              >
                <If test={!placeOrderInBasket}>
                  {isAddedInWatchlist
                    ? <BookmarkedIcon onClick={onBookmarkClick} />
                    : <NotbookmarkedIcon onClick={onBookmarkClick} />}
                </If>
              </GAElement>
            </div>
          </If>
        </div>
      </div>
    </GAElement>
  );

  const gttActionLink = gttStitch ? 'action=place-gtt' : '';

  return (
    isMultiGrid
      ? (
        <div role="presentation" onClick={suggestionMultigridRowAction}>
          {getSuggestionContent()}
        </div>
      )
      : (
        <Link to={`${getInstrumentLink(instrumentType)}/${id}?${gttActionLink}`}>
          {getSuggestionContent()}
        </Link>
      )
  );
};

export default Suggestion;
