import React, {
  useContext, useEffect, useRef, useMemo,
} from 'react';
import { useCallbackForEvents } from '@utils/react';
import { ICON_NAME } from '@common/Icon/enums';
import EmptyState from '@common/EmptyState';
import { sendEvent } from '@service/AnalyticsService';
import { WATCHLIST_GA } from '@constants/ga-events';
import LocalStorage from '@service/LocalStorage';
import { FNO_SEARCH_ENABLE, EXCHANGE, INSTRUMENTS } from '@utils/enum';
import { useIrData } from '@layout/App/UserContext';
import styles from './index.scss';
import { WatchlistContext } from '../../watchlistContext';
import Suggestion from './Suggestion';
import { NO_SUGGESTIONS } from './enums';
import ScopeFilters from './partials/ScopeFilters';

const SuggestionsContainer = ({
  suggestions, searchQuery, searchFocused, updateSuggestions, updateSearchFocused,
  handleBookmarkClick, customClass, emptyCustomClass,
  handleSuggestionClick, searchInputRef, inProgress,
  stockSuggestionTitle, currentScope, setCurrentScope,
  isScopeEnabled = true, isStockControlRequired, isBookmarkRequired, orderCount = 0, placeOrderInBasket = null,
}) => {
  const buttonRef = useRef(null);
  const { watchlistData } = useContext(WatchlistContext);
  const { isInvestmentReadyFO } = useIrData();

  const showScopes = useMemo(() => {
    if (isInvestmentReadyFO
      && (LocalStorage.get(FNO_SEARCH_ENABLE) || LocalStorage.get(FNO_SEARCH_ENABLE) === null)) {
      return true;
    }
    return false;
  }, [isInvestmentReadyFO]);

  const handleOpenSuggestionDocClick = useCallbackForEvents((e) => {
    // clicking on search input should not close suggestion container
    if (e.target === searchInputRef?.current || e.target === buttonRef.current) {
      return;
    }
    // update searchFocused state to close suggestions
    updateSearchFocused(false);
  }, [watchlistData, searchInputRef]);

  useEffect(() => {
    window.document.addEventListener('click', handleOpenSuggestionDocClick, false);
    return () => {
      window.document.removeEventListener('click', handleOpenSuggestionDocClick, false);
    };
  }, [handleOpenSuggestionDocClick, suggestions, updateSuggestions]);

  const onBookmarkClick = (e, suggestionId) => {
    handleBookmarkClick(suggestionId);
    if (watchlistData.length <= 1) {
      // stopping event propagation will prevent closing suggestions container.
      e.nativeEvent.stopImmediatePropagation();
    }
  };

  const onSuggestionClick = () => {
    handleSuggestionClick();
  };

  const emptyStatePersist = (e) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    searchInputRef.current.focus();
  };

  if (!searchFocused) {
    return null;
  }

  const handleScopeChange = (scope) => {
    setCurrentScope(scope);
    searchInputRef.current.focus();
  };

  return (
    <>
      {
        (showScopes && isScopeEnabled)
          ? (
            <div onClick={emptyStatePersist} role="presentation">
              <ScopeFilters
                currentScope={currentScope}
                handleScopeSelect={handleScopeChange}
                basketOrders={!!placeOrderInBasket}
              />
            </div>
          )
          : null
      }
      {(suggestions.length === 0 && !inProgress)
        ? (
          <div
            className={emptyCustomClass || styles.emptySuggestions}
            onClick={(e) => emptyStatePersist(e)}
            role="button"
            tabIndex={0}
            aria-label="empty-suggestions-container"
          >
            <EmptyState
              text={NO_SUGGESTIONS.HEADING}
              subText={NO_SUGGESTIONS.SUBHEADING}
              iconName={ICON_NAME.EMPTY_SEARCH_RESULT}
              isFullScreen={false}
              ctaText={NO_SUGGESTIONS.BUTTON}
              className={styles.emptySearchContainer}
              onCtaClick={() => {
                searchInputRef.current.focus();
                sendEvent({
                  event_category: WATCHLIST_GA.RETRY_SEARCH_CLICKED.EVENT_CATEGORY,
                  event_action: WATCHLIST_GA.RETRY_SEARCH_CLICKED.EVENT_ACTION,
                  event_label: WATCHLIST_GA.RETRY_SEARCH_CLICKED.EVENT_LABEL(searchQuery),
                });
              }}
              buttonRef={buttonRef}
            />
          </div>
        )
        : (
          !inProgress
          && (
            <div className={`${customClass || styles.suggestionDropdown} ${showScopes && styles.showScopeFilters}`}>
              {stockSuggestionTitle && (
                <div className={styles.stockSuggestionTitle}>
                  <div className={styles.stockTitleName}>{stockSuggestionTitle}</div>
                </div>
              )}
              {suggestions
                .map(({
                  id, name, exchange, matched_identifier, instrument_type, isin, security_id: securityId,
                  tick_size, segment, lot_size, exch_symbol, expiry_display_date,
                }, key) => {
                  const hideStockControls = placeOrderInBasket !== null
                    && exchange === EXCHANGE.BSE
                    && [INSTRUMENTS.FUTIDX, INSTRUMENTS.OPTIDX].includes(instrument_type);
                  return (
                    <Suggestion
                      key={id}
                      id={id}
                      name={name}
                      exchange={exchange}
                      instrumentType={instrument_type}
                      searchQuery={searchQuery}
                      matched_identifier={matched_identifier}
                      bookmarkClick={onBookmarkClick}
                      suggestionClick={onSuggestionClick}
                      stockSuggestionTitle={stockSuggestionTitle}
                      positionInList={key + 1}
                      isin={isin}
                      securityId={securityId}
                      tick_size={tick_size}
                      lot_size={lot_size}
                      segment={segment}
                      symbol={exch_symbol}
                      isStockControlShown={isStockControlRequired && !hideStockControls}
                      isBookmarkRequired={isBookmarkRequired}
                      orderCount={orderCount}
                      placeOrderInBasket={placeOrderInBasket}
                      updateSearchFocused={updateSearchFocused}
                      expiryDate={expiry_display_date}
                    />
                  );
                })}
            </div>
          )
        )}
    </>
  );
};

export default SuggestionsContainer;
