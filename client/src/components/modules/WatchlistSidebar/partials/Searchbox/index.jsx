import React, {
  useContext, useState, useLayoutEffect,
} from 'react';
import { useCallbackForEvents } from '@utils/react';
import { classNames as cx } from '@utils/style';
import { RECENT_TYPE, UserEventContext } from '@layout/App/UserEventContext';
import { mobileBrowser } from '@utils';
import styles from './index.scss';
import SearchInput from './SearchInput';
import SuggestionsContainer from './SuggestionsContainer';
import { WatchlistContext } from '../../watchlistContext';
import useSearchBox from './useSearchBox';
import { getTrendingStocksList } from '../../watchlist-sidebar-api';
import { SEARCH_SCOPES } from './partials/ScopeFilters/enums';

const Searchbox = ({
  searchQuery,
  updateSearchQuery,
  isSuggestionRequired = true,
  showScope = true,
  customClass,
  isStockControlRequired = true,
  isBookmarkRequired = true,
  emptyCustomClass,
  scopeSelected = null,
  orderCount = 0,
  placeOrderInBasket = null,
}) => {
  const {
    activeWatchlistStocks, handleExpansion, searchInputRef,
  } = useContext(WatchlistContext);
  const [stockSuggestionTitle, setStockSuggestionTitle] = useState('');
  const { getRecentSecurities, addRecentSecurity } = useContext(UserEventContext);
  const [currentScope, setCurrentScope] = useState(scopeSelected
    ? (SEARCH_SCOPES.find((item) => item.id === scopeSelected)?.id)
    : SEARCH_SCOPES[0].id);

  const {
    suggestions,
    updateSuggestions,
    searchFocused,
    updateSearchFocused,
    handleInputFocus,
    inProgress,
    makeRequest,
    isShowSearch,
  } = useSearchBox({ searchInputRef, searchScope: currentScope });

  const handleSearchInput = (e) => {
    updateSearchQuery(e.target.value);
    if (!isSuggestionRequired && searchQuery.length > 0) {
      updateSearchFocused(true);
    }
  };

  const handleBookmarkClick = (suggestionData) => {
    handleExpansion(suggestionData.id, true);
    addRecentSecurity(RECENT_TYPE.SEARCHED, suggestionData);
  };

  const handleSuggestionClick = () => {
    updateSearchQuery('');
    updateSearchFocused(false);
  };

  const getRecentSearchedStocks = useCallbackForEvents(async () => {
    try {
      const recentlySearched = await makeRequest(getRecentSecurities(RECENT_TYPE.SEARCHED));
      setStockSuggestionTitle('Recent Stocks');
      updateSuggestions(recentlySearched || []);
    } catch (error) {
    }
  }, []);

  const getTrendingStocks = useCallbackForEvents(async () => {
    try {
      const {
        data: { results },
      } = await makeRequest(getTrendingStocksList());
      setStockSuggestionTitle('Trending Stocks');
      updateSuggestions(results || []);
    } catch (error) {
    }
  }, []);

  const setRecentOrTrendingSuggestions = useCallbackForEvents(() => {
    if (!activeWatchlistStocks?.length) {
      getTrendingStocks();
    } else {
      getRecentSearchedStocks();
    }
  }, [
    getRecentSearchedStocks,
    getTrendingStocks,
    activeWatchlistStocks]);

  useLayoutEffect(() => {
    if (mobileBrowser()) {
      searchInputRef.current.focus();
    }
  }, [searchInputRef]);

  useLayoutEffect(() => {
    if (isSuggestionRequired && searchFocused && !searchQuery) {
      setRecentOrTrendingSuggestions();
    } else {
      setStockSuggestionTitle('');
    }
  },
  [isSuggestionRequired, searchFocused, searchQuery, setRecentOrTrendingSuggestions]);

  return (
    <div className={cx([styles.searchBoxContainer], {
      [styles.suggestionsActive]: !inProgress && suggestions.length && searchFocused,
      [styles.empstyStateActive]: !inProgress && suggestions.length === 0 && searchFocused,
      [styles.addHeight]: !inProgress && suggestions.length && searchFocused && placeOrderInBasket,
      [styles.basketOrderSearchWrapper]: placeOrderInBasket,
    })}
    >
      <SearchInput
        handleOnChange={handleSearchInput}
        searchQuery={searchQuery}
        placeholder="Search by Stock Name"
        searchInputRef={searchInputRef}
        handleInputFocus={handleInputFocus}
        updateSearchQuery={updateSearchQuery}
      />
      <SuggestionsContainer
        suggestions={suggestions}
        searchQuery={searchQuery}
        searchFocused={searchFocused}
        updateSuggestions={updateSuggestions}
        updateSearchFocused={updateSearchFocused}
        handleBookmarkClick={handleBookmarkClick}
        handleSuggestionClick={handleSuggestionClick}
        searchInputRef={searchInputRef}
        inProgress={inProgress}
        stockSuggestionTitle={stockSuggestionTitle}
        currentScope={currentScope}
        setCurrentScope={setCurrentScope}
        isScopeEnabled={showScope}
        customClass={customClass}
        isStockControlRequired={isStockControlRequired}
        isBookmarkRequired={isBookmarkRequired}
        emptyCustomClass={emptyCustomClass}
        orderCount={orderCount}
        placeOrderInBasket={placeOrderInBasket}
        isShowSearch={isShowSearch}
      />
    </div>
  );
};
export default Searchbox;
