@import './../../../../../styles/main';

$caTagBackgroundColor: rgba(238, 156, 22, .1);

.sortWrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 4.1rem;
}

.sortheader {
  margin-right: .5rem;
  color: $grey2;

  @include typography(h7);
}

.showMore {
  color: $primary-color;
  margin-top: 1.5rem;
  display: inline-flex;
  cursor: pointer;

  @include typography(h8);
}

.downArrow {
  margin-left: .5rem;

  div {
    border-color: $primary-color;
  }
}

.dropdown {
  color: $grey1;
  margin: auto;
  border-radius: .4rem;
  width: 13rem;
  height: 2.2rem;
  background-color: $default;
  /* stylelint-disable-next-line declaration-no-important */
  padding: .5rem 1rem .5rem .5rem !important;

  @include typography(h8);

  .dropdownOption {
    border: 0;
    height: 3rem;

    @include typography(h8);
  }

  .selectedSortOption {
    color: $secondary-color;

    @include typography(h8, semibold);
  }
}

.listingContainer {
  height: calc(100% - 4.1rem);
  position: relative;
  overflow: auto;
  border-radius: .4rem;
  background: $default;

  @include box-shadow(0, 2px, 6px);

  @include respond(phone) {
    display: contents;
  }

  > :last-child::after {
    content: '';
    display: block;
    margin-bottom: 4.5rem;
  }
}

.listingLoaderWrapper {
  background: $default;
  height: calc(100% - 4.1rem);
  margin-top: 2rem;

  > div > *:not(:last-child) {
    border-bottom: .1rem solid $grey4;
    margin-bottom: 0;
  }
}

.listingOverlay {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
}

.noWatchlistWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.noWatchlistText {
  color: $grey2;
  margin: 2.5rem 0 2rem;

  @include typography(h7);
}

.noWatchlistBtn {
  padding: 1rem 3.4rem;
  border-radius: .3rem;

  @include btnFill($primary-color);
  @include typography(h7);

  &:hover {
    cursor: pointer;
  }
}

.stockContainer {
  border-bottom: .1rem solid $grey4;
  color: $grey1;
  padding: 1rem 1.5rem;
  min-height: 5.5rem;

  &:hover {
    cursor: pointer;

    @include box-shadow(0, 0, 10px);

    .valueContainer {
      display: none;
    }

    .stockDescription {
      max-width: calc(100% - 18.1rem);
    }

    .name {
      text-overflow: ellipsis;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      display: inline-flex;
    }
  }
}

.collapseStockView {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expandedStockView {
  overflow: hidden;
  max-height: 0;
  transition: max-height .25s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.showExpandedStockView {
  max-height: 48rem;
  transition: max-height .25s ease-in;
}

.expandedWrapper {
  min-height: 30rem;
}

.stockDescription {
  display: flex;
  flex-direction: column;
}

.name {
  margin-bottom: .5rem;
  display: inline-flex;

  @include typography(h7);

  @include respond(phone) {
    color: $grey0;

    @include typography(h6, semibold);
  }
}

.exchange {
  color: $grey2;
  display: flex;

  @include typography(h8);

  @include respond(phone) {
    margin-top: 1rem;

    @include typography(h7);
  }

  > span {
    margin-left: 1rem;
  }
}

.valueContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.rateContainer {
  display: flex;
  align-items: center;
  margin-bottom: .5rem;
}

.currentRate {
  margin-right: .5rem;

  @include typography(h7);

  @include respond(phone) {
    color: $grey0;

    @include typography(h6, semibold);
  }
}

.change {
  color: $green;
}

.marketDepthWrapper {
  padding: 1rem 0;
}

.marketDepthHeader {
  margin-bottom: 1rem;

  @include typography(h7);
}

.tag {
  display: inline-flex;
}

.performanceContainer {
  > div > div {
    width: 95%;
  }
}

.priceChange {
  @include respond(phone) {
    margin: .8rem 0 0;
  }

  > div {
    @include respond(phone) {
      @include typography(h7, upperbold);
    }

    @include respond(desktop) {
      @include typography(h8);
    }
  }
}

.controls {
  display: none;
}

.listingRow {
  background-color: $default;
  transition: all .1s;
  position: relative;
}

.dragIconContainer {
  position: absolute;
  left: 50%;
  bottom: .5rem;
}

.dragIcon {
  display: none;
  border-top: .1rem solid $grey3;
  border-bottom: .1rem solid $grey3;
  height: .5rem;
  width: 1.4rem;
  cursor: pointer;
}

.sortableElement {
  box-shadow: 0 0 1rem 0 $box-shadow-color;
  border-bottom: .1rem solid $grey4;
}

.showDragIcon {
  .listingRow:hover {
    .dragIcon {
      display: block;
    }
  }

  .controlsWrapper:hover {
    .controls {
      display: flex;
    }
  }
}

.showIcon {
  .dragIcon {
    display: block;
  }
}

.caTag {
  border-radius: .4rem;
  background-color: $caTagBackgroundColor;
  padding: .1rem .5rem;
  color: $yellow;
  display: inline-flex;
  margin-left: .8rem;
  height: 1.5rem;

  @include typography(h9, semibold);
}

.value {
  color: $grey1;

  @include typography(h8);
}

.infoRow {
  display: flex;
  justify-content: space-between;
  margin-top: .8rem;
}

.header {
  color: $grey0;
  margin-bottom: .8rem;

  @include typography(h8, semibold);
}

.title {
  color: $grey3;

  @include typography(h8);
}

.caDataBox {
  padding: 1.5rem 0;
  border-bottom: .1rem solid $grey4;
}

.caBox {
  max-width: 33rem;
  margin-top: 1.5rem;
  padding: 1.5rem;
  border-radius: .4rem;
  border: solid .1rem $forever-white;
  background-color: $grey5;
  opacity: .75;
}

.SleekCard {
  position: absolute;
  bottom: 4.8rem;
  width: 100%;

  @include respond(phone) {
    position: fixed;
    bottom: 16rem;
    width: 95%;
  }
}

.messageClass {
  padding: .5rem;
  margin-right: 1.5rem;

  @include typography(h7);
}

.close {
  position: absolute;
  right: .5rem;
  top: .5rem;
  cursor: pointer;
}

.watchlistWithInfoCard {
  > :last-child::after {
    content: '';
    display: block;
    margin-bottom: 10.5rem;
  }
}

.editIcon {

  display: flex;
  flex-direction: column;
  align-items: center;
  color: $grey3;
  padding-top: 1.5rem;

  @include typography(h8);

  @include respond(phone) {
    padding-bottom: 7rem;
  }
}
