import React, { useState, useRef, useContext } from 'react';
import PropTypes from 'prop-types';
import usePlaceOrder, { DraggableModalContext } from '@common/usePlaceOrder';
import StockControls from '@common/StockControls';
import { useHistory } from 'react-router-dom';
import { classNames as cx } from '@utils/style';
import { sendEvent } from '@service/AnalyticsService';
import { WATCHLIST_GA } from '@constants/ga-events';
import If from '@common/If';
import useCorporateActionData, { getDescription } from '@common/useCorporateActionData';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { getDateInDdMmmYyyy, getInstrumentLink, mobileBrowser } from '@utils';
import styles from './styles';
import MarketDepthLayout from '../MarketDepthLayout';
import useDeleteInstrument from './useDeleteInstrument';
import InstrumentDetails from './InstrumentDetails';
import PerformanceData from '../../../../pages/Company/partials/Overview/partials/Performance/PerformaceData';
import usePerformanceData, { getFormattedDataForCard } from '../../../../pages/Company/partials/Overview/partials/Performance/usePerformanceData';
import { OPTION_TYPES } from './enums';
import PinScripOptions from '../PinScripOptions';

const Stock = ({
  data: {
    name, exchange, security_id: securityId, instrument_type, isin, tick_size, lot_size,
    segment, expiry_date, ...feedData
  },
  watchlistId,
  instrumentId,
  getStocksOfActiveWatchlist,
  index,
  expandedStockIndex,
  setExpandedStockIndex,
  scrollWatchlistContainer,
  orderCount,
  placeOrderInBasket,
  isMultiGrid,
  checkMultiGridInitChartsData,
  isAdvancedChartSelected,
}) => {
  const { openDraggableModal } = useContext(DraggableModalContext);
  const { deleteInstrument } = useDeleteInstrument({ getStocksOfActiveWatchlist, watchlistId, instrumentId });
  const [showPinScripOptions, setShowPinScripOptions] = useState(true);
  const { caData } = useCorporateActionData({ isin, exchange, segment });
  const isStockExpanded = index === expandedStockIndex;
  const [showCaInfo, setShowCaInfo] = useState(false);
  const [showMore, setShowMore] = useState(caData.length > 1);
  const history = useHistory();

  const expandedStockViewRef = useRef(null);

  const openMarketDepth = () => {
    setExpandedStockIndex(isStockExpanded ? -1 : index);
  };

  const { buy, sell } = usePlaceOrder(name, exchange, securityId, {
    isin,
    segment,
    tickSize: tick_size / 100,
    lotSize: lot_size,
    instrumentType: instrument_type,
    instrument_Id: instrumentId,
    isWatchlist: true,
    orderCount,
    placeOrderInBasket,
  });
  const performanceData = usePerformanceData({ exchange, securityId, segment });

  const sendGAEvent = (type) => {
    sendEvent({
      event_category: WATCHLIST_GA.STOCK_OPTIONS_CLICKED.EVENT_CATEGORY,
      event_action: WATCHLIST_GA.STOCK_OPTIONS_CLICKED.EVENT_ACTION,
      event_label: WATCHLIST_GA.STOCK_OPTIONS_CLICKED.EVENT_LABEL(
        name.toLowerCase(),
        type,
      ),
    });
  };

  const instrumentLink = getInstrumentLink(instrument_type);

  const caBox = (data) => (
    <div className={styles.caDataBox}>
      <div className={styles.infoRow}>
        <span className={styles.title}>
          Type:
          {' '}
          <span className={styles.value}>{data?.event_type}</span>
        </span>
        {data?.event_type.toLowerCase() === 'dividend' ? (
          <span className={styles.title}>
            Dividend per Share:
            {' '}
            <span className={styles.value}>{`₹ ${data?.dividend_price}`}</span>
          </span>
        ) : null}
      </div>
      <div className={styles.infoRow}>
        <span className={styles.title}>
          Ex-date:
          {' '}
          <span className={styles.value}>{getDateInDdMmmYyyy(data?.ex_date)}</span>
        </span>
        <span className={styles.title}>
          Record Date:
          {' '}
          <span className={styles.value}>{data?.record_date ? getDateInDdMmmYyyy(data?.record_date) : '-'}</span>
        </span>
      </div>
      { (data?.existing_ratio_value && data?.adjusted_ratio_value) && (
      <div className={styles.infoRow}>
        <span className={styles.title}>
          Existing Ratio:
          {' '}
          <span className={styles.value}>{data?.existing_ratio_value}</span>
        </span>
        <span className={styles.title}>
          Adjusted Ratio:
          {' '}
          <span className={styles.value}>{data?.adjusted_ratio_value}</span>
        </span>
      </div>
      ) }
      <div className={styles.infoRow}>
        <span className={styles.title}>
          More Info:
          {' '}
          <span className={styles.value}>{getDescription(data?.event_type)}</span>
        </span>
      </div>
      <If test={caData.length > 1 && showMore}>
        <div role="presentation" onClick={() => setShowMore(false)} className={styles.showMore}>
          Show More
          <Icon size={3} name={ICON_NAME.DROP_DOWN} className={styles.downArrow} />
        </div>
      </If>
    </div>
  );

  return (
    <div
      className={`${styles.stockContainer} ${styles.controlsWrapper}`}
      onMouseLeave={() => setShowPinScripOptions(false)}
      onMouseOver={() => setShowPinScripOptions(true)}
    >
      <div className={styles.collapseStockView}>
        <InstrumentDetails
          name={name}
          exchange={exchange}
          isin={isin}
          showCaInfo={showCaInfo}
          showTag={caData.length}
          setShowCaInfo={setShowCaInfo}
          {...feedData}
        />
        <If test={!mobileBrowser()}>

          <StockControls
            buy={() => {
              buy();
              sendGAEvent(OPTION_TYPES.BUY);
              sendEvent({
                event_category: 'order',
                event_action: 'order_entry_from_watchlist',
                event_label: 'orders',
                vertical_name: 'stocks',
                screenName: '/stocks_orders',
              });
            }}
            sell={(data) => {
              sell(data);
              sendGAEvent(OPTION_TYPES.SELL);
              sendEvent({
                event_category: 'order',
                event_action: 'order_entry_from_watchlist',
                event_label: 'orders',
                vertical_name: 'stocks',
                screenName: '/stocks_orders',
              });
            }}
            openMarketDepth={() => {
              openMarketDepth();
              // to disable multiple events for open and close
              if (expandedStockIndex !== index) {
                sendGAEvent(OPTION_TYPES.MARKET_DEPTH);
              }
            }}
            openCharts={() => {
              const stockData = {
                name,
                exchange,
                security_id: securityId,
                instrument_type,
                isin,
                tick_size,
                lot_size,
                segment,
                expiry_date,
                ...feedData,
              };
              if (!isMultiGrid || !isAdvancedChartSelected) {
                history.push(`${instrumentLink}/${instrumentId}?toggleDetails=true`);
              } else {
                checkMultiGridInitChartsData(stockData);
              }
              sendGAEvent(OPTION_TYPES.SHOW_CHART);
            }}
            deleteStock={() => {
              deleteInstrument();
              sendGAEvent(OPTION_TYPES.REMOVE);
            }}
            showPinOptions={showPinScripOptions}
            PinScripOptions={(
              <PinScripOptions
                name={name}
                securityId={securityId}
                segment={segment}
                exchange={exchange}
                instrument_type={instrument_type}
                {...feedData}
              />
            )}
            className={styles.controls}
            {...feedData}
          />
        </If>
      </div>
      {/* Expanded-Section */}
      <div
        className={cx(styles.expandedStockView, { [styles.showExpandedStockView]: isStockExpanded })}
        onTransitionEnd={(e) => {
          if (expandedStockViewRef.current === e.target && isStockExpanded) {
            scrollWatchlistContainer();
          }
        }}
        ref={expandedStockViewRef}
      >
        <div className={styles.expandedWrapper}>
          {isStockExpanded && (
            <>
              <div className={styles.marketDepthWrapper}>
                <div className={styles.marketDepthHeader}>Market Depth</div>
                <MarketDepthLayout
                  exchange={exchange}
                  securityId={securityId}
                  segment={segment}
                  name={name}
                  isin={isin}
                  tickSize={tick_size}
                  lotSize={lot_size}
                  instrumentType={instrument_type}
                  openDraggableModal={openDraggableModal}
                />
              </div>
              <div className={styles.marketDepthWrapper}>
                <div className={styles.marketDepthHeader}>Performance</div>
                <PerformanceData
                  data={getFormattedDataForCard({ ...performanceData, expiryDate: expiry_date }, segment)}
                  containerClass={styles.performanceContainer}
                />
              </div>
            </>
          )}
        </div>
      </div>
      <If test={showCaInfo && caData.length}>
        <div className={styles.caBox}>
          <div className={styles.header}>
            Corporate Action for this Stock
          </div>
          {caData.map((data, i) => {
            if (i == 0) {
              return caBox(data);
            }
            if (i !== 0 && !showMore) {
              return caBox(data);
            }
            return <div />;
          })}
          <If test={!showMore}>
            <div role="presentation" onClick={() => setShowMore(true)} className={styles.showMore}>
              Show Less
              <Icon size={3} name={ICON_NAME.DROP_UP} className={styles.downArrow} />
            </div>
          </If>
        </div>
      </If>
    </div>
  );
};

Stock.propTypes = {
  data: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])).isRequired,
};

export default Stock;
