import React, {
  useState, useCallback, useContext, useRef,
  useEffect,
} from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import useObservable from '@common/useObservable';
import WithLoader from '@common/WithLoader';
import { ICON_NAME, staticImagePath } from '@common/Icon/enums';
import EmptyState from '@common/EmptyState';
import { ROUTE_NAME, INSTRUMENTS, INFO_CARD } from '@utils/enum';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE, DIRECTION } from '@common/SkeletonLoader/enums';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { SortableContainer, SortableElement } from 'react-sortable-hoc';
import { usePostApi } from '@common/UseApi';
import LocalStorage from '@service/LocalStorage';
import InfoCard from '@pages/Portfolio/partials/InfoCard';
import { classNames as cx } from '@utils/style';
import Tooltip from '@common/Tooltip';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { useModal } from '@layout/App/ModalContext';
import { MODAL_TYPES } from '@common/Modal/enums';
import If from '@common/If';
import { sendPageEvent } from '@service/AnalyticsService';
import Routes from '@/routes';
import {
  getInstrumentLink,
  getUrlParameter, setQueryParam, moveElementInArray, mobileBrowser, isBondInstrument, uniqueArrayVal,
} from '@utils';

import { NO_WATCH_LIST } from './enums';
import Stock from './Stock';
import styles from './styles';
import IndexInstrument from './IndexInstrument';
import WatchlistStockSorter from './WatchlistStockSorter';
import useIndexStockFeed from '../../../../pages/Index/useIndexStockFeed';
import { defaultSortOption, sortFxn } from '../../../../common/useSort';
import { reorderSecurities } from '../../watchlist-sidebar-api';
import WatchlistEdit from './WatchlistEdit';
import BondInstrument from './BondInstrument';

export const EmptyWatchlistView = ({ searchInputRef, isExpanded, handleExpansion }) => {
  const { setShowSearch } = useLoggedInContext();

  const onAddStock = useCallback(
    (e) => {
      if (mobileBrowser()) {
        setShowSearch(true);
      } else {
        if (isExpanded) { handleExpansion(); }
        e.preventDefault();
        e.nativeEvent.stopImmediatePropagation();
        searchInputRef.current.focus();
      }
    },
    [handleExpansion, isExpanded, searchInputRef, setShowSearch],
  );

  return (
    <EmptyState
      text={NO_WATCH_LIST.HEADING}
      subText={NO_WATCH_LIST.SUBHEADING}
      ctaText={NO_WATCH_LIST.BUTTON}
      iconName={ICON_NAME.NO_WATCH_LIST}
      isFullScreen={false}
      onCtaClick={onAddStock}
    />
  );
};

export const IsMultiGridHOC = (WrappedInstrumentComponent, anyProps) => {
  const {
    gttStitch, data, instrumentId, isMultiGrid, checkMultiGridInitChartsData, isBond,
    isAdvancedChartSelected,
  } = anyProps;
  let linkTo = null;
  const gttActionLink = gttStitch ? 'action=place-gtt' : '';

  if (data && data?.instrument_type && instrumentId) {
    linkTo = `${getInstrumentLink(data.instrument_type)}/${instrumentId}?${gttActionLink}`;
  } else {
    linkTo = `${Routes[ROUTE_NAME.INDEX_PAGE].url}/${instrumentId}?${gttActionLink}`;
  }

  const history = useHistory();
  const watchlistMultigridRowAction = () => {
    sendPageEvent({
      // openScreen Events || Pulse Events
      eventDetails: {
        screenName: '/watchlist_companypage',
        vertical_name: 'stocks',
        event_action: 'open_screen',
      },
    });
    if (!isAdvancedChartSelected) {
      setQueryParam({
        query: {},
        pathname: linkTo,
        replace: history.push,
      });
      return;
    }
    checkMultiGridInitChartsData(data);
  };

  const handleClick = () => {
    sendPageEvent({
      // openScreen Events || Pulse Events
      eventDetails: {
        screenName: '/watchlist_companypage',
        vertical_name: 'stocks',
        event_action: 'open_screen',
      },
    });
  };

  return (
    isMultiGrid && !isBond
      ? (
        <div role="presentation" onClick={watchlistMultigridRowAction}>
          <WrappedInstrumentComponent {...anyProps} />
        </div>
      )
      : (
        <Link to={linkTo} onClick={handleClick}>
          <WrappedInstrumentComponent {...anyProps} />
        </Link>
      )
  );
};

export const WatchlistListingRow = ({
  draggingStockIndex,
  watchlistId,
  setCurrentWatchlist,
  instrumentId,
  data: stocksData,
  getStocksOfActiveWatchlist,
  instrument_type,
  rowIndex,
  activeSort,
  expandedStockIndex,
  setExpandedStockIndex,
  watchlistListingRef,
  orderCount,
  placeOrderInBasket,
  gttStitch,
}) => {
  const { security_id, exchange, segment } = stocksData;
  const stockDetailsRef = useRef({});
  const stockDatatRef = useRef({});
  const reqStreams = useIndexStockFeed();
  const stockDetails = useObservable(
    () => reqStreams({
      security_id,
      exchange,
      instrument_type,
      segment,
    }),
    [reqStreams, security_id, exchange, instrument_type, segment],
  ) || {};

  stockDetailsRef.current = stockDetails;
  stockDatatRef.current = stocksData;
  useEffect(() => {
    const watchListItem = {
      ...(stockDatatRef.current),
      ...(stockDetailsRef.current),
    };
    setCurrentWatchlist(watchListItem);
  }, [activeSort, setCurrentWatchlist]);

  const props = {
    watchlistId,
    instrumentId,
    data: { ...stocksData, ...stockDetails },
    getStocksOfActiveWatchlist,
    instrument_type,
    index: rowIndex,
  };

  const watchListingRowRef = useRef(null);

  const scrollWatchlistContainer = useCallback(() => {
    watchlistListingRef.current.scroll({
      top: watchListingRowRef.current.offsetTop,
      left: 0,
      behavior: 'smooth',
    });
  }, [watchlistListingRef, watchListingRowRef]);

  const {
    isMultiGrid,
    checkMultiGridInitChartsData,
    isAdvancedChartSelected,
  } = useContext(WatchlistContext);

  const isBond = isBondInstrument(instrument_type);

  const anyProps = {
    expandedStockIndex,
    setExpandedStockIndex,
    scrollWatchlistContainer,
    orderCount,
    placeOrderInBasket,
    isMultiGrid,
    checkMultiGridInitChartsData,
    isBond,
    gttStitch,
    isAdvancedChartSelected,
    ...props,
  };

  return (
    <div
      className={cx(styles.listingRow, {
        [styles.showIcon]: draggingStockIndex === rowIndex,
      })}
      ref={watchListingRowRef}
    >
      {isBond && IsMultiGridHOC(BondInstrument, anyProps)}
      {instrument_type === INSTRUMENTS.INDEX
        ? IsMultiGridHOC(IndexInstrument, anyProps)
        : (!isBond) && IsMultiGridHOC(Stock, anyProps)}
      <div className={styles.dragIconContainer}>
        <Tooltip message="Reorder">
          <div className={styles.dragIcon} />
        </Tooltip>
      </div>
    </div>
  );
};

export const WatchlistListing = ({
  sortedStockList,
  draggingStockIndex,
  orderCount,
  placeOrderInBasket,
  activeSort,
  setCurrentWatchlist,
}) => {
  const [expandedStockIndex, setExpandedStockIndex] = useState(-1);
  const {
    activeWatchlist, getStocksOfActiveWatchlist, searchInputRef, isExpanded, handleExpansion,
  } = useContext(WatchlistContext);
  const [infoCardVisible, setInfoCardVisibility] = useState(LocalStorage.get(`${INFO_CARD.WATCHLIST}Card`));

  const watchlistListingRef = useRef(null);
  const { pathname, search } = useLocation();
  const gttStitch = decodeURIComponent(getUrlParameter('gttStitch', search));
  const history = useHistory();
  const handleEdit = () => {
    setQueryParam({
      query: {
        mode: 'edit',
      },
      pathname,
      replace: history.push,
    });
  };
  return (
    <>
      <div
        className={cx(styles.listingContainer, {
          [styles.showDragIcon]: draggingStockIndex === -1,
          [styles.watchlistWithInfoCard]: infoCardVisible == null || infoCardVisible,
        })}
        ref={watchlistListingRef}
      >
        {sortedStockList?.length
          ? sortedStockList.map((item, index) => {
            const props = {
              key: item.id,
              watchlistId: activeWatchlist?.id,
              instrumentId: item.id,
              data: item,
              getStocksOfActiveWatchlist,
              instrument_type: item.instrument_type,
              rowIndex: index,
              draggingStockIndex,
            };

            return (
              <WatchlistListingRowWrapper
                {...props}
                activeSort={activeSort}
                setCurrentWatchlist={setCurrentWatchlist}
                index={index}
                expandedStockIndex={expandedStockIndex}
                setExpandedStockIndex={setExpandedStockIndex}
                watchlistListingRef={watchlistListingRef}
                orderCount={orderCount}
                placeOrderInBasket={placeOrderInBasket}
                gttStitch={gttStitch}
              />
            );
          })
          : (
            <EmptyWatchlistView
              searchInputRef={searchInputRef}
              isExpanded={isExpanded}
              handleExpansion={handleExpansion}
            />
          )}
        <If test={mobileBrowser() && sortedStockList?.length}>
          <div className={styles.editIcon}>
            <img onClick={handleEdit} src={`${staticImagePath}/common/edit-watchlist.svg`} alt="Edit" />
            <div>Edit Watchlist</div>
          </div>
        </If>
      </div>
      <InfoCard
        infoCardObject={INFO_CARD.WATCHLIST}
        className={styles.SleekCard}
        closeIconClass={styles.close}
        messageClass={styles.messageClass}
        setInfoCardVisibility={setInfoCardVisibility}
      />
    </>
  );
};

export const WatchlistListingContainer = ({ hideWatchlistStockSorter = false, orderCount, placeOrderInBasket }) => {
  const {
    activeWatchlist, activeWatchlistStocks, setActiveWatchlistStocks,
  } = useContext(WatchlistContext);
  const [draggingStockIndex, setDraggingStockIndex] = useState(-1);
  const currentWatchlist = useRef([]);
  const { makeRequest, inProgress } = usePostApi();
  const { openModal, closeModal } = useModal();
  const { search } = useLocation();
  const mode = getUrlParameter('mode', search);
  const [sortedStockList, setSortedStockList] = useState(activeWatchlist);
  const [activeSort, setActiveSort] = useState({});

  const setCurrentWatchlist = useCallback((item) => {
    currentWatchlist.current = [...currentWatchlist.current, item];
    const currentWatchlistArray = uniqueArrayVal(currentWatchlist.current, 'id');
    if (currentWatchlistArray.length === activeWatchlistStocks.length) {
      const sortData = sortFxn({
        activeSort,
        data: activeSort.id ? currentWatchlistArray : activeWatchlistStocks,
      });
      setSortedStockList([...sortData]);
      currentWatchlist.current = [];
    }
  }, [activeWatchlistStocks, activeSort]);

  const handleSorting = useCallback((selectedTab) => {
    if (activeSort.id !== selectedTab.id || activeSort.sortingOrder !== selectedTab.sortingOrder) {
      setActiveSort(selectedTab);
    }
  }, [activeSort.sortingOrder, activeSort.id]);

  useEffect(() => {
    setSortedStockList(activeWatchlistStocks);
  }, [activeWatchlistStocks]);

  // Triggered on reordering watchlist stocks/indices
  const onMoveStock = useCallback(async ({
    oldIndex, newIndex,
  }) => {
    if (oldIndex === newIndex) return setDraggingStockIndex(-1);
    const modifiedStockList = moveElementInArray(sortedStockList, oldIndex, newIndex);
    setActiveWatchlistStocks(modifiedStockList);
    const updatedOrder = modifiedStockList.map((el) => el.id);

    if (activeSort?.id) handleSorting(defaultSortOption);

    try {
      await makeRequest(reorderSecurities(activeWatchlist.id, updatedOrder));
    } catch (err) {
      setActiveWatchlistStocks(sortedStockList);
    }

    return setDraggingStockIndex(-1);
  }, [sortedStockList, setActiveWatchlistStocks, activeSort, handleSorting, makeRequest, activeWatchlist.id]);

  const onSortStart = useCallback(({ index }) => setDraggingStockIndex(index), []);
  const handleEdit = useCallback(() => {
    openModal({
      Component: WatchlistEdit,
      componentProps: {
        pressDelay: 200,
        lockAxis: 'y',
        lockToContainerEdges: true,
      },
      type: MODAL_TYPES.SIDEPANE,
      disableClose: true,
    });
  }, [openModal]);

  useEffect(() => {
    if (mode === 'edit') {
      handleEdit();
    } else {
      closeModal();
    }
  }, [closeModal, handleEdit, mode]);

  return (
    <>
      {!hideWatchlistStockSorter ? (
        <WatchlistStockSorter activeSort={activeSort} handleSorting={handleSorting} />
      ) : null}
      <WatchlistListingWrapper
        lockAxis="y"
        lockToContainerEdges
        helperClass={styles.sortableElement}
        activeSort={activeSort}
        setCurrentWatchlist={setCurrentWatchlist}
        draggingStockIndex={draggingStockIndex}
        updateBeforeSortStart={onSortStart}
        onSortEnd={onMoveStock}
        reqInProgress={inProgress}
        sortedStockList={sortedStockList}
        pressDelay={200}
        handleEdit={handleEdit}
        orderCount={orderCount}
        placeOrderInBasket={placeOrderInBasket}
      />
    </>
  );
};

const ListingLoader = () => (
  <div className={styles.listingLoaderWrapper}>
    <SkeletonLoader type={TYPE.WATCHLIST} direction={DIRECTION.VERTICAL} repeat={5} />
  </div>
);

const WatchlistListingRowWrapper = SortableElement((props) => <WatchlistListingRow {...props} />);
const WatchlistListingWrapper = SortableContainer((props) => <WatchlistListing {...props} />);

export default WithLoader({
  WrappedComponent: WatchlistListingContainer,
  LoadingComponent: ListingLoader,
});
