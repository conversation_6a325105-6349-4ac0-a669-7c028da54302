import React, {
  useContext, useState, useRef, useEffect, useMemo,
} from 'react';
import {
  EXCHANGE, SEGMENT_TYPES,
} from '@utils/enum';
import { classNames as cx } from '@utils/style';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { LoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { useMarketStatus } from '@modules/MarketStatus';
import Lottie from 'lottie-react';
import If from '@common/If';
import WithFullPage from '@common/WithFullPage';
import { useModal } from '@layout/App/ModalContext';
import { MODAL_TYPES } from '@common/Modal/enums';
import useTimerMsg from '@layout/LoggedInLayout/useTimerMsg';
import { mobileBrowser, getFormattedTime } from '@utils';
import streaming from './streaming.json';
import styles from './styles';
import Searchbox from './partials/Searchbox';
import WatchlistListingWithLoader from './partials/WatchlistListing';
import WatchlistSelectorWithLoader, { WatchlistLauncher } from './partials/WatchListSelector';
import ExpandedSection from './partials/WatchListSelector/partials/ExpandedSection';
import AddNewWatchlistSection from './partials/WatchListSelector/partials/AddNewWatchlistSection';

const SearchboxWithFullPageView = WithFullPage(Searchbox);

function useMarketOpenCloseTimer() {
  const {
    openConfig, closeConfig, openMsg, closeMsg,
  } = useContext(LoggedInContext);

  const { time: openTime, setTime: setOpenTime } = useTimerMsg(openConfig || {});
  const { time: closeTime, setTime: setCloseTime } = useTimerMsg(closeConfig || {});

  const { marketStatus } = useMarketStatus();
  const marketState = useMemo(() => marketStatus[SEGMENT_TYPES.CASH][EXCHANGE.NSE].status, [marketStatus]);

  useEffect(() => {
    if (marketState === 1) {
      setOpenTime(0);
    }
    if (marketState === 2) {
      setCloseTime(0);
    }
  }, [marketState, setCloseTime, setOpenTime]);

  const msg = openTime ? openMsg : closeMsg;

  return { openTime, closeTime, msg };
}

function MarketMsg({
  openTime, closeTime, msg, close,
}) {
  return (
    <div className={styles.marketOpenContainer}>
      <div className={styles.streaming}>
        <Lottie animationData={streaming} />
      </div>
      <div className={styles.time}>
        {openTime ? getFormattedTime(openTime) : getFormattedTime(closeTime)}
      </div>
      <div className={styles.message}>
        {msg}
      </div>
      <Icon name={ICON_NAME.CLOSE_POPUP} size={2} className={styles.close} onIconClick={close} />
    </div>
  );
}

const WatchlistSidebar = () => {
  const {
    handleExpansion,
    isExpanded,
    inProgress: fetchWatchlistInProgress,
    failed: fetchWatchlistFailed,
    activeWatchlistReqInProgress,
    activeWatchlistReqFailed,
    setActiveWatchlist,
    expandedState,
    activeStockId,
    toggleStockBookmark,
    openManageWatchlist,
  } = useContext(WatchlistContext);
  const { isSleekCardActive, showSearch } = useContext(LoggedInContext);

  const watchlistSidebarRef = useRef(null);
  const [searchQuery, updateSearchQuery] = useState('');
  const [showMarketMsg, setShowMarketMsg] = useState(true);
  const { openTime, closeTime, msg } = useMarketOpenCloseTimer();
  const { openModal, closeModal } = useModal();

  useEffect(() => {
    if (isExpanded && mobileBrowser()) {
      openModal({
        Component: (props) => (
          <>
            <ExpandedSection {...props} />
            <div className={styles.bottomSection}>
              <AddNewWatchlistSection />
            </div>
          </>
        ),
        componentProps: {
          expandedState,
          activeStockId,
          toggleStockBookmark,
          handleWatchListClick: (watchList) => {
            setActiveWatchlist(watchList);
            closeModal();
          },
          closeExpandedSection: closeModal,
          openManageSection: openManageWatchlist,

          watchlistSidebarRef,
        },
        type: MODAL_TYPES.BOTTOM_SHEET,
        disableClose: true,
        afterCloseCallback: () => {
          handleExpansion(false);
        },
      });
    }
  }, [activeStockId, closeModal, expandedState,
    fetchWatchlistInProgress, handleExpansion, isExpanded,
    openManageWatchlist, openModal, setActiveWatchlist, toggleStockBookmark]);
  return (
    <div
      role="presentation"
      className={cx([styles.sidebarWrapper], {
        [styles.isSleekCardActive]: isSleekCardActive,
      })}
      ref={watchlistSidebarRef}
    >
      {(showMarketMsg && !!(openTime || closeTime)) && (
        <MarketMsg
          openTime={openTime}
          closeTime={closeTime}
          msg={msg}
          close={() => setShowMarketMsg(false)}
        />
      )}
      <If test={!mobileBrowser()}>
        <Searchbox
          searchQuery={searchQuery}
          updateSearchQuery={updateSearchQuery}
        />
      </If>
      <If test={mobileBrowser() && showSearch}>
        <SearchboxWithFullPageView
          searchQuery={searchQuery}
          updateSearchQuery={updateSearchQuery}
        />
      </If>
      <div className={cx([styles.watchlistSection], {
        [styles.isMarketMsgShown]: showMarketMsg && !!(openTime || closeTime),
      })}
      >
        <WatchlistListingWithLoader
          inProgress={fetchWatchlistInProgress || activeWatchlistReqInProgress}
          failed={fetchWatchlistFailed || activeWatchlistReqFailed}
        />
        <div className={styles.watchlistSelectorWrapper}>
          <If test={mobileBrowser()}>
            <WatchlistLauncher />
          </If>
          <If test={!mobileBrowser()}>
            <WatchlistSelectorWithLoader
              watchlistSidebarRef={watchlistSidebarRef}
              inProgress={fetchWatchlistInProgress}
            />
          </If>

        </div>
      </div>
      <If test={!mobileBrowser()}>
        <div
          role="presentation"
          className={cx([styles.fadeOverlay], {
            [styles.fadeOverlayVisible]: isExpanded,
          })}
          onClick={() => handleExpansion()}
        />
      </If>
    </div>
  );
};

export default WatchlistSidebar;
