@import '../../../../../styles/main';

.navSectionWrapper {
  @include respond(phone) {
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 var(--container-box-shadow);
    background-color: $default;
    margin: 1rem;
  }

  .parentName {
    color: $grey1;
    padding: 2rem 1.2rem .5rem;
    display: flex;

    @include typography(h6, semibold);

    @include respond(phone) {
      @include typography(h4, upperbold);
    }

    .icon {
      margin-right: 1rem;

      @include respond(phone) {
        display: none;
      }
    }
  }

  .subSection {
    border-bottom: .1rem solid $grey4;

    a {
      text-decoration: none;
    }
  }

  .modeSwitch {
    border-bottom: .1rem solid $grey4;
  }

  .sections {
    .sectionInfo {
      padding: 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
    }

    .sectionName {
      color: $grey1;
      flex: .9;

      @include typography(h6);

      @include respond(phone) {
        color: $grey2;
      }
    }

    .activeSection {
      color: $secondary-color;

      @include typography(h6, semibold);
    }
  }
}

.version {
  color: $grey3;
  float: right;

  @include typography(h6);
}

.hideMarginPledge {
  display: none;
}

.logoutSection {
  @include respond(phone) {
    margin-bottom: 10rem;
  }
}

.subSectionVisibility {
  @include respond(phone) {
    display: none;
  }
}

.selectChartTextInfoContainer {
  display: flex;
  align-items: center;
  cursor: default;
}

.popper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 20.8rem;
  height: 10.2rem;
  bottom: 3rem;
  right: -12.5rem;
  border-radius: 1rem;
  background-color: $default;
  box-shadow: 0 0 1.2rem 0 $box-shadow-color;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 34%;
    border-width: .6rem;
    border-style: solid;
    border-color: $default transparent transparent;
  }
}

.selectChartsInfoText {
  color: $grey1;
  margin: 0 2rem;

  @include typography(h7);
}

.iconStyles {
  margin: .5rem;
  cursor: pointer;
}

.iconClose {
  margin: 1rem 1rem 0;
  cursor: pointer;
}

.boldText {
  @include typography(h7, bold);
}

.stockInstructionsMarginPledgeContainer {
  @include respond(desktop) {
    display: none;
  }
}

.infoIcon {
  padding-right: 20rem;
  padding-top: .1rem;
}
