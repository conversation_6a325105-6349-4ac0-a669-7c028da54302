import React, {
  useMemo,
} from 'react';
import { Link, useLocation } from 'react-router-dom';
import { mobileBrowser } from 'utils';
import { THEMES, ROUTE_NAME } from '@utils/enum';
import Icon from '@common/Icon';
import If from '@common/If';
import {
  classNames as cx,
} from '@utils/style';
import { ICON_NAME } from '@common/Icon/enums';
import Popper from '@common/Popper';
import Toggle from '@common/Toggle';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import GAElement from '@common/GAElement';
import { PROFILE_PAGE_GA } from '@constants/ga-events';
import { sendEvent, sendPageEvent } from '@service/AnalyticsService';
import { useIrData } from '@layout/App/UserContext';
import { useModal } from '@layout/App/ModalContext';
import { MODAL_TYPES } from '@common/Modal/enums';
import MobileAppDownloadView from '@common/MobileAppDownloadView';
import styles from './index.scss';
import { THEME_MODES, TRADING_VIEW_CHARTS, SELECT_CHART_MODES_INFO } from './enums';
import Routes from '../../../../../routes';
import FnOSearchToggle from './partials/FnOSearchToggle';
import StickyOrderToggle from './partials/StickyOrderToggle/index';

const BuybacksDelistingOfs = 'BuybacksDelistingOfs';
function NavSections({
  parent,
}) {
  const location = useLocation();
  const {
    theme, setTheme, selectedChart, setSelectedChartType,
  } = useLoggedInContext();
  const redirectToBuybackDelisting = () => {
    sendPageEvent({
      // openScreen Events || Pulse Events
      eventDetails: {
        screenName: '/corporateactions_land',
        vertical_name: 'stocks',
        event_action: 'open_screen',
      },
    });
  };
  const activeTheme = useMemo(() => THEME_MODES.find((item) => item.id === theme), [theme]);
  const { logout, isInvestmentReadyFO } = useIrData();

  const stopPropagation = (e) => e.stopPropagation();
  const { openModal } = useModal();
  const showModal = (heading) => {
    const imgSrc = heading.includes('Instructions') ? ICON_NAME.STOCK_INSTRUCTION : ICON_NAME.MARGIN_PLEDGE_MOBILE_VIEW;
    openModal({
      Component: MobileAppDownloadView,
      componentProps: {
        heading,
        imgSrc,
      },
      type: MODAL_TYPES.MobileAPPDOWNLOAD_POPUP,
    });
  };
  return (
    <div className={styles.navSectionWrapper}>
      <div className={styles.parentName}>
        <Icon name={parent.icon} size={4} className={styles.icon} />
        {parent.name}
      </div>
      <div className={styles.sections}>
        {
          parent.id === 'settings'
          && (
            <>
              {
                isInvestmentReadyFO
                && (
                  <FnOSearchToggle />
                )
              }
              <div className={`${styles.sectionInfo} ${styles.modeSwitch}`}>
                <span className={styles.sectionName}>
                  Dark Mode
                </span>
                <Toggle
                  options={THEME_MODES}
                  activeId={activeTheme.id}
                  mode="toggle"
                  showLabel={false}
                  onClickCb={(mode) => {
                    setTheme(mode.id);
                    sendEvent({
                      event_category: PROFILE_PAGE_GA.DARK_MODE_CLICKED.EVENT_CATEGORY,
                      event_action: PROFILE_PAGE_GA.DARK_MODE_CLICKED.EVENT_ACTION,
                      event_label: PROFILE_PAGE_GA.DARK_MODE_CLICKED.EVENT_LABEL(
                        mode.id === THEMES.DARK ? 'yes' : 'no',
                      ),
                    });
                  }}
                />
              </div>
              <If test={!mobileBrowser()}>
                <div className={`${styles.sectionInfo} ${styles.modeSwitch}`}>
                  <div className={`${styles.sectionName} ${styles.selectChartTextInfoContainer}`}>
                    {TRADING_VIEW_CHARTS}
                    <Popper
                      iconName={ICON_NAME.INFO_BLUE}
                      activeIconName={ICON_NAME.INFO_BLUE}
                      iconSize={2.8}
                      showDropDownIcon={false}
                      iconStyles={styles.iconStyles}
                      className={styles.popper}
                      closeIcon
                      closeIconClassName={styles.iconClose}
                      closeIconSize={1.2}
                    >
                      <div
                        onClick={stopPropagation}
                        role="presentation"
                      >
                        <div className={styles.selectChartsInfoText}>
                          <span className={styles.boldText}>{SELECT_CHART_MODES[0].label}</span>
                          {` ${SELECT_CHART_MODES_INFO[0]} `}
                          <span className={styles.boldText}>{SELECT_CHART_MODES[1].label}</span>
                          {` ${SELECT_CHART_MODES_INFO[1]} `}
                        </div>
                      </div>
                    </Popper>
                  </div>
                  <Toggle
                    options={SELECT_CHART_MODES}
                    activeId={selectedChart}
                    mode="toggle"
                    showLabel={false}
                    onClickCb={(mode) => {
                      setSelectedChartType(mode.id);
                    }}
                  />
                </div>
                <StickyOrderToggle />
              </If>
            </>
          )
        }
        {parent.sections.map((section, key) => (
          <div
            className={cx(styles.subSection, {
              [styles.subSectionVisibility]: (section.name === 'Margin against stocks' || section.name === 'Stock Instructions'),
            })}
            key={`${key}${section.id}`}
          >
            <GAElement
              gaEventCategory={PROFILE_PAGE_GA.NAV_OPTIONS_CLICKED.EVENT_CATEGORY}
              gaEventAction={PROFILE_PAGE_GA.NAV_OPTIONS_CLICKED.EVENT_ACTION}
              gaEventLabel={PROFILE_PAGE_GA.NAV_OPTIONS_CLICKED.EVENT_LABEL(section.name.toLowerCase())}
            >
              <Link
                to={section.link + location.search}
                target={section.isExternalLink && '_blank'}
                rel={section.isExternalLink && 'noopener noreferrer'}
                onClick={() => {
                  if (section.id === 'bankAccount') {
                    sendPageEvent({
                      eventDetails: {
                        screenName: 'bankaccount',
                        eventType: 'openScreen',
                        event_action: 'open_screen',
                      },
                    });
                  } else if (section.id === 'marginPledge') {
                    sendEvent({
                      event_category: 'marginpledge',
                      event_action: 'margin pledge_entry_accountssection',
                      event_label: 'marginpledge',
                      vertical_name: 'stocks',
                      screenName: '/marginpledge',
                    });
                  } else if (section.id === BuybacksDelistingOfs) {
                    sendEvent({
                      event_category: 'buyback',
                      event_action: 'buyback_accountsection_clicked',
                      event_label: 'buyback',
                      vertical_name: 'stocks',
                      screenName: '/buyback',
                    });
                  }
                  return section.id === BuybacksDelistingOfs ? redirectToBuybackDelisting() : null;
                }}
              >
                <div className={styles.sectionInfo}>
                  <span className={
                    cx(styles.sectionName,
                      { [styles.activeSection]: location?.pathname.includes(section.link) })
                  }
                  >
                    {section.name}
                  </span>
                  {section.id === 'marginPledge' || section.id === 'MarginTradeFunding' || section.id === 'MTFStockList' ? <Icon name={ICON_NAME.NEW} /> : null}
                  {section.isExternalLink
                    ? <Icon name={ICON_NAME.LINK} size={2} />
                    : <Icon name={ICON_NAME.OUTLINE_ARROW_RIGHT} size={3} />}
                </div>
              </Link>
            </GAElement>
          </div>
        ))}
        {
          parent.id === 'myAccount' && (
            <div className={styles.stockInstructionsMarginPledgeContainer}>
              <div className={styles.subSection}>
                <div className={styles.sectionInfo}>
                  <span
                    className={
                    cx(styles.sectionName)
                  }
                    onClick={() => showModal(parent.sections[3].name)}
                    role="presentation"
                  >
                    {parent.sections[3].name}
                  </span>
                  <Icon name={ICON_NAME.OUTLINE_ARROW_RIGHT} size={3} />
                </div>
              </div>
              <div className={styles.subSection}>
                <div className={styles.sectionInfo}>
                  <span
                    className={
                    cx(styles.sectionName)
                  }
                    onClick={() => showModal('Margin Against Stocks')}
                    role="presentation"
                  >
                    {parent.sections[4].name}
                  </span>
                  <Icon name={ICON_NAME.OUTLINE_ARROW_RIGHT} size={3} />
                </div>
              </div>
            </div>
          )
        }
        {
          parent.id === 'settings'
          && (
            <div className={cx([styles.sectionInfo, styles.logoutSection])}>
              <span
                className={styles.sectionName}
                onClick={logout.bind(null, { returnUrl: Routes[ROUTE_NAME.HOME].url })}
                role="presentation"
              >
                Logout
              </span>
              <span className={styles.version}>Version 2.0.0</span>
            </div>
          )
        }
      </div>
    </div>
  );
}

export default NavSections;
