import { THEMES } from '@utils/enum';

const THEME_MODES = [{
  label: 'Light',
  id: THEMES.LIGHT,
}, {
  label: 'Dark',
  id: THEMES.DARK,
}];

const STICKY_ORDER_MODES = [
  {
    label: 'Disabled',
    id: 'DISABLED',
  },
  {
    label: 'Enabled',
    id: 'ENABLED',
  }];

const STICKY_ORDER_STATUS = {
  ENABLED: true,
  DISABLED: false,
};

const FNO_STATUS_TYPES = {
  NOACCESS: 'NOACCESS',
  INQUEUE: 'INQUEUE',
  GRANTED: 'GRANTED',
};

const FNO_SEARCH = [{
  label: 'Disabled',
  id: FNO_STATUS_TYPES.NOACCESS,
}, {
  label: 'Enabled',
  id: FNO_STATUS_TYPES.GRANTED,
}];

const TRADING_VIEW_CHARTS = 'Trading View Charts';

const STICKY_ORDER_TITLE = 'Sticky Order';

const STICKY_ORDER_BLOG_LINK = 'https://www.paytmmoney.com/blog/introducing-sticky-window-for-orders/';
const SELECT_CHART_MODES_INFO = ['is the default library for charts. You can view', 'Charts by switching the toggle on.'];

export {
  THEME_MODES,
  FNO_STATUS_TYPES,
  FNO_SEARCH,
  TRADING_VIEW_CHARTS,
  SELECT_CHART_MODES_INFO,
  STICKY_ORDER_TITLE,
  STICKY_ORDER_MODES,
  STICKY_ORDER_STATUS,
  STICKY_ORDER_BLOG_LINK,
};
