import React, {
  useState, useCallback, useRef,
  useContext,
} from 'react';
import { classNames as cx } from '@utils/style';
import PropTypes from 'prop-types';
import {
  EXCHANGE, TRANSACTION_TYPES, ORDER_TYPES, SEGMENT_TYPES,
} from '@utils/enum';
import { TYPE } from '@modules/OrderFlow/CDSLAuthorisation/Enum';
import { MODAL_TYPES } from '@common/Modal/enums';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { useOrderflowApi } from '@common/UseApi';
import { getMessage } from '@common/UseApi/useGetApi';
import CONSTANTS from '@common/UseApi/enums';
import { useMarketStatus } from '@modules/MarketStatus';
import useEDSLAuth from '@modules/OrderFlow/CDSLAuthorisation/useCDSLauth';
import { orderUpdateSubject } from '@common/UseRefreshPage/useRefreshPage';
import { isAMOorder } from '@pages/Orders/TodayOrders/OrderDataTable/partials/TableRow';
import { ORDER_STATUS } from '@pages/Orders/TodayOrders/OrderDetails/enums';
import { useDidUpdateEffect } from '@utils/react';
import If from '@common/If';
import OrderCharges from '@modules/OrderCharges';
import { getCustomOrderType } from '@modules/OrderCharges/utils';
import { button } from '@commonStyles';
import Header from '../Header';
import Note from '../Note';
import AdvancedOptions from '../AdvancedOptions';
import BasicFields from '../Fields/BasicFields';
import { getAdvaceOptionProps } from '../useAdvancedOptions';
import useBasicFields, { DEFAULT_TICK, getFields } from '../useBasicFields';
import {
  getOrderType, productTypeLabels, handleMarketStatusChange, handleOrderStatusChange, MARKET_STATUS_ERROR_CODES,
} from '../util';
import styles from './index.scss';
import { modifyOrder } from './api';
import useOrderType from '../useOrderType';
import { HoldingsContext } from '@modules/Holdings';

export const SubHeader = ({ transactionType, productType }) => (
  <div className={styles.children}>
    <div className={cx(styles.typeBtn,
      {
        [styles.buy]: transactionType === TRANSACTION_TYPES.BUY,
        [styles.sell]: transactionType === TRANSACTION_TYPES.SELL,
      })}
    >
      {transactionType === TRANSACTION_TYPES.BUY ? 'Buy' : 'Sell'}
    </div>
    <div className={styles.productType}>
      {productTypeLabels[productType]}
    </div>
  </div>
);

function ModifyOrder({
  isin,
  name,
  exchange,
  securityId,
  initialQuantity,
  initialPrice,
  initialTriggerPrice,
  legNo,
  algoOrderNo,
  orderType,
  tradedQuantity,
  transactionType,
  productType,
  extraParams,
  handleClose,
  status,
  tickSize,
  lotSize,
  segment = SEGMENT_TYPES.CASH,
  dragAreaRef,
  instrument_type,
  ext_info,
}) {
  const { marketStatus } = useMarketStatus();
  const marketConfig = marketStatus[segment][exchange];
  const allowedOptions = marketConfig[productType];
  const { makeRequest, inProgress, failed } = useOrderflowApi();
  const {
    isLimit,
    isSL,
    toggleIsLimit,
    toggleIsSL,
  } = useOrderType({
    orderType, productType, exchange, isModify: true, segment,
  });
  const isSLAllowed = allowedOptions?.indexOf(ORDER_TYPES.SL) > -1 && allowedOptions?.indexOf(ORDER_TYPES.SLM) > -1;
  const [isModified, setIsModified] = useState(false);
  const [authReqd, setAuthReqd] = useState({ authFlag: false });
  const [edisErrorCode, setEdisErrorCode] = useState(null);
  const [showCharges, setShowCharges] = useState(false);
  const { holdings: { holdingMap } } = useContext(HoldingsContext);
  const holding = holdingMap?.[isin];

  const { addToast } = useToast();

  const basicFields = useBasicFields({
    transactionType,
    isSL,
    initialQuantity: initialQuantity - tradedQuantity,
    initialPrice,
    initialTriggerPrice,
    isLimit,
    tradedQuantity,
    exchange,
    securityId,
    segment,
    productType,
    tick: tickSize || DEFAULT_TICK,
    lotSize,
  });

  const orderModify = async (eDisPayload = {}) => {
    try {
      await makeRequest(modifyOrder({
        exchange,
        product: productType,
        txn_type: transactionType,
        security_id: securityId,
        isSL,
        isLimit,
        extraParams,
        ...basicFields,
        legNo,
        algoOrderNo,
        eDisPayload,
        off_mkt_flag: isAMOorder(status),
        segment,
        ext_info,
      }));
      handleClose();
      orderUpdateSubject.next({
        txn_type: transactionType,
        product: productType,
        status: ORDER_STATUS.O_MODIFIED,
      });
    } catch (err) {
      if (MARKET_STATUS_ERROR_CODES.indexOf(err?.data?.[0]?.oms_error_code) === -1) {
        const message = getMessage(err) || CONSTANTS.ERROR_MESSAGE;
        if (err?.error_code === 'PM-0029') {
          if (edisErrorCode) {
            setAuthReqd({ qty: err?.data[0]?.additional_qty, authFlag: true });
            setEdisErrorCode(null);
            addToast(message, APPEARANCE_TYPES.FAIL);
          } else {
            setEdisErrorCode(err?.error_code);
          }
        } else {
          addToast(message, APPEARANCE_TYPES.FAIL);
        }
      } else {
        handleOrderStatusChange(err, handleClose);
        handleMarketStatusChange(err);
      }
    }
  };

  const { showCDSLView, Component, edslAuth } = useEDSLAuth({
    callBack: orderModify,
    failed,
    isin,
    quantity: basicFields.quantity,
    edisErrorCode,
    isEdisFailure: authReqd?.authFlag,
  });

  const ltpValue = useRef(null);
  const updateLtp = useRef(true);

  const ltpCallback = (val) => {
    if (updateLtp.current) {
      ltpValue.current = val;
    }
  };

  const openChargesInfo = useCallback(() => {
    updateLtp.current = !updateLtp.current;
    setShowCharges(!showCharges);
  }, [showCharges]);

  useDidUpdateEffect(() => {
    setIsModified(true);
  }, [basicFields.quantity, basicFields.triggerPrice, basicFields.price, isLimit, isSL]);

  const disableBtn = inProgress || basicFields.disableBtn || !isModified;

  return (
    <div className={styles.container}>
      <If test={showCharges}>
        <OrderCharges
          exchange={exchange}
          qty={basicFields?.quantity}
          instrumentType={instrument_type}
          productType={productType}
          transactionType={transactionType}
          isSL={isSL}
          ltp={ltpValue.current}
          price={basicFields?.price}
          triggerPrice={basicFields?.triggerPrice}
          orderType={getCustomOrderType(isLimit, isSL, productType)}
          containerStyle={styles.charges}
          handleCloseClick={openChargesInfo}
          isModify
        />
      </If>
      {
        showCDSLView ? (
          <Component
            onSuccess={orderModify}
            inProgress={inProgress}
            exchange={exchange}
            securityId={securityId}
            quantity={initialQuantity}
            isin={isin}
            name={name}
            note={marketConfig.message}
            transactionType={transactionType}
            isLimit={isLimit}
            toggleIsLimit={toggleIsLimit}
            isSL={isSL}
            basicFields={basicFields}
            isSLAllowed={isSLAllowed}
            productType={productType}
            toggleIsSL={toggleIsSL}
            disableBtn={disableBtn}
            type={TYPE.MODIFY}
            dragAreaRef={dragAreaRef}
            isEdisReqd={authReqd}
          />
        )
          : (
            <>
              <Header
                name={name}
                exchange={exchange}
                securityId={securityId}
                segment={segment}
                lotSize={lotSize}
                ref={dragAreaRef}
                ltpCallback={ltpCallback}
              >
                <SubHeader transactionType={transactionType} productType={productType} />
              </Header>
              <div className={styles.content}>
                <BasicFields
                  {...getFields({
                    ...basicFields, isLimit, isSL, toggleIsLimit, segment,
                  }, true, legNo === '1')}
                  onEnter={disableBtn ? () => {} : edslAuth}
                />
                <button
                  className={cx([button.btn, button.btnLarge, button.primaryBtnFill], {
                    [button.disabledBtn]: disableBtn,
                  })}
                  disabled={disableBtn}
                  onClick={edslAuth}
                >
                  MODIFY
                </button>
                <Note
                  holding={holding}
                  segment={segment}
                  exchange={exchange}
                  securityId={securityId}
                  transactionType={transactionType}
                  instrumentType={instrument_type}
                  isSL={isSL}
                  isLimit={isLimit}
                  {...basicFields}
                  openChargesInfo={openChargesInfo}
                  isModify
                />
                <AdvancedOptions
                  {...getAdvaceOptionProps({
                    isSL, toggleIsSL, isSLAllowed, productType,
                  }, true, legNo === '1')}
                  orderType={getOrderType(isLimit, isSL)}
                />
              </div>
            </>
          )
      }
    </div>
  );
}

ModifyOrder.propTypes = {
  name: PropTypes.string.isRequired,
  exchange: PropTypes.oneOf(Object.values(EXCHANGE)).isRequired,
  transactionType: PropTypes.oneOf(Object.values(TRANSACTION_TYPES)).isRequired,
  initialQuantity: PropTypes.number,
  tradedQuantity: PropTypes.number.isRequired,
  initialPrice: PropTypes.number,
  initialTriggerPrice: PropTypes.number,
  modalType: PropTypes.oneOf(Object.values(MODAL_TYPES)).isRequired,
};

ModifyOrder.defaultProps = {
  initialQuantity: null,
  initialPrice: null,
  initialTriggerPrice: null,
};

export default ModifyOrder;
