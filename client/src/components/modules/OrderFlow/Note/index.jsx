import React, {
  useCallback, useEffect, useRef, useState, useContext,
} from 'react';
import { getAvailableFundsSummary } from '@pages/ManageFunds/api';
import Lottie from 'lottie-react';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import {
  PRODUCT_TYPES, TRANSACTION_TYPES, EXIT,
} from '@utils/enum';
import Icon from '@common/Icon';
import { UserContext } from '@layout/App/UserContext';
import useGetApi from '@common/UseApi/useGetApi';
import { ICON_NAME } from '@common/Icon/enums';
import { classNames as cx } from '@utils/style';
import ChargesBtn from '@modules/OrderCharges/partials/ChargesBtn';
import { formatPrice } from '@utils';
import styles from './index.scss';
import useMarginRequired from './useMarginRequired';
import DotAnimation from './dotanimation.json';
import { FUNDS } from './config';
import Tooltip from '../PlaceOrder/ProfitTab/ToolTip';
import { getMtfPositions } from '../../Positions/positionsApi';
import { renderMessage } from '../../../pages/Company/partials/InvestCare';

function Note({
  productType, holding, transactionType, segment, instrumentType, openChargesInfo, netQty, positionData, isin, isLimit,
  isModify = false, ...props
}) {
  const { makeRequest } = useGetApi();
  const { webConfig: { mpp: mppRaw } } = useContext(UserContext);
  const mppParsed = JSON.parse(mppRaw || null);
  const { miscConfig, equityCardInfo } = useLoggedInContext();
  const fundsRequiredInfo = miscConfig.data?.info_message?.delivery_buy_funds_required?.message;
  const [funds_summary, setFundsSummary] = useState({});
  const isDeliverySell = (productType === PRODUCT_TYPES.DELIVERY || productType === PRODUCT_TYPES.MTF)
  && transactionType === TRANSACTION_TYPES.SELL;
  const [isEmptyPositions, setisEmptyPositions] = useState(true);
  const [mtfPositions, setMtfPositions] = useState([]);

  const getAvailableData = useCallback(async () => {
    const body = {
      transaction_type: transactionType, product_type: productType, instrument_type: instrumentType, is_sgb: false,
    };
    try {
      const { data } = await makeRequest(getAvailableFundsSummary(body));
      setFundsSummary(data);
    } catch (error) { }
  }, [instrumentType, makeRequest, productType, transactionType]);

  useEffect(() => {
    if (isDeliverySell) return;
    getAvailableData();
  }, [getAvailableData, isDeliverySell]);
  const {
    quantity, posControlType, initialTransactionType, initialProductType,
  } = props;

  const toCallMarginApi = useRef(posControlType === EXIT
    // eslint-disable-next-line radix
    ? parseInt(quantity) > Math.abs(parseInt(netQty)) : true);

  const card = [];
  useEffect(() => {
    if (productType === PRODUCT_TYPES.MTF && transactionType === TRANSACTION_TYPES.SELL) {
      getMtfPositions().then(({ data: mtfPositionsList }) => {
        setMtfPositions(mtfPositionsList.filter((item) => item.isin === isin)[0]);
        setisEmptyPositions(!mtfPositionsList.filter((item) => item.isin === isin).length);
      });
    }
  }, [isin, productType, transactionType]);

  useEffect(() => {
    if (transactionType !== initialTransactionType || initialProductType !== productType) {
      toCallMarginApi.current = true;
      return;
    } if (posControlType === EXIT) {
      // eslint-disable-next-line radix
      toCallMarginApi.current = parseInt(quantity) > Math.abs(parseInt(netQty));
    }
  }, [initialProductType, initialTransactionType, netQty, posControlType, productType, quantity, transactionType]);

  const { marginRequired, inProgress, refreshMarginRequired } = useMarginRequired({
    productType,
    segment,
    transactionType,
    toCallMarginApi,
    netQty,
    isLimit,
    ...props,
  });

  card.push(
    <>
      <div
        className={`${styles.fundsLabel} ${styles.fundsLabelHighlighted}`}
      >
        {FUNDS.available_funds}
        <span className={styles.availableCashValue}>
          {`${funds_summary?.available_cash < 0 ? '-₹' : '₹'} ${formatPrice(funds_summary?.available_cash)}`}
        </span>
      </div>
      <div
        className={cx(styles.fundsLabel)}
      >
        {FUNDS.trade_balance}
        <span className={styles.tradeBalanceValue}>
          {`${funds_summary?.trade_balance < 0 ? '-₹' : '₹'} ${formatPrice(funds_summary?.trade_balance)}`}
        </span>
      </div>
      <br />
    </>,
  );
  card.push(renderMessage([equityCardInfo?.available_cash]));

  if ((!holding || holding?.remaining_quantity === 0)
    && transactionType === TRANSACTION_TYPES.SELL
    && productType === PRODUCT_TYPES.DELIVERY) {
    return (
      <div className={styles.note}>
        <div className={styles.emptyState}>
          <Icon
            name={ICON_NAME.HOLDINGS}
            size={3}
          />
          <div>
            No Holding available
          </div>
        </div>
      </div>
    );
  }
  if (isEmptyPositions && productType === PRODUCT_TYPES.MTF && transactionType === TRANSACTION_TYPES.SELL
    && !isModify) {
    return (
      <div className={styles.note}>
        <div className={styles.emptyState}>
          <Icon
            name={ICON_NAME.HOLDINGS}
            size={3}
          />
          <div>
            No Open Positions available
          </div>
        </div>
      </div>
    );
  }

  const MarginReqd = ({ marginStyle }) => (
    <span className={cx(styles.value, { [marginStyle]: [marginStyle] })}>
      {marginRequired ? `₹${formatPrice(marginRequired)}` : '-'}
    </span>
  );

  const showHoldingCount = () => (
    <div className={cx(styles.item, {
      [styles.centerDiv]: mtfPositions?.net_qty === undefined,
    })}
    >
      <Icon
        name={ICON_NAME.HOLDINGS}
        className={styles.holdingIcon}
        size={3}
      />
      <div className={styles.holdingsText}>
        {productType === PRODUCT_TYPES.MTF
          ? `${mtfPositions?.net_qty || '-'}` : `${holding.remaining_quantity} x ${formatPrice(holding.cost_price)}`}
      </div>
    </div>
  );

  return (
    <div className={styles.note}>
      <div className={cx([], {
        [styles.flexColumn]: isDeliverySell,
      })}
      >
        <div className={cx([styles.item, styles.itemLeft], {
          [styles.flexRow]: isDeliverySell,
        })}
        >
          {isDeliverySell ? <MarginReqd marginStyle={styles.marginSell} /> : null}
          <span className={cx([styles.label, styles.paddingBottom])}>
            {isDeliverySell ? 'Approx Value' : 'Funds Required'}
            {
              (fundsRequiredInfo && productType === PRODUCT_TYPES.DELIVERY && transactionType === TRANSACTION_TYPES.BUY)
              || (!isLimit)
                ? (
                  <Tooltip
                    showHeader={false}
                    customTooltipClass={styles.cutomTooltip}
                    customHeaderClass={styles.customHeader}
                    tooltipMessage={!isLimit && mppParsed?.order_info ? mppParsed?.order_info : fundsRequiredInfo}
                    customMessageClass={!isLimit && mppParsed?.order_info ? styles.mppCustom : styles.customMessage}
                    customTooltipBubble={styles.customTooltipBubble}
                  />
                ) : null
            }
          </span>
          <div className={styles.flexRow}>
            {
            inProgress
              ? (
                <Lottie
                  animationData={DotAnimation}
                  style={
                    {
                      width: '2rem',
                      marginTop: '-.2rem',
                    }
                  }
                />
              )
              : (
                <>
                  {
                    (productType !== PRODUCT_TYPES.DELIVERY
                      && !(productType === PRODUCT_TYPES.MTF && transactionType === TRANSACTION_TYPES.SELL))
                    && toCallMarginApi.current
                      ? (
                        <Icon
                          name={ICON_NAME.REFRESH}
                          className={styles.refreshIcon}
                          onIconClick={refreshMarginRequired}
                          size={2}
                        />
                      ) : null
                  }
                  {!isDeliverySell && toCallMarginApi.current ? <MarginReqd /> : null}
                  {!toCallMarginApi.current && !isDeliverySell ? '-' : ''}
                  {!isDeliverySell ? (
                    <ChargesBtn
                      openChargesInfo={openChargesInfo}
                      isPlusIcon
                      plusIconStyle={styles.plusIcon}
                      btnContainer={styles.chargeBtn}
                      ctaText="Order Charges"
                      arrowStyle={styles.arrowIcon}
                      rotateArrowIcon
                    />
                  ) : null }
                </>
              )
            }
          </div>
        </div>
        {isDeliverySell ? (
          <ChargesBtn
            openChargesInfo={openChargesInfo}
            btnContainer={styles.chargeBtn}
            ctaText="Order Charges"
            arrowStyle={styles.arrowIcon}
            rotateArrowIcon
          />
        ) : null}
      </div>
      {(isDeliverySell || (productType === PRODUCT_TYPES.MTF && !isEmptyPositions)) ? (
        showHoldingCount()
      ) : (
        <div className={styles.item}>
          <div className={styles.columnItem}>
            <div className={styles.availableFundsWrapper}>
              <span className={cx([styles.availableFundlabel, styles.paddingBottom])}>Available Funds</span>
              {funds_summary?.trade_balance
                ? (
                  <Tooltip
                    showHeader={false}
                    customTooltipClass={styles.availableCashTooltip}
                    customHeaderClass={styles.customHeader}
                    tooltipMessage={card}
                    customMessageClass={styles.customMessage}
                    customTooltipBubble={styles.availableCashTooltipBubble}
                  />
                ) : null}
            </div>
            <span className={styles.value}>
              {`${funds_summary?.available_funds < 0 ? '-₹' : '₹'} ${formatPrice(funds_summary?.available_funds)}`}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default Note;
