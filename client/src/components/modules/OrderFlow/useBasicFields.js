import {
  useEffect, useState, useContext,
} from 'react';
import {
  TRANSACTION_TYPES, ORDER_TYPES, PRODUCT_TYPES, SEGMENT_TYPES, FIELDS_LABLES, NO_BLOCKING_ERROR_CASES,
} from '@utils/enum';
import { UserContext } from '@layout/App/UserContext';
import { getCustomOrderType } from '@modules/OrderCharges/utils';
import useResultFromFeed from '@common/useResultFromFeed';
import { RESPONSE_TYPES, REQUEST_TYPES } from '@service/dataConfig';
import { useCallbackForEvents, useDidUpdateEffect } from '@utils/react';
import { useMarketStatus } from '@modules/MarketStatus';
import { log } from '@layout/App/api';
import { roundValue, formatPrice, emptyFn } from '@utils';
import useQuantityInput from '../../common/useQuantityInput';
import usePriceInput from '../../common/usePriceInput';
import { CUSTOM_ORDER_TYPES } from '../OrderCharges/enums';

export const DEFAULT_TICK = 0.05;
export const DEFAULT_LOT_SIZE = 1;

export function useCLValidation(lcl, ucl, priceDprBlockingFlag) {
  return useCallbackForEvents((value, setError) => {
    if (ucl === 0 || lcl === 0) return;
    if (Number(value) < lcl && NO_BLOCKING_ERROR_CASES[1] !== priceDprBlockingFlag) setError(`Should be more than Rs ${formatPrice(lcl - 0.01)}`);
    else if (Number(value) > ucl && NO_BLOCKING_ERROR_CASES[1] !== priceDprBlockingFlag) setError(`Should be less than Rs ${formatPrice(ucl + 0.01)}`);
  }, [lcl, ucl]);
}

const requiredFeedResponse = [RESPONSE_TYPES.TRADE, RESPONSE_TYPES.CIRCUIT_LIMIT];
function useBasicFields({
  transactionType, isSL, isLimit,
  initialQuantity, initialPrice, initialTriggerPrice,
  exchange, securityId, segment, productType, tick = DEFAULT_TICK, lotSize = DEFAULT_LOT_SIZE,
}) {
  const {
    getExchangeConfig,
    setNonBlockerError,
    nonBlockerError,
    webConfig,
  } = useContext(UserContext);
  const [orderConfig, setOrderConfig] = useState(getExchangeConfig(segment, exchange));
  const [flagDprLog, setFlagDprLog] = useState(false);

  useEffect(() => {
    setOrderConfig(getExchangeConfig(segment, exchange));
  }, [exchange, getExchangeConfig, segment]);
  const [stockData, limitData] = useResultFromFeed(
    REQUEST_TYPES.STOCK,
    requiredFeedResponse,
    { exchange, securityId, segment },
  );
  const ltp = Number(roundValue(stockData?.lastTradePrice));
  const ucl = Number(roundValue(limitData?.ucl));
  const lcl = Number(roundValue(limitData?.lcl));
  const {
    qty: quantity, onInput: setQuantity, qtyError: quantityError, onKeyDown, validateInput,
  } = useQuantityInput({
    defaultValue: initialQuantity || lotSize,
    max: orderConfig?.MaxOrderQty,
    multipleOf: lotSize,
    min: lotSize,
  });

  useEffect(() => {
    if (webConfig.enableDprLogging && !flagDprLog && (ltp > ucl || ltp < lcl)) {
      setFlagDprLog(true);
      log([{
        level: 'error',
        key: 'ltp-out-of-circuit-web',
        timestamp: new Date().toISOString(),
        version: window.pmVersion,
        data: JSON.stringify({
          dprLowerCircuit: lcl,
          dprUpperCircuit: ucl,
          ltp,
        }),
      }]);
    }
  }, [ltp, ucl, lcl, flagDprLog, webConfig.enableDprLogging]);

  useEffect(() => {
    validateInput();
  }, [exchange, validateInput]);

  const {
    formattedPrice: formattedTriggerPrice,
    price: triggerPrice,
    onInput: setTriggerPrice,
    priceError: triggerPriceError,
    setPriceError: setTriggerPriceError,
    onKeyDown: onTriggerPriceKeyDown,
  } = usePriceInput({
    defaultValue: initialTriggerPrice,
    multipleOf: tick,
    enable: isSL,
    allowedDecimals: 2,
  });

  const {
    formattedPrice,
    price,
    onInput: setPrice,
    priceError,
    setPriceError,
    onKeyDown: onPriceKeyDown,
  } = usePriceInput({
    defaultValue: initialPrice,
    multipleOf: tick,
    enable: isLimit,
    allowedDecimals: 2,
    lcl,
    ucl,
  });

  const [priceTPError, setPriceTPError] = useState('');
  const { marketStatus } = useMarketStatus();
  const { priceDprBlockingFlag } = useContext(UserContext);

  const allowedOptions = marketStatus[segment][exchange][productType];

  const [isLtpExists, setIsLtpExist] = useState(!!ltp);

  const clValidation = useCLValidation(lcl, ucl, priceDprBlockingFlag);

  const ltpValidation = useCallbackForEvents((value, setError, type) => {
    if ((type === TRANSACTION_TYPES.BUY) && Number(value) <= ltp) setError('Should be greater than live Price');
    else if ((type === TRANSACTION_TYPES.SELL) && Number(value) >= ltp) setError('Should be lower than live Price');
  }, [ltp]);

  const ltpValidationForPrice = useCallbackForEvents((value, setError, type) => {
    if ((type === TRANSACTION_TYPES.BUY) && Number(value) <= ltp) setError('Buy Price should be above Live Price');
    else if ((type === TRANSACTION_TYPES.SELL) && Number(value) >= ltp) {
      setError('Sell Price should be below Live Price');
    }
  }, [ltp]);

  useEffect(() => {
    if (isSL) return;
    if (priceError && !priceError.includes('Should be a multiple of')) {
      setPriceError('');
      clValidation(price, setPriceError);
    }
  }, [price, isSL, transactionType, setPriceError, ltpValidationForPrice, priceError, clValidation]);

  const fillLtpValueInPrice = useCallbackForEvents(() => {
    setPrice(String(ltp));
  }, [ltp]);

  useEffect(() => {
    if (ltp) {
      setIsLtpExist(true);
    }
  }, [ltp]);

  useEffect(() => {
    if (isLimit && isLtpExists && !initialPrice) {
      fillLtpValueInPrice();
    }
  }, [fillLtpValueInPrice, isLimit, isLtpExists, initialPrice]);

  useDidUpdateEffect(() => {
    if (!isSL) return;
    ltpValidation(triggerPrice, setTriggerPriceError, transactionType);
    clValidation(triggerPrice, setTriggerPriceError);
  }, [triggerPrice, isSL, transactionType, setTriggerPriceError, clValidation, ltpValidation, triggerPriceError]);

  useDidUpdateEffect(() => {
    if (!isSL || !isLimit) return;
    ltpValidationForPrice(price, setPriceError, transactionType);
  }, [price, isSL, transactionType, setPriceError, clValidation, ltpValidation, isLimit, priceError]);

  useDidUpdateEffect(() => {
    if (!isLimit) return;
    clValidation(price, setPriceError);
  }, [price, isLimit, setPriceError, clValidation]);

  useDidUpdateEffect(() => {
    if (!isLimit || !isSL) setPriceTPError('');
    else if ((transactionType === TRANSACTION_TYPES.BUY) && (Number(triggerPrice) > Number(price))) setPriceTPError('Trigger Price should be lower than Buy Price');
    else if ((transactionType === TRANSACTION_TYPES.SELL) && (Number(triggerPrice) < Number(price))) setPriceTPError('Trigger Price should be higher than Sell Price');
    else setPriceTPError('');
  }, [triggerPrice, price, isLimit, isSL, transactionType, setPriceTPError]);

  const isLimitAllowed = allowedOptions?.indexOf(ORDER_TYPES.LMT) > -1 && allowedOptions?.indexOf(ORDER_TYPES.MKT) > -1;

  useEffect(() => {
    const orderType = getCustomOrderType(isLimit, isSL, productType);
    let tradeValue;
    const tradePrice = price || ltp;
    if ([CUSTOM_ORDER_TYPES.BOM, CUSTOM_ORDER_TYPES.COM, CUSTOM_ORDER_TYPES.BOL, CUSTOM_ORDER_TYPES.COL,
      CUSTOM_ORDER_TYPES.SLL, CUSTOM_ORDER_TYPES.LMT].includes(orderType)) {
      tradeValue = quantity * (tradePrice);
    } else if (CUSTOM_ORDER_TYPES.SLM === orderType) {
      tradeValue = quantity * (parseInt(triggerPrice || 0, 10) + (triggerPrice * 0.0313));
    } else if (CUSTOM_ORDER_TYPES.MKT === orderType) {
      tradeValue = quantity * (parseInt(tradePrice || 0, 10) + (tradePrice * 0.0313));
    }
    if (quantityError) { setNonBlockerError(''); } else if (tradeValue > orderConfig?.MaxTradeValue) {
      setNonBlockerError(`Max trade value allowed: ${orderConfig?.MaxTradeValue}`);
    } else setNonBlockerError('');
  }, [isLimit, isSL, ltp, nonBlockerError, orderConfig, price, productType, quantity, quantityError,
    setNonBlockerError, triggerPrice]);

  return {
    quantity,
    quantityError,
    setQuantity,
    triggerPrice,
    formattedTriggerPrice,
    triggerPriceError,
    setTriggerPrice,
    onTriggerPriceKeyDown,
    onPriceKeyDown,
    price,
    formattedPrice,
    priceError,
    setPrice,
    priceTPError,
    isLimitAllowed,
    ltp,
    lcl,
    ucl,
    productType,
    disableBtn: quantityError || priceError || triggerPriceError || priceTPError,
    lotSize,
    onKeyDown,
  };
}

export function allowOrderTypeChange(isModify, isFirstLeg, productType) {
  return !((isModify && productType === PRODUCT_TYPES.COVER_ORDER && !isFirstLeg)
    || (isModify && productType === PRODUCT_TYPES.BRACKET_ORDER && !isFirstLeg));
}

export function getFields({
  quantity, quantityError, setQuantity,
  triggerPrice, formattedTriggerPrice, triggerPriceError, setTriggerPrice,
  onTriggerPriceKeyDown, price, formattedPrice, priceError, setPrice, onPriceKeyDown,
  priceTPError,
  isLimit, toggleIsLimit,
  isSL, isLimitAllowed, traded_qty, remaining_quantity, productType, isDisabled = false,
  lotSize = DEFAULT_LOT_SIZE, segment, onKeyDown, isPartialTradeCancel, nonBlockerError,
}, isModify = false, isFirstLeg = false) {
  const showLimitOption = isLimitAllowed && allowOrderTypeChange(isModify, isFirstLeg, productType);
  const fields = [{
    label: isModify ? FIELDS_LABLES.REM_QTY : FIELDS_LABLES.QUANTITY,
    secondaryLabel: segment === SEGMENT_TYPES.DERIVATIVES ? `Lots: ${parseInt(quantity / lotSize, 10)}` : null,
    showCounter: segment === SEGMENT_TYPES.DERIVATIVES,
    stepSize: lotSize,
    value: quantity,
    onChange: setQuantity,
    hasError: quantityError,
    isDisabled,
    disabled: isModify && ((productType === PRODUCT_TYPES.COVER_ORDER)
      || (productType === PRODUCT_TYPES.BRACKET_ORDER && !isFirstLeg)),
    disableClick: isModify && ((productType === PRODUCT_TYPES.COVER_ORDER)
      || (productType === PRODUCT_TYPES.BRACKET_ORDER && !isFirstLeg)),
    onKeyDown,
  }];

  fields.push({
    label: FIELDS_LABLES.TRIGGER_PRICE,
    value: formattedTriggerPrice,
    onChange: setTriggerPrice,
    hasError: triggerPrice && (triggerPriceError || (price && priceTPError)),
    widthAnimation: true,
    showWidthAnimation: false,
    centerAnimation: true,
    show: isSL,
    onKeyDown: onTriggerPriceKeyDown,
  });

  if (isPartialTradeCancel) {
    fields.push({
      label: FIELDS_LABLES.EXECUTED_QUANTITY,
      value: traded_qty,
      disabled: !isLimit,
      disableClick: (!showLimitOption && !isLimit),
    });

    fields.push({
      label: FIELDS_LABLES.PENDING_EXECUTION,
      value: remaining_quantity,
      disabled: !isLimit,
      disableClick: (!showLimitOption && !isLimit),
    });
  }

  fields.push({
    label: FIELDS_LABLES.PRICE,
    value: isLimit ? formattedPrice : '',
    onChange: setPrice,
    hasError: (price && (priceError || (triggerPrice && priceTPError))) || (nonBlockerError),
    disabled: (!showLimitOption && !isLimit),
    placeholder: isLimit ? '' : 'At Market',
    onFocus: (isLimit || !showLimitOption) ? emptyFn : toggleIsLimit,
    disableClick: (!showLimitOption && !isLimit),
    onKeyDown: onPriceKeyDown,
  });

  return {
    fields,
    isLimit,
    showLimitOption,
    toggleIsLimit,
    error: ((quantityError || (price && priceError) || (triggerPrice && triggerPriceError)
     || (price && triggerPrice && priceTPError)) || nonBlockerError),
  };
}

export default useBasicFields;
