import React, {
  useState,
  useCallback,
  useEffect,
  useRef,
  useContext,
  useMemo,
} from 'react';
import { UserContext } from '@layout/App/UserContext';
import { useHistory, useLocation } from 'react-router-dom';

import { MODAL_TYPES } from '@common/Modal/enums';
import {
  EXCHANGE, TRANSACTION_TYPES, PRODUCT_TYPES, ROUTE_NAME, SEGMENT_TYPES,
  ORDER_TYPES,
  INSTRUMENTS,
  STICKY_ORDER,
  NO_BLOCKING_ERROR_CASES,
  BLOCKING_ERROR_CASES,
  IR_STATUS,
  MTF_INFO_LINK,
  MTF_NOT_ACTIVATED_STATUSES,
  MTF_STATUSES,
} from '@utils/enum';
import useMtfIrDataPolling from '@common/useMtfIrDataPolling';
import { getMtfScrips } from '@modules/MTF/api';
import { shapeInitialData } from '@modules/Positions/utils';
import { classNames as cx } from '@utils/style';
import { TYPE } from '@modules/OrderFlow/CDSLAuthorisation/Enum';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { useGetApi, useOrderflowApi, usePostApi } from '@common/UseApi';
import { getMessage } from '@common/UseApi/useGetApi';
import { addScripToBasket, modifyBasketOrder } from '@pages/StockInstructions/BasketOrders/basket-order-api';
import CONSTANTS from '@common/UseApi/enums';
import useEDSLAuth from '@modules/OrderFlow/CDSLAuthorisation/useCDSLauth';
import { orderUpdateSubject } from '@common/UseRefreshPage/useRefreshPage';
import LocalStorage from '@service/LocalStorage';
import { useMarketStatus } from '@modules/MarketStatus';
import { fetchPmlId, getPositionsData } from '@modules/Positions/positionsApi';
import { ORDER_STATUS } from '@pages/Orders/TodayOrders/OrderDetails/enums';
import { isEmpty } from 'lodash';
import { sendEvent } from '@service/AnalyticsService';
import { PLACE_ORDER_GA } from '@constants/ga-events';
import { HoldingsContext } from '@modules/Holdings';
import { ICON_NAME } from '@common/Icon/enums';
import Icon from '@common/Icon';
import If from '@common/If';
import { getCustomOrderType } from '@modules/OrderCharges/utils';
import OrderCharges from '@modules/OrderCharges';
import { getCompanyData } from '@pages/Company/api';
import { getSourceNameFromRoute, emptyObj } from '@utils';
import { button } from '@commonStyles';
import Routes from '@/routes';
import Header from '../Header';
import Note from '../Note';
import AdvancedOptions from '../AdvancedOptions';
import TransactionType from '../TransactionType';
import ProductType from '../ProductType';
import BasicFields from '../Fields/BasicFields';
import ApproxPnl from './ApproxPnl';
import useBasicFields, { DEFAULT_TICK, getFields, DEFAULT_LOT_SIZE } from '../useBasicFields';
import useAdvancedOptions, { getAdvaceOptionProps } from '../useAdvancedOptions';
import {
  getOrderType, handleMarketStatusChange, MARKET_STATUS_ERROR_CODES, productTypeLabels, PlaceOrderType,
} from '../util';
import styles from './index.scss';
import { placeOrder, getDetailsByIsin } from './api';
import useAdvancedFields, { getAdvancedFields } from '../useAdvancedFieds';
import useOrderType from '../useOrderType';
import AdvancedFields from '../Fields/AdvancedFields';
import ROUTES from '../../Header/enums';
import SelectExchange from './SelectExchange';
import InfoMessage from '../InfoMessage';
import InvestCare from './InvestCare';
import { getInvestcareNudge } from './InvestCare/api';
import { POSITIONS_STATUS, STATICS, POSITIONS_TAB } from '../../Positions/enums';

const ORDER_PREFERENCE = 'orderPreference';

const TICK_ERROR_MESSAGE = 'Should be a multiple of';

const DPR_MESSAGE = ['Should be more than', 'Should be less than'];

function getProductPreference(initialProductType, disableTrade, segment, instrumentType, mtfFeatureFlag, mtfData) {
  if (initialProductType) return null;
  const preference = LocalStorage.get(ORDER_PREFERENCE, false);

  switch (segment) {
    case SEGMENT_TYPES.CASH:
      if (isEmpty(preference)) return { productType: PRODUCT_TYPES.DELIVERY };
      if (preference.productType === PRODUCT_TYPES.MTF && mtfFeatureFlag && mtfData) {
        preference.productType = PRODUCT_TYPES.MTF;
      }

      if ((preference.productType !== PRODUCT_TYPES.DELIVERY && disableTrade)
        || preference.productType === PRODUCT_TYPES.MARGIN) {
        preference.productType = PRODUCT_TYPES.DELIVERY;
      }
      break;

    case SEGMENT_TYPES.DERIVATIVES:
      if (isEmpty(preference)) {
        if ([INSTRUMENTS.FUTSTK, INSTRUMENTS.OPTSTK].indexOf(instrumentType) > -1) {
          return { productType: PRODUCT_TYPES.MARGIN, orderType: ORDER_TYPES.LMT };
        }
        return { productType: PRODUCT_TYPES.MARGIN };
      }

      if (preference.productType === PRODUCT_TYPES.MTF) {
        preference.productType = PRODUCT_TYPES.MARGIN;
      }

      if ((preference.productType !== PRODUCT_TYPES.MARGIN && disableTrade)
        || preference.productType === PRODUCT_TYPES.DELIVERY) {
        preference.productType = PRODUCT_TYPES.MARGIN;
      }
      if (preference.orderType === ORDER_TYPES.MKT
        && [INSTRUMENTS.FUTSTK, INSTRUMENTS.OPTSTK].indexOf(instrumentType) > -1) {
        preference.orderType = ORDER_TYPES.LMT;
      }
      break;

    default:
      break;
  }

  return preference;
}

function getPlaceOrderEventAction(segment, productType) {
  if (segment === SEGMENT_TYPES.CASH) {
    return productType === PRODUCT_TYPES.DELIVERY ? 'invest' : 'trade';
  }

  return productType === PRODUCT_TYPES.MARGIN ? 'overnight' : 'intraday';
}

function PlaceOrder(componentProps) {
  const {
    name,
    exchange: initialExchange,
    securityId: initialSecurityId,
    transactionType: initialTransactionType,
    initialQuantity,
    initialTargetValue, initialStoplossValue, initialPrice, initialTriggerPrice,
    productType: initialProductType,
    channel,
    orderType,
    modalType,
    handleClose,
    tickSize: initialTickSize,
    isin,
    siblingSecurityId: initialSiblingSecurityId,
    siblingTickSize: initialSiblingTickSize,
    forceExchangeSelect = false,
    segment = SEGMENT_TYPES.CASH,
    lotSize = DEFAULT_LOT_SIZE,
    dragAreaRef,
    instrumentType = '',
    eventAction,
    eventCategory,
    eventLabel,
    instrument_Id,
    orderCount = 0,
    isModifyBasketOrder = false,
    order_id,
    basketIdCompany = null,
    pmlIdCompany = null,
    placeOrderInBasket = null,
    instrument_type,
    exch_symbol,
    posControlType,
    netQty,
    activeTab,
    setShowMtfOnboarding,
    isMtfModalClosed,
    fromPositions = false,
  } = componentProps;
  const [showForceSelect, setShowForceSelect] = useState(initialSecurityId
    && initialSiblingSecurityId && forceExchangeSelect);
  const history = useHistory();
  const { marketStatus } = useMarketStatus();
  const { makeRequest, inProgress, failed } = useOrderflowApi();
  const { makeRequest: makeGetReq, inProgress: switchInProgress } = useGetApi({ defaultInProgress: false });
  const { makeRequest: makePostReq } = usePostApi();
  const [exchange, setExchange] = useState(initialExchange);
  const [securityId, setSecurityId] = useState(initialSecurityId);
  const [tick, setTick] = useState(initialTickSize || DEFAULT_TICK);
  const [siblingSecurityId, setSiblingSecurityId] = useState(initialSiblingSecurityId);
  const [siblingTick, setSiblingTick] = useState(initialSiblingTickSize);
  const marketConfig = marketStatus[segment][exchange];
  const disableTrade = !marketConfig[PRODUCT_TYPES.INTRADAY].length;
  const [transactionType, setTransactionType] = useState(
    initialTransactionType || TRANSACTION_TYPES.BUY,
  );
  const [mtfData, setMtfData] = useState(null);
  const {
    combinedIrData,
    mtfFeatureFlag,
    mtfScrips,
    nonBlockerError,
    webConfig: { mpp: mppRaw = emptyObj, showApproxPnl },
    priceDprBlockingFlag, priceTickSizeBlockingFlag, getMTFData,
  } = useContext(UserContext);
  const orderPreference = getProductPreference(initialProductType,
    disableTrade,
    segment,
    instrumentType,
    mtfFeatureFlag,
    mtfData);

  const [productType, setProductType] = useState(initialProductType || orderPreference?.productType);
  const [authReqd, setAuthReqd] = useState({ authFlag: false });
  const [edisErrorCode, setEdisErrorCode] = useState(null);
  const [showTooltip, setShowTooltip] = useState(true);
  const [currentScrips, setCurrentScrips] = useState(null);
  const [showMessage, setShowMessage] = useState(true);
  const [pmlId, setPmlId] = useState(pmlIdCompany);
  const [basketId, setBasketId] = useState(basketIdCompany);
  const [showCharges, setShowCharges] = useState(false);
  const [investCareData, setInvestCareData] = useState(null);
  const [isPositionDataFetched, setIsPositionDataFetched] = useState(false);
  const mppParsed = JSON.parse(mppRaw || null);
  const [showMPPForSegment, setShowMppForSegment] = useState(false);
  const [mtfDataFetched, setMtfDataFetched] = useState(false);
  useMtfIrDataPolling({ isMtfModalClosed, mtfData });
  const [mtfExitFlow, setMtfExitFlow] = useState(false);
  const mtfIrData = useMemo(() => combinedIrData?.EQUITY[2] || emptyObj, [combinedIrData]);

  useEffect(() => {
    if (segment === SEGMENT_TYPES.CASH && combinedIrData?.EQUITY[0].hasInvested === IR_STATUS.NOT_INVESTED
      && combinedIrData?.EQUITY[0].hasTraded === IR_STATUS.NOT_TRADED
       && combinedIrData?.EQUITY[0].irStatus === IR_STATUS.ACTIVE) {
      setShowMppForSegment(true);
    }
    if (segment === SEGMENT_TYPES.DERIVATIVES
      && combinedIrData?.EQUITY[1].hasInvested === IR_STATUS.NOT_INVESTED
       && combinedIrData?.EQUITY[1].hasTraded === IR_STATUS.NOT_TRADED
        && combinedIrData?.EQUITY[1].irStatus === IR_STATUS.ACTIVE) {
      setShowMppForSegment(true);
    }
  }, [combinedIrData, segment]);

  const updateShowTooltip = useCallback((val) => {
    if (showCharges) setShowCharges(false);
    setShowTooltip(val);
  }, [showCharges]);

  const { addToast } = useToast();
  const ltpValue = useRef(null);
  const updateLtp = useRef(true);
  const stickyOrderStatus = LocalStorage.get(STICKY_ORDER, false);
  const [showInfoData, setShowInfoData] = useState({});
  const [userConsentInvestcare, setUserConsentInvestcare] = useState(true);
  const [companyData, setCompanyData] = useState({
    segment, securityId, exch_symbol, exchange, pml_id: pmlId, isin, lot_size: lotSize,
  });

  const {
    isLimit,
    isSL,
    toggleIsLimit,
    toggleIsSL,
  } = useOrderType({
    orderType: orderType || orderPreference?.orderType, productType, exchange, segment,
  });

  const advancedOptions = useAdvancedOptions({
    exchange, productType, setProductType, segment,
  });
  const storeKey = 'modal-drag-setting';
  const [dragEnabled] = useState(LocalStorage.get(storeKey, false));
  const basicFields = useBasicFields({
    initialQuantity,
    initialPrice,
    initialTriggerPrice,
    isLimit,
    transactionType,
    isSL,
    exchange,
    securityId,
    segment,
    productType,
    tick,
    lotSize: companyData.lot_size,
  });

  useEffect(() => {
    if (updateLtp.current) {
      ltpValue.current = basicFields?.ltp;
    }
  }, [basicFields]);
  const advancedFields = useAdvancedFields({
    ...basicFields,
    initialTargetValue,
    initialStoplossValue,
    initialTriggerPrice,
    isLimit,
    productType,
    transactionType,
    tick,
    segment,
    exchange,
  });
  const [disableBtn, setDisableBtn] = useState(false);

  useEffect(() => {
    if (NO_BLOCKING_ERROR_CASES.includes(priceDprBlockingFlag) && basicFields.disableBtn
     && typeof basicFields.disableBtn === 'string'
      && DPR_MESSAGE.filter((item) => basicFields?.disableBtn.includes(item)).length) {
      setDisableBtn(false);
      return;
    } if (BLOCKING_ERROR_CASES.includes(priceDprBlockingFlag)
     && basicFields.disableBtn && typeof basicFields.disableBtn === 'string'
      && DPR_MESSAGE.filter((item) => basicFields?.disableBtn.includes(item)).length) {
      setDisableBtn(true);
      return;
    }
    setDisableBtn(((inProgress || basicFields?.disableBtn || advancedFields?.disableBtn)
     || !userConsentInvestcare) && !nonBlockerError);
  }, [inProgress, basicFields.disableBtn, advancedFields?.disableBtn,
    userConsentInvestcare, nonBlockerError, priceDprBlockingFlag]);

  const {
    toggleIsBO,
    toggleIsCO,
    isBOAllowed,
    isCOAllowed,
  } = advancedOptions;
  const { holdings: { holdingMap } } = useContext(HoldingsContext);
  const callGetPmlId = useCallback(() => {
    const getPmlId = async (security_id, company_exchange, company_segment) => {
      try {
        const {
          data: { results },
        } = await makeRequest(fetchPmlId(security_id, company_exchange, company_segment));
        setPmlId(results[0].id);
      } catch (err) { }
    };
    getPmlId(securityId, exchange, segment);
  }, [securityId, exchange, segment, makeRequest]);

  const holding = holdingMap[isin];
  const switchExchange = useCallback(() => {
    sendEvent({
      event_category: 'order',
      event_action: 'orderpad_exchange_click',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
    const siblingExchange = exchange === EXCHANGE.NSE ? EXCHANGE.BSE : EXCHANGE.NSE;
    updateShowTooltip(true);
    if (isin) {
      if (siblingSecurityId) {
        const scripData = mtfScrips?.find((val) => val.sid == siblingSecurityId);
        if (productType !== PRODUCT_TYPES.MTF || scripData) {
          setSiblingSecurityId(securityId);
          setSiblingTick(tick);
          setSecurityId(siblingSecurityId);
          setTick(siblingTick);
          setExchange(siblingExchange);
        } else {
          addToast(`MTF is only allowed in ${exchange} for this scrip`, APPEARANCE_TYPES.FAIL);
        }
      } else {
        makeGetReq(getDetailsByIsin(isin))
          .then(({ data: { results: [{ scrips }] } }) => {
            const siblingScrip = scrips[siblingExchange];
            if (isEmpty(siblingScrip)) return;
            const currentScrip = scrips[exchange];
            const scripData = mtfScrips?.find((val) => val.sid == siblingScrip.security_id);
            if (productType !== PRODUCT_TYPES.MTF || scripData) {
              setSiblingTick(currentScrip.tick_size / 100);
              setSiblingSecurityId(currentScrip.security_id);
              setSecurityId(siblingScrip.security_id);
              setTick(siblingScrip.tick_size / 100);
              setExchange(siblingExchange);
              setCurrentScrips(scrips);
              setPmlId(siblingScrip.id);
            } else {
              setSiblingSecurityId(siblingScrip.security_id);
              addToast(`MTF is only allowed in ${exchange} for this scrip`, APPEARANCE_TYPES.FAIL);
            }
          })
          .catch();
      }
    }
  }, [addToast, exchange, isin, makeGetReq, mtfScrips, productType, securityId,
    siblingSecurityId, siblingTick, tick, updateShowTooltip]);

  useEffect(() => {
    if (!tick && isin) {
      getDetailsByIsin(isin)
        .then(({ data: { results: [{ scrips }] } }) => {
          const currentScrip = scrips[exchange];
          setTick(currentScrip.tick_size / 100);
          const sibllingScrip = scrips[exchange === EXCHANGE.NSE ? EXCHANGE.BSE : EXCHANGE.NSE];
          if (!isEmpty(sibllingScrip)) {
            setSiblingSecurityId(sibllingScrip.security_id);
            setSiblingTick(sibllingScrip.tick_size / 100);
          }
        })
        .catch();
    }
  }, [exchange, isin, makeGetReq, tick]);

  useEffect(() => {
    if (!isBOAllowed && (productType === PRODUCT_TYPES.BRACKET_ORDER)) toggleIsBO();
  }, [isBOAllowed, productType, toggleIsBO]);

  useEffect(() => {
    if (!isCOAllowed && (productType === PRODUCT_TYPES.COVER_ORDER)) toggleIsCO();
  }, [isCOAllowed, productType, toggleIsCO]);

  useEffect(() => {
    const scripData = mtfScrips?.find((val) => val.sid == securityId);
    setMtfData(scripData);
    setMtfDataFetched(true);
  }, [mtfScrips, securityId]);

  const switchTransactionType = useCallback(
    () => setTransactionType(transactionType === TRANSACTION_TYPES.BUY
      ? TRANSACTION_TYPES.SELL : TRANSACTION_TYPES.BUY),
    [transactionType],
  );
  const { pathname, search } = useLocation();
  const showCrosshair = search.includes(true);
  const [positionData, setPositionData] = useState([]);

  useEffect(() => {
    if (pathname.includes('baskets')) {
      setBasketId(pathname.slice(17));
    }
  }, [pathname, setBasketId]);

  useEffect(() => {
    if (isEmpty(instrument_Id)) {
      callGetPmlId();
    } else {
      setPmlId(instrument_Id);
    }
  }, [instrument_Id, callGetPmlId]);

  useEffect(() => {
    if (pmlId && (!exch_symbol || !isin)) {
      getCompanyData(pmlId).then(({ data: { results } }) => {
        setCompanyData({ ...results[0] });
      });
    }
  }, [pmlId, exch_symbol, isin]);

  useEffect(() => {
    getPositionsData().then(({ data: result }) => {
      setPositionData(result);
      setIsPositionDataFetched(true);
    }).catch();
  }, []);

  useEffect(() => {
    if (securityId && segment && exchange) {
      try {
        makeGetReq(
          getInvestcareNudge(securityId, segment, exchange, isin),
        ).then((res) => {
          setShowInfoData(res.data);
        });
      } catch (err) {}
    }
  }, [securityId, segment, exchange, isin, makeGetReq]);

  const getPositionFilter = () => getPlaceOrderEventAction(segment, productType) === PlaceOrderType.INVEST
    && transactionType === TRANSACTION_TYPES.BUY && positionData.find((pos) => pos.isin === companyData?.isin
      && pos.display_pos_status === POSITIONS_STATUS.OPEN && pos.net_qty < 0
      && pos.display_product === productTypeLabels[PRODUCT_TYPES.DELIVERY]);

  const place = async (eDisPayload = {}) => {
    let ORDER_SUMMARY_STATUS = 'fail';
    try {
      await makeRequest(placeOrder({
        productType,
        isLimit,
        isSL,
        transactionType,
        exchange,
        securityId,
        ...basicFields,
        ...advancedFields,
        amo: marketConfig?.amo,
        eDisPayload,
        segment,
        channel,
      }), () => history.push(Routes[ROUTE_NAME.ORDERS].url), transactionType, handleClose);
      sendEvent({
        event_category: 'order',
        event_action: 'orderpad_swipe_to_sell_post_edis_success',
        event_label: 'orders',
        vertical_name: 'stocks',
        screenName: '/stocks_orders',
      });
      if (!stickyOrderStatus) handleClose();
      LocalStorage.set(ORDER_PREFERENCE, { productType, orderType: getOrderType(isLimit, isSL) }, '', false);
      if (marketConfig?.amo) {
        orderUpdateSubject.next({
          txn_type: transactionType,
          product: productType,
          status: ORDER_STATUS.O_PENDING,
        });
      }
      ORDER_SUMMARY_STATUS = 'success';
    } catch (err) {
      if (MARKET_STATUS_ERROR_CODES.indexOf(err?.data?.[0]?.oms_error_code) === -1) {
        const message = getMessage(err) || CONSTANTS.ERROR_MESSAGE;
        if (err?.error_code === 'PM-0029') {
          if (edisErrorCode) {
            setAuthReqd({ qty: err?.data[0]?.additional_qty, authFlag: true });
            setEdisErrorCode(null);
            addToast(message, APPEARANCE_TYPES.FAIL);
          } else {
            setEdisErrorCode(err?.error_code);
          }
        } else {
          addToast(message, APPEARANCE_TYPES.FAIL);
        }
      } else {
        handleMarketStatusChange(err);
      }
    }
    sendEvent({
      event_category: 'order',
      event_action: 'orderpad_swipe_to_buyorsell',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
    if (transactionType === TRANSACTION_TYPES.BUY) {
      sendEvent({
        event_category: 'order',
        event_action: 'orderpad_swipe_to_buy',
        event_label: 'orders',
        vertical_name: 'stocks',
        screenName: '/stocks_orders',
      });
    } else {
      sendEvent({
        event_category: 'order',
        event_action: 'orderpad_swipe_to_sell',
        event_label: 'orders',
        vertical_name: 'stocks',
        screenName: '/stocks_orders',
      });
    }

    sendEvent({
      event_category: PLACE_ORDER_GA.PLACE_OPTION_CLICKED.EVENT_CATEGORY,
      event_action: PLACE_ORDER_GA.PLACE_OPTION_CLICKED.EVENT_ACTION(
        getPlaceOrderEventAction(segment, productType),
        transactionType === TRANSACTION_TYPES.BUY ? 'buy' : 'sell',
      ),
      event_label: PLACE_ORDER_GA.PLACE_OPTION_CLICKED.EVENT_LABEL(
        getSourceNameFromRoute(ROUTES, pathname),
        name.toLowerCase(),
      ),
    });
    sendEvent({
      event_category: PLACE_ORDER_GA.PLACE_ORDER_SUMMARY.EVENT_CATEGORY,
      event_action: PLACE_ORDER_GA.PLACE_ORDER_SUMMARY.EVENT_ACTION(
        getPlaceOrderEventAction(segment, productType),
        transactionType === TRANSACTION_TYPES.BUY ? 'buy' : 'sell',
      ),
      event_label: PLACE_ORDER_GA.PLACE_ORDER_SUMMARY.EVENT_LABEL(
        getSourceNameFromRoute(ROUTES, pathname),
        name.toLowerCase(),
        ORDER_SUMMARY_STATUS,
      ),
    });
    if (history.location.pathname.indexOf('fno') > -1) {
      const type = transactionType === TRANSACTION_TYPES.BUY ? 'buy' : 'sell';
      sendEvent({
        event_category: `${eventCategory}`,
        event_action: `${eventAction}`,
        event_label: `${eventLabel}|${type} ${ORDER_SUMMARY_STATUS}`,
        vertical_name: 'derivatives',
      });
    }
  };

  const addOrderToBasket = async () => {
    const order_type = getOrderType(isLimit, isSL);
    const sequence = orderCount + 1;
    const validity = 'DAY';
    const ctaText = pathname.includes(pmlId) || pathname.includes('fno') ? 'View Basket' : null;
    const cta = ctaText === 'View Basket';
    const onClick = cta ? () => history.push(`${Routes[ROUTE_NAME.BASKET_PAGE].url}/${basketId}`) : null;
    if (basketId) {
      if (isModifyBasketOrder && order_id) {
        await makePostReq(modifyBasketOrder({
          basketId,
          order_id,
          pmlId,
          transactionType,
          sequence: orderCount,
          exchange,
          segment,
          productType,
          securityId,
          isLimit,
          isSL,
          ...basicFields,
          validity,
          order_type,
          ...advancedFields,
        }));
      } else {
        await makePostReq(addScripToBasket({
          basketId,
          pmlId,
          transactionType,
          sequence,
          exchange,
          segment,
          productType,
          securityId,
          isLimit,
          isSL,
          ...basicFields,
          validity,
          order_type,
          ...advancedFields,
        }), onClick, handleClose, cta, ctaText);
      }
      placeOrderInBasket();
      handleClose();
    }
  };

  const { showCDSLView, Component, edslAuth } = useEDSLAuth({
    callBack: place,
    failed,
    isin,
    quantity: basicFields.quantity,
    edisErrorCode,
    isEdisFailure: authReqd?.authFlag,
    edisForMarginPositions: productType === PRODUCT_TYPES.MTF,
  });

  const advancedFieldOptions = getAdvancedFields({ ...advancedFields, nonBlockerError });

  useEffect(() => {
    const hasPriceError = basicFields?.priceError;
    const isPriceErrorMultiple = hasPriceError && basicFields.priceError.includes(TICK_ERROR_MESSAGE);
    const isPriceTickSizeBlocking = NO_BLOCKING_ERROR_CASES.includes(priceTickSizeBlockingFlag);

    if (isPriceErrorMultiple && isPriceTickSizeBlocking) {
      setDisableBtn(false);
    }
  }, [basicFields, basicFields.priceError, priceTickSizeBlockingFlag]);

  const selectExchange = useCallback((selectedExchange) => {
    if (selectedExchange !== exchange) switchExchange();
    setShowForceSelect(false);
  }, [exchange, switchExchange]);

  const containerRef = useRef();

  const [containerHeight, setContainerHeight] = useState(0);

  let ctaText = 'BUY';
  if (basketId) {
    ctaText = (
      <>
        <Icon name={ICON_NAME.BASKET_BUTTON} size={3} className={styles.basketIcon} />
        Add to Basket
      </>
    );
    if (isModifyBasketOrder) {
      ctaText = (
        <>
          <Icon name={ICON_NAME.MODIFY_ORDER_WHITE} size={3} className={styles.basketIcon} />
          Modify Order
        </>
      );
    }
  } else if (transactionType === TRANSACTION_TYPES.SELL) ctaText = 'SELL';

  useEffect(() => {
    setTimeout(() => {
      if (showForceSelect) setContainerHeight(containerRef.current.scrollHeight);
    });
  }, [showForceSelect]);

  const resetContainerHeight = useCallback(() => {
    if (!showForceSelect) {
      setContainerHeight(0);
    }
  }, [showForceSelect]);

  const handleProductType = (selectedProductType) => {
    if (selectedProductType === PRODUCT_TYPES.MTF) {
      sendEvent({
        event_category: 'mtf',
        event_action: 'margintab_on_orderpad_click',
        event_label: 'mtf',
        screenName: '/stocks_orders',
        vertical_name: 'stocks',
      });
      getMtfScrips(securityId, exchange).then((res) => {
        if (res.data?.margin_x !== (mtfData?.margin_x || mtfData?.mx) && res.data.status === 'ENABLED') setMtfData(res.data);
      }).then(() => {
        getMTFData();
      });
    } else {
      let productEventAction = selectedProductType === PRODUCT_TYPES.DELIVERY ? 'orderpad_delivery_click' : 'orderpad_intraday_click';
      if (selectedProductType === PRODUCT_TYPES.MARGIN) {
        productEventAction = 'orderpad_overnight_click';
      }
      sendEvent({
        event_category: 'order',
        event_action: productEventAction,
        event_label: 'orders',
        vertical_name: 'stocks',
        screenName: '/stocks_orders',
      });
    }
    setProductType(selectedProductType);
    setShowMessage(selectedProductType === PRODUCT_TYPES.DELIVERY);
  };

  const openChargesInfo = useCallback(() => {
    if (showTooltip) {
      sendEvent({
        event_category: 'order',
        event_action: 'orderpad_ordercharges_click',
        event_label: 'orders',
        vertical_name: 'stocks',
        screenName: '/stocks_orders',
      });
    }
    updateLtp.current = !updateLtp.current;
    if (showTooltip) setShowTooltip(false);
    setShowCharges(!showCharges);
  }, [showCharges, showTooltip]);

  const onMtfClick = () => {
    sendEvent({
      event_category: 'mtf',
      event_action: 'margin_orderpad_activate_click',
      event_label: 'mtf',
      screenName: '/stocks_orders',
      vertical_name: 'stocks',
    });
    setShowMtfOnboarding({ ...componentProps, productType });
  };

  const knowMore = () => {
    sendEvent({
      event: 'custom_event',
      event_category: 'mtf',
      event_action: 'mtf_orderpad_knowmore_clicked',
      event_label: 'mtf',
      screenName: '/stocks_orders',
      vertical_name: 'stocks',
    });
    window.open(MTF_INFO_LINK, '_blank');
  };

  useEffect(() => {
    if (mtfFeatureFlag && mtfData && productType === PRODUCT_TYPES.MTF) {
      const { irStatus } = mtfIrData;
      const eventMap = {
        ACTIVE: 'mtf_onboarding_activate_successscreen',
        REVOKED: 'mtf_onboarding_activate_blockedscreen',
        SUBSCRIPTION_PENDING: 'mtf_onboarding_activate_queuescreen',
        UNDER_REGISTRATION: 'mtf_onboarding_activate_queuescreen',
        NOT_OPTED: 'mtf_onboarding_activate_notIRreadyscreenH5',
      };
      const eventActionMap = eventMap[irStatus];
      if (eventActionMap) {
        sendEvent({
          event_category: 'mtf',
          event_action: eventActionMap,
          event_label: 'mtf',
          screenName: '/stocks_orders',
          vertical_name: 'stocks',
        });
      }
    }
  }, [mtfData, mtfFeatureFlag, mtfIrData, productType]);

  useEffect(() => {
    if (productType === PRODUCT_TYPES.MTF) {
      if (mtfDataFetched
        && ((!mtfData || mtfFeatureFlag === null) && !fromPositions)) {
        setProductType(PRODUCT_TYPES.DELIVERY);
      }
      if (posControlType === 'EXIT' && fromPositions) {
        setMtfExitFlow(true);
      }
    }
  }, [fromPositions, mtfData, mtfDataFetched, mtfFeatureFlag, productType, posControlType]);

  return (
    <div style={containerHeight ? { overflow: 'hidden' } : null}>
      <If test={!showForceSelect}>
        <div
          className={styles.tooltip}
          role="presentation"
        >
          <InvestCare
            showInfoData={showInfoData}
            setUserConsentInvestcare={setUserConsentInvestcare}
            setInvestCareData={setInvestCareData}
            investCareData={investCareData}
            showTooltip={showTooltip}
            updateShowTooltip={updateShowTooltip}
            handleClose={handleClose}
            userConsentInvestcare={userConsentInvestcare}
            companyData={companyData}
          />
        </div>
      </If>
      <If test={showCharges && !showForceSelect}>
        <OrderCharges
          exchange={exchange}
          qty={basicFields?.quantity}
          instrumentType={instrument_type || instrumentType}
          productType={productType}
          transactionType={transactionType}
          isSL={isSL}
          ltp={ltpValue.current}
          price={basicFields?.price}
          triggerPrice={basicFields?.triggerPrice}
          orderType={getCustomOrderType(isLimit, isSL, productType)}
          containerStyle={styles.charges}
          handleCloseClick={openChargesInfo}
        />
      </If>
      <div
        className={styles.wrapper}
        style={containerHeight ? { overflow: 'hidden' } : null}
      >
        <div
          className={cx(styles.scrollContainer, {
            [styles.adjustScrollContainerWidth]: !dragEnabled?.enabled || showCrosshair,
            [styles.showForceSelect]: showForceSelect,
            [styles.scrollContainerSidepane]: modalType === MODAL_TYPES.SIDEPANE,
          })}
          style={containerHeight && !showForceSelect ? { height: containerHeight } : null}
          onTransitionEnd={resetContainerHeight}
        >
          <SelectExchange
            name={name}
            nseSecurityId={exchange === EXCHANGE.NSE ? securityId : siblingSecurityId}
            bseSecurityId={exchange === EXCHANGE.BSE ? securityId : siblingSecurityId}
            selectExchange={selectExchange}
            ref={dragAreaRef}
          />
          <div
            ref={containerRef}
            id="place-order"
            className={styles.container}
            style={containerHeight && showForceSelect ? {
              transform:
                `scale(${containerRef.current.offsetHeight / containerHeight})`,
            } : null}
          >
            {showCDSLView ? (
              <Component
                onSuccess={place}
                inProgress={inProgress}
                exchange={exchange}
                securityId={securityId}
                quantity={initialQuantity}
                isin={isin}
                name={name}
                note={marketConfig.message}
                transactionType={transactionType}
                isLimit={isLimit}
                toggleIsLimit={toggleIsLimit}
                isSL={isSL}
                basicFields={basicFields}
                isSLAllowed={advancedOptions.isSLAllowed}
                productType={advancedOptions.productType}
                toggleIsSL={toggleIsSL}
                type={TYPE.PLACE}
                disableBtn={disableBtn}
                dragAreaRef={dragAreaRef}
                holding={holding}
                isEdisReqd={authReqd}
                instrumentType={instrument_type || instrumentType}
                segment={segment}
                {...basicFields}
                {...advancedFields}
                posControlType={posControlType}
                initialProductType={initialProductType}
                initialTransactionType={initialTransactionType}
                netQty={netQty}
                openChargesInfo={openChargesInfo}
              />
            )
              : (
                <>
                  <Header
                    ref={showForceSelect ? null : dragAreaRef}
                    name={name}
                    exchange={exchange}
                    securityId={securityId}
                    segment={segment}
                    note={marketConfig.message}
                    mppNote={showMPPForSegment ? mppParsed?.order_header : null}
                    switchInProgress={switchInProgress}
                    lotSize={lotSize}
                    switchExchange={segment === SEGMENT_TYPES.CASH ? switchExchange : null}
                    updateShowTooltip={updateShowTooltip}
                    showInvestCareIcon={!!investCareData}
                    tooltipValue={showTooltip}
                    userConsentInvestcare={userConsentInvestcare}
                    icon={ICON_NAME.PLACE_ORDER_NOTE}
                    mppIcon={[ICON_NAME.MPP_ICON]}
                    isLimit={isLimit}
                  >
                    <TransactionType
                      transactionType={transactionType}
                      switchTransactionType={switchTransactionType}
                      allowSwitch={!mtfExitFlow}
                    />
                    {companyData?.exch_symbol && segment === SEGMENT_TYPES.CASH
                      ? (
                        <span className={styles.exchangeSymbol}>
                          {currentScrips ? currentScrips[exchange].exch_symbol : companyData?.exch_symbol}
                        </span>
                      ) : null}
                  </Header>
                  <If test={getPositionFilter() && showMessage}>
                    <div className={styles.alertBuyBack}>
                      <div className={styles.iconsContainer}>
                        <Icon name={ICON_NAME.ALERT_BUY_BACK} className={styles.investCare} />
                        <Icon name={ICON_NAME.CLOSE_POPUP} onIconClick={() => setShowMessage(false)} size={2} />
                      </div>
                      <span className={styles.messageElement}>
                        {STATICS.DELIVERY_BUY_BACK_ALERT}
                      </span>
                      <a
                        className={styles.knowMore}
                        href="https://www.paytmmoney.com/blog/sebi-peak-margin-norms/"
                        rel="noopener noreferrer"
                        target="_blank"
                      >
                        &nbsp;Know More
                      </a>
                    </div>
                  </If>
                  <div
                    className={styles.content}
                  >
                    <ProductType
                      productType={productType}
                      changeProductType={handleProductType}
                      disableTrade={disableTrade}
                      segment={segment}
                      transactionType={transactionType}
                      showMtf={((!!mtfData && mtfFeatureFlag) || (!!mtfData && fromPositions))}
                    />
                    <If test={mtfData && mtfIrData.irStatus === 'ACTIVE' && productType === PRODUCT_TYPES.MTF && transactionType === TRANSACTION_TYPES.BUY}>
                      <div className={styles.mtfInfoWrapper}>
                        <div className={styles.mtfInfo}>
                          Buy
                          {' '}
                          {mtfData?.margin_x || mtfData?.mx}
                          {'x '}
                          qty (Pay
                          {' '}
                          {mtfData?.margin_perc || mtfData?.mp}
                          % amount) under MTF
                        </div>
                        {/* To be updated in phase 2 */}
                        {/* <a href="https://www.paytmmoney.com/blog/basket-orders-on-paytm-money/" className={styles.mtfInterestRates} target="_blank" rel="noopener noreferrer">
                            Discover interest rates
                          <Icon name={ICON_NAME.ARROW_DARK_BLUE} size={3} />
                        </a> */}
                      </div>
                    </If>
                    {(mtfData && productType === PRODUCT_TYPES.MTF
                    && MTF_NOT_ACTIVATED_STATUSES.includes(mtfIrData.irStatus) && !mtfExitFlow)
                      ? (
                        <>
                          <Icon name={ICON_NAME.MTF_INTRO} className={styles.mtfIcon} size={24} />
                          <div className={styles.mtfMsg}>{mtfIrData.cardDisplayMessage}</div>
                          <div className={styles.knowMoreMtf} role="presentation" onClick={knowMore}>
                            Know more about MTF
                          </div>
                          <button
                            className={cx([button.btn, button.btnLarge], {
                              [button.secondaryBtnFill]: button.secondaryBtnFill,
                              [button.disabledBtn]: [MTF_STATUSES.UNDER_REGISTRATION,
                                MTF_STATUSES.REVOKED].includes(mtfIrData.irStatus),
                            })}
                            onClick={onMtfClick}
                          >
                            Activate MTF
                          </button>

                        </>
                      )
                      : (
                        <>
                          <BasicFields
                            {...getFields({
                              ...basicFields, isLimit, toggleIsLimit, isSL, segment, exchange, nonBlockerError,
                            })}
                            onEnter={disableBtn ? () => { } : edslAuth}
                          />
                          {advancedFieldOptions.fields.length
                            ? (
                              <AdvancedFields
                                {...advancedFieldOptions}
                                onEnter={disableBtn ? () => { } : edslAuth}
                              />
                            ) : null}
                          {isPositionDataFetched && showApproxPnl && productType !== PRODUCT_TYPES.MTF ? (
                            <ApproxPnl
                              securityId={securityId}
                              positionData={shapeInitialData(positionData)[activeTab || POSITIONS_TAB.ALL]}
                              productType={productType}
                              exchange={exchange}
                              basicFields={basicFields}
                              isLimit={isLimit}
                              transactionType={transactionType}
                              quantity={initialQuantity}
                              holding={holding}
                              isin={isin === 'NA' ? null : isin}
                              segment={segment}
                              instrumentType={instrument_type}
                              id={pmlId}
                            />
                          ) : null}
                          <button
                            className={cx([button.btn, button.btnLarge], {
                              [button.greenBtnFill]: transactionType === TRANSACTION_TYPES.BUY,
                              [button.redBtnFill]: transactionType === TRANSACTION_TYPES.SELL,
                              [button.disabledBtn]: disableBtn,
                            })}
                            disabled={disableBtn}
                            onClick={pathname.includes('baskets') || basketId ? addOrderToBasket : edslAuth}
                          >
                            {ctaText}
                          </button>
                          <If test={(mtfData && productType === PRODUCT_TYPES.MTF
                       && !MTF_NOT_ACTIVATED_STATUSES.includes(mtfIrData.irStatus))
                        || productType !== PRODUCT_TYPES.MTF}
                          >
                            <Note
                              holding={holding}
                              initialTransactionType={initialTransactionType}
                              netQty={netQty}
                              segment={segment}
                              exchange={exchange}
                              securityId={securityId}
                              transactionType={transactionType}
                              isSL={isSL}
                              isLimit={isLimit}
                              {...basicFields}
                              {...advancedFields}
                              disableBtn={disableBtn}
                              instrumentType={instrument_type || instrumentType}
                              openChargesInfo={openChargesInfo}
                              posControlType={posControlType}
                              initialProductType={initialProductType}
                              isin={isin}
                            />
                            <AdvancedOptions
                              {...getAdvaceOptionProps({ ...advancedOptions, isSL, toggleIsSL })}
                              productType={productType}
                              orderType={getOrderType(isLimit, isSL)}
                            />
                            <InfoMessage productType={productType} transactionType={transactionType} />
                          </If>
                        </>
                      )}
                  </div>
                </>
              )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default PlaceOrder;
