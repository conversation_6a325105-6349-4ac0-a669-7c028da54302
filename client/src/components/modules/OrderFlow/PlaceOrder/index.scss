@import '../../../../styles/main';

.wrapper {
  display: flex;
  width: 100%;
  border-radius: inherit;
  overflow: hidden;
  z-index: 10;
}

.sidebarWrapper {
  border-radius: 4px;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 9rem);
  position: sticky;
  top: 0;

  @include respond(phone) {
    margin-top: 1.75rem;
  }
}

.scrollContainer {
  display: flex;
  max-width: 80rem;
  transition: all .25s ease-in-out;
  left: 0;
  position: relative;
  border-radius: inherit;

  @include respond(phone) {
    width: 100%;
  }

  &Sidepane {
    width: 100%;
    transition: none;

    > div:first-child {
      display: none;
    }

    .container {
      width: 100%;
    }

    .content {
      max-width: 40rem;
      margin: 1.5rem auto;
      border-radius: 3px;
    }
  }
}

.container {
  width: 40rem;
  border-radius: inherit;
  transition: transform .25s ease-in-out;
  transform: scaleY(1);

  @include respond(phone) {
    width: 100vw; // scss-lint:disable PropertyUnits
  }
}

.content {
  padding: 1.5rem;
  background-color: $default;
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}

.showForceSelect {
  left: 40rem;
  height: 35.3rem;
}

.charges {
  position: absolute;
  right: -83%;
  top: 0;

  @include respond(phone) {
    top: unset;
    right: unset;
    bottom: 0;
    z-index: 100000;
  }
}

.linkText {
  color: $secondary-color;
  margin-left: .5rem;
  cursor: pointer;
  display: inline-block;

  @include typography(h7, semibold);
}

hr {
  display: block;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-top: 0 solid $grey4;
}

.investCareIcon {
  cursor: pointer;
}

.exchangeSymbol {
  margin: 1rem 0 .8rem;
  text-align: left;
  color: $grey2;
}

.alertBuyBack {
  width: 36.1rem;
  height: 9.2rem;
  margin: 1.5rem .6rem 1rem 1.4rem;
  padding: .6rem 1rem .9rem;
  border-radius: .4rem;
  border: solid .1rem $grey4;
  background-color: $light-yellow;
}

.investCare {
  width: 8.1rem;
  height: 2rem;
  margin: 0 16.2rem .5rem 0;
  object-fit: contain;
}

.knowMore {
  color: $secondary-color;

  @include typography(h8, bold);
}

.iconsContainer {
  display: flex;
  justify-content: space-between;
}

.messageElement {
  color: $grey2;
  line-height: 1.6;
}

.adjustScrollContainerWidth {
  width: 40rem;
}

.basketIcon {
  margin-right: 1rem;
}

.mtfIcon {
  justify-content: center !important; // scss-lint:disable ImportantRule
}

.mtfMsg {
  @include typography(h7);
  color: $grey0;
  justify-content: center;
  text-align: center;
  margin-top: 2rem;
  margin-bottom: 4rem;
}

.knowMoreMtf {
  @include typography(h7);
  color: $secondary-color;
  display: flex;
  justify-content: center;
  padding-bottom: 1.5rem;
  cursor: pointer;
}

.mtfInfoWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 1.6rem;
}

.mtfInfo {
  @include typography(h7);
  color: $grey2;
  padding-bottom: 1rem;
}

.mtfInterestRates {
  @include typography(h7);
  color: $secondary-color;
  display: flex;
}
