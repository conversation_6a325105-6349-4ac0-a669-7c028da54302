/* eslint-disable import/no-unresolved */
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useContext,
} from 'react';
import cloneDeep from 'lodash/cloneDeep';

import Tablist from '@common/Tabs/Tablist';
import useGetApi from '@common/UseApi/useGetApi';
import CustomDropdown from '@common/CustomDropdown';
import TableHeader from '@common/Table/Header';
import Switch from '@common/Switch';
import useSort from '@common/useSort';
import { SCOPE, scopeList } from '@pages/Home/partials/MarketMovers/config';
import { HoldingsContext } from '@modules/Holdings';
import EmptyState from '@common/EmptyState';
import { ICON_NAME } from '@common/Icon/enums';
import { SEGMENT_TYPES, INSTRUMENTS, SORTING_ORDER } from '@utils/enum';
import { TYPE } from '@common/SkeletonLoader/enums';
import SkeletonLoader from '@common/SkeletonLoader';
import WithLoader from '@common/WithLoader';
import { sendEvent } from '@service/AnalyticsService';
import { DASHBOARD_GA, MARKET_PAGE_GA } from '@constants/ga-events';
import { useHistory, useLocation } from 'react-router-dom';
import { useMarketStatus } from '@modules/MarketStatus';
import { useDidUpdateEffect } from '@utils/react';
import { classNames as cx } from '@utils/style';
import FloatingSortControl from '@common/Table/FloatingSortControl';

import If from '@common/If';
import { RECENT_TYPE, UserEventContext } from '@layout/App/UserEventContext';
import Icon from '@common/Icon';
import {
  mobileBrowser, getSourceNameFromRoute, getUrlParameter, setQueryParam,
} from '@utils';
import TableRow from './TableRow';
import { getMarketConfig, getMarketMovers, getMarketMoversHome } from './movers-api';
import useMarketStockFeed, { sortOptions } from './useMarketStockFeed';
import { getFilteredConfig, transformHoldingsData } from './utils';

import styles from '../../pages/Market/styles';

import ROUTES from '../Header/enums';
import { INFO_MESSAGE, MOVERS_CATEGORIES } from './enums';

const mobileSortOptions = [{
  id: 'name',
  label: 'Name',
}, {
  id: 'percentageChange',
  label: '% Change',
  defaultOrder: SORTING_ORDER.DESCENDING,
},
{
  id: 'ltp',
  label: 'Price',
  defaultOrder: SORTING_ORDER.DESCENDING,
},
];
function Rows({
  data, activeTab, scope, ACTIVE_EVENT, isHomePage, gttStitch,
}) {
  return (
    <>
      {
        data.map((ele, index) => (
          <TableRow
            key={index}
            data={{ ...ele, instrument_type: INSTRUMENTS.STOCK }}
            activeTab={activeTab}
            positionInList={index + 1}
            scope={scope}
            ACTIVE_EVENT={ACTIVE_EVENT}
            isHomePage={isHomePage}
            gttStitch={gttStitch}
          />
        ))
      }
    </>
  );
}

const LoadingComponent = () => (
  <SkeletonLoader
    type={TYPE.TABLE}
    rows={5}
    columns={mobileBrowser() ? 2 : 6}
    className={styles.tableRow}
  />
);

function EmptyComponent({ isHomePage, marketDisplayMessage }) {
  return (
    <EmptyState
      text="No market movers found"
      subText={marketDisplayMessage}
      iconName={ICON_NAME.EMPTY_MARKET_MOVERS}
      className={!isHomePage ? styles.emptyState : ''}
    />
  );
}

const RowsHOC = WithLoader({ WrappedComponent: Rows, LoadingComponent, EmptyComponent });

const Movers = ({
  scope = SCOPE.MARKET,
  isHomePage = false,
  pageSize = 25,
  exchgIndex,
  currIndex,
  refresh,
  setRefresh,
}) => {
  const [recentSearch, setRecentSearch] = useState(null);
  const { getRecentSecurities } = useContext(UserEventContext);
  const [activeTab, setTab] = useState(null);
  const [exchange, setExchange] = useState(null);
  const [currentIndex, setIndex] = useState(null);
  const [marketConfig, setMarketConfig] = useState(null);
  const [marketMovers, setMarketMovers] = useState([]);
  const { holdings: { holdingMap } } = useContext(HoldingsContext);
  const { makeRequest, inProgress, failed } = useGetApi({ throwError: true });
  const [marketDisplayMessage, setDisplayMessage] = useState(null);
  const { search, pathname } = useLocation();
  const history = useHistory();
  const pageRoute = getSourceNameFromRoute(ROUTES, pathname);
  const { marketStatus } = useMarketStatus();
  const gttStitch = decodeURIComponent(getUrlParameter('gttStitch', search));

  const marketState = useMemo(() => {
    if (exchange) return marketStatus[SEGMENT_TYPES.CASH][exchange.toUpperCase()].status;
    return 1;
  }, [exchange, marketStatus]);

  const indices = useMemo(() => {
    if (marketConfig && exchange) {
      return marketConfig.exchanges.find((item) => item.id === exchange).indices;
    }
    return null;
  }, [marketConfig, exchange]);

  const getConfig = useCallback(async () => {
    try {
      const res = await getMarketConfig();
      const { exchanges, categories } = getFilteredConfig(res.data);
      setMarketConfig({ exchanges, categories });
      if (!isHomePage) { setExchange(exchanges[0].id); }
    } catch (err) { }
  }, [isHomePage]);

  const getMovers = useCallback(async () => {
    try {
      const getMoversValue = isHomePage && (scope === 'watchlist' || scope === 'portfolio') ? getMarketMoversHome : getMarketMovers;
      const res = await makeRequest(getMoversValue(
        {
          categoryId: activeTab,
          scope,
          pageSize,
          exchangeId: currentIndex?.exchange,
          indexId: currentIndex?.id,
        },
      ));
      setMarketMovers(res.data.securities || []);
      setDisplayMessage(res.meta.displayMessage);
    } catch (err) {
      setMarketMovers([]);
    }
  }, [isHomePage, scope, makeRequest, activeTab, pageSize, currentIndex]);

  useDidUpdateEffect(() => {
    if (marketState === 1) getMovers();
  }, [marketState]);

  useEffect(() => {
    getConfig();
  }, [getConfig]);

  useEffect(() => {
    if (refresh) {
      getMovers();
      setRefresh(false);
    }
  }, [getMovers, refresh, setRefresh]);

  useEffect(() => {
    if (activeTab && currentIndex) {
      getMovers();
    }
  }, [getMovers, activeTab, currentIndex]);

  useEffect(() => {
    if (isHomePage && exchgIndex) {
      setExchange(exchgIndex);
    }
  }, [exchgIndex, isHomePage]);

  useEffect(() => {
    if (indices) {
      if (isHomePage && currIndex) {
        setIndex(currIndex);
      } else {
        const value = indices.find((item) => item.is_default);
        value.exchange = exchange;
        setIndex(value);
      }
    }
  }, [currIndex, exchange, indices, isHomePage]);

  useEffect(() => {
    const getRecentSearch = async () => {
      setRecentSearch(await getRecentSecurities(RECENT_TYPE.SEARCHED));
    };
    getRecentSearch();
  }, [getRecentSecurities]);
  useEffect(() => {
    const view = decodeURIComponent(getUrlParameter('view', search));
    const isViewValid = MOVERS_CATEGORIES.find((tab) => tab.id == view);
    if (isViewValid == undefined) {
      if (!isHomePage) {
        setQueryParam({
          query: {
            view: MOVERS_CATEGORIES[0].id,
            gttStitch,
          },
          pathname,
          replace: history.replace,
        });
      }
      setTab(MOVERS_CATEGORIES[0].key);
    }
    if (isViewValid) {
      if (!isHomePage) {
        setQueryParam({
          query: {
            view,
            gttStitch,
          },
          pathname,
          replace: history.replace,
        });
      }
      setTab(isViewValid.key);
    }
  }, [gttStitch, history, isHomePage, pathname, search]);

  const marketHeaders = useMemo(() => {
    const opt = mobileBrowser() ? sortOptions.slice(0, 2) : sortOptions;
    if (currentIndex && exchange && indices) {
      const options = cloneDeep(opt);
      options[0].component = !isHomePage ? (
        <CustomDropdown
          selectedValue={currentIndex}
          options={indices}
          onChange={(val) => {
            const obj = val;
            obj.exchange = exchange;
            setIndex(obj);
          }}
          containerClass={styles.dropdown}
          optionDefaultClass={styles.optionClass}
          displayValue={currentIndex.label}
        />
      ) : null;
      return options;
    }
    return opt;
  }, [currentIndex, exchange, indices, isHomePage]);

  const requiredStreams = useMarketStockFeed();

  const rawData = useMemo(() => {
    const updatedMoversData = marketMovers.map((el) => {
      let isRecent;
      if (recentSearch?.findIndex((obj) => obj.id === el.id) !== -1) {
        isRecent = true;
      } else {
        isRecent = false;
      }

      const { remaining_quantity = 0 } = holdingMap[el.isin] || {};
      return {
        ...el,
        remaining_quantity,
        isRecent,
      };
    });
    return updatedMoversData;
  }, [marketMovers, holdingMap, recentSearch]);
  const {
    data, activeSort, handleSorting, isLoading,
  } = useSort({
    rawData,
    requiredFeedFxn: requiredStreams,
    transformFxn: transformHoldingsData,
  });
  const sendGAEvent = (source, l2_level) => {
    let l1_level = scope;
    let CURRENT_EVENT = MARKET_PAGE_GA;
    if (source === 'dashboard') {
      l1_level = scopeList.find((entry) => entry.id === scope).label;
      CURRENT_EVENT = DASHBOARD_GA;
    } else {
      l1_level = 'movers';
    }
    sendEvent({
      event_category: CURRENT_EVENT.L2_CLICKED.EVENT_CATEGORY,
      event_action: CURRENT_EVENT.L2_CLICKED.EVENT_ACTION,
      event_label: CURRENT_EVENT.L2_CLICKED.EVENT_LABEL(
        l1_level.toLowerCase(), l2_level,
      ),
    });
  };
  const handleTabChange = (e) => {
    handleSorting({});
    setTab(e);
    sendGAEvent(pageRoute, e);
    if (!isHomePage) {
      setQueryParam({
        query: {
          view: MOVERS_CATEGORIES.find((tab) => tab.key == e).id,
          gttStitch,
        },
        pathname,
        replace: history.push,
      });
    }
  };
  return (
    <div className={cx([styles.table], {
      [styles.mTable]: !isHomePage,
    })}
    >
      <div className={cx([styles.movers, styles.noWrap])}>
        {marketConfig?.categories && (
          <Tablist
            options={marketConfig.categories}
            activeTab={activeTab}
            setTab={(e) => handleTabChange(e)}
            isTable
          />
        )}
        {!isHomePage && exchange && (
          <Switch
            options={marketConfig.exchanges}
            active={exchange}
            setSwitch={(val) => {
              handleSorting({});
              setExchange(val);
              sendEvent({
                event_category: MARKET_PAGE_GA.NSE_BSE_CLICKED.EVENT_CATEGORY,
                event_action: MARKET_PAGE_GA.NSE_BSE_CLICKED.EVENT_ACTION,
                event_label: MARKET_PAGE_GA.NSE_BSE_CLICKED.EVENT_LABEL(val),
              });
            }}
            containerClassName={styles.switchStyles}
          />
        )}
      </div>
      {marketHeaders && (isHomePage || !mobileBrowser()) && (
        <TableHeader
          options={marketHeaders}
          active={activeSort}
          onClick={handleSorting}
          className={styles.tableRow}
        />
      )}
      <If test={!isHomePage && !mobileBrowser()}>
        <div className={styles.moversInfo}>
          <Icon name={ICON_NAME.INFO_DARK} size={2.8} className={styles.infoIcon} />
          {INFO_MESSAGE}
        </div>
      </If>
      <RowsHOC
        inProgress={inProgress || isLoading}
        reqEmpty={!data.length}
        failed={failed}
        data={data}
        isHomePage={isHomePage}
        marketDisplayMessage={marketDisplayMessage}
        refresh={getMovers}
        activeTab={activeTab}
        scope={scope}
        ACTIVE_EVENT={pageRoute === 'home' ? DASHBOARD_GA : MARKET_PAGE_GA}
        gttStitch={gttStitch}
      />
      <If test={!isHomePage && mobileBrowser()}>
        <FloatingSortControl onlyMobile onAction={handleSorting} options={mobileSortOptions} />
      </If>
    </div>
  );
};

export default Movers;
