import React, {
  useCallback, useState, useEffect, useContext,
} from 'react';
import moment from 'moment';
import { classNames as cx } from '@utils/style';
import { LoadingComponent } from '@pages/Pricing/index';
import { PnlContext } from '@layout/App/PnlContext';
import If from '@common/If';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import {
  convertDate, numDifferentiation, getMonthDateRange, updateMonth,
} from '../../utils';
import {
  WEEK_DAYS, SWITCH_OPTIONS, NUMBER_OF_MONTHS,
} from '../../constant';
import BarChartDaily from '../BarChartDaily';
import styles from './index.scss';

function CalendarDailyChartView({ activeSwitch }) {
  const {
    monthsData, currentMonthIndex, setCurrentMonthIndex,
    currentMonth, setCurrentMonth, yearToBeDisplayed, setYearToBeDisplayed,
  } = useContext(PnlContext);
  const [monthToBeDisplayed, setMonthToBeDisplayed] = useState(moment(currentMonthIndex, 'M').format('MMMM'));
  const [currentMonthDailyData, setCurrentMonthDailyData] = useState([]);
  const [startDate, setStartDate] = useState(getMonthDateRange(yearToBeDisplayed, currentMonthIndex).start);

  useEffect(() => {
    const { start } = getMonthDateRange(yearToBeDisplayed, currentMonthIndex);
    setStartDate(start);
  }, [currentMonthIndex, yearToBeDisplayed]);

  const updateMonthOnClick = useCallback((updateType) => {
    const {
      monthIndex, yearDisplayed, monthDisplayed, month,
    } = updateMonth(
      {
        currentMonthIndex, yearToBeDisplayed, updateType, currentMonth,
      },
    );
    setYearToBeDisplayed(yearDisplayed);
    setCurrentMonthIndex(monthIndex);
    setMonthToBeDisplayed(monthDisplayed);
    setCurrentMonth(month);
  }, [currentMonth, currentMonthIndex, setCurrentMonth, setCurrentMonthIndex, setYearToBeDisplayed, yearToBeDisplayed]);

  const calculateMonthData = useCallback(() => {
    if (currentMonthDailyData[currentMonth]) return;
    const dateData = currentMonthDailyData;
    const dailyData = [];
    const endDay = moment(`${yearToBeDisplayed}-${currentMonthIndex}`, 'YYYY-MM').daysInMonth();
    for (let index = 1; index <= endDay; index += 1) {
      const data = [];
      monthsData[currentMonth].forEach((item) => {
        const sellDate = item.sd ? convertDate(item.sd) : convertDate(item.sellDt);
        const buyDate = item.pd ? convertDate(item.pd) : convertDate(item.purDt);
        const dateToBeCompared = new Date(sellDate).getTime() > new Date(buyDate).getTime()
          ? sellDate : buyDate;
        if (dateToBeCompared === `${yearToBeDisplayed}-${String(currentMonthIndex).padStart(2, '0')}-${String(index).padStart(2, '0')}`) {
          data.push(item);
        }
      });
      dailyData[index - 1] = data;
    }
    dateData[currentMonth] = dailyData;
    setCurrentMonthDailyData([...dateData]);
  }, [currentMonth, currentMonthDailyData, currentMonthIndex, monthsData, yearToBeDisplayed]);

  const calculateDailyData = useCallback(() => currentMonthDailyData[currentMonth]
    .map((item) => item.reduce((accumulator, object) => accumulator + (object.rgl || object.rglAmt), 0)),
  [currentMonth, currentMonthDailyData]);

  const showMonthData = useCallback(() => {
    if (!currentMonthDailyData[currentMonth]) return null;
    const currentData = calculateDailyData();
    const month = [];
    let week = [];
    const endDay = moment(`${yearToBeDisplayed}-${currentMonthIndex}`, 'YYYY-MM').daysInMonth();
    for (let ele = 1; ele <= endDay; ele += 1) {
      const day = moment(`${yearToBeDisplayed}-${currentMonthIndex}-${ele}`).day();
      // eslint-disable-next-line no-continue
      if (day === 0) continue; // Sunday
      if (day === 6) { // Saturday
        month.push(week);
        week = [];
      } else {
        week[day - 1] = { value: currentData[ele - 1] || 0, date: ele };
      }
    }
    if (week.length) month.push(week);
    const data = [];
    if (![0, 6].includes(startDate)) {
      for (let index = 1; index < startDate; index += 1) {
        data.push(<div className={styles.emptyRectangle} />);
      }
    }
    month.forEach((item) => {
      item.forEach((dateData) => {
        const pnlValue = numDifferentiation(dateData.value);
        data.push(
          <div
            className={`${pnlValue === 0 ? styles.disabledRectangle : styles.rectangle}`}
          >
            <div className={styles.date}>{dateData.date}</div>
            <div className={cx(styles.value, {
              [styles.profit]: dateData.value > 0,
              [styles.loss]: dateData.value < 0,
              [styles.neutral]: pnlValue === 0,
            })}
            >
              {pnlValue !== 0 ? `₹${pnlValue}` : '-'}
            </div>
          </div>,
        );
      });
    });
    return data;
  }, [calculateDailyData, currentMonth, currentMonthDailyData, currentMonthIndex, startDate, yearToBeDisplayed]);

  useEffect(() => {
    calculateMonthData();
  }, [calculateMonthData]);

  if (!currentMonthDailyData[currentMonth]) return <LoadingComponent />;

  return (
    <div>
      <div className={styles.headerWrapper}>
        <div className={styles.monthWrapper}>
          <div>
            <Icon
              onIconClick={() => { if (currentMonth === 0) return () => {}; return updateMonthOnClick('reduce'); }}
              name={currentMonth === 0 ? ICON_NAME.LEFT_ARROW_GREY : ICON_NAME.LEFT_ARROW_FILLED}
              className={`${currentMonth === 0 ? styles.disabled : ''} ${styles.arrow}`}
            />
          </div>
          <div className={styles.month}>
            {monthToBeDisplayed}
            {' '}
&nbsp;
            {' '}
            {yearToBeDisplayed}
          </div>
          <div>
            <Icon
              name={currentMonth === (NUMBER_OF_MONTHS - 1) ? ICON_NAME.RIGHT_ARROW_GREY : ICON_NAME.RIGHT_ARROW_FILLED}
              onIconClick={() => { if (currentMonth === NUMBER_OF_MONTHS - 1) return () => {}; return updateMonthOnClick('add'); }}
              className={`${currentMonth === (NUMBER_OF_MONTHS - 1) ? styles.disabled : ''} ${styles.arrow}`}
            />
          </div>
        </div>
        <div className={`${activeSwitch === SWITCH_OPTIONS(true)[0].id ? styles.weekWrapper : styles.hideWrapper}`}>
          {WEEK_DAYS.map((week) => (<div className={styles.week}>{week}</div>))}
        </div>
      </div>
      <If test={activeSwitch === SWITCH_OPTIONS(true)[0].id
         && currentMonthDailyData[currentMonth] && currentMonthDailyData[currentMonth].length}
      >
        <div className={styles.rectangleWrapper}>{showMonthData()}</div>
      </If>
      <If test={activeSwitch === SWITCH_OPTIONS(true)[1].id}>
        {currentMonthDailyData[currentMonth]
          ? (
            <BarChartDaily
              pnlForMonth={currentMonthDailyData[currentMonth]}
              yearToBeDisplayed={yearToBeDisplayed}
              currentMonthIndex={currentMonthIndex}
            />
          ) : null}
      </If>
    </div>
  );
}

export default CalendarDailyChartView;
