import React, {
  useEffect, useState, useContext, useCallback,
} from 'react';
import { useHistory, useLocation, Link } from 'react-router-dom';
import Spinner from '@common/SkeletonLoader/Loaders/Spinner';
import { UserContext } from '@layout/App/UserContext';
import LocalStorage from '@service/LocalStorage';
import {
  IR_STATUS, ROUTE_NAME, THEMES, COOKIES, OTP_DATA,
} from '@utils/enum';
import moment from 'moment';
import Checkbox from '@common/Checkbox';
import { setDataTheme } from '@utils/style';
import { useGetApi, usePostApi } from '@common/UseApi';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { staticImagePath } from '@common/Icon/enums';
import { LOCAL_STORAGE_KEYS } from '@layout/LoggedInLayout/enums';
import FnoBlocker from '@layout/LoggedInLayout/FnoBlocker';
import {
  isArrayElementsValid, getUrlParameter, setCookie, deleteCookie, getCookieValue, extractData,
} from '@utils';
import Routes from '@/routes';
import CONFIG from '@/config';
import FormBanner from './FormBanner';
import PasscodeFlow from './PasscodeFlow';
import TOTPFlow from './TOTPFlow';
import OTPFlow from './OTPFlow';
import { getTOTPstatus } from './TOTPFlow/api';
import styles from './index.scss';
import {
  VALIDATION_SCREENS, ERROR_MSG, SUCCESS_MSG_OTP, ERROR_CODES_OTP,
} from './enums';
import { getRequestToken, getMerchantPermission, verifyMerchantPassCode } from './PasscodeFlow/api';
import { sendOTP } from './OTPFlow/api';

import { PMLENV_CONFIG } from './config';
import { IR_STATUSES } from '../ManagePasscode/enums';

function Passcode() {
  const history = useHistory();
  const location = useLocation();
  const {
    ssoToken: isLoggedIn,
    mssoToken,
    login,
    userId,
    setRiskDisclosureAcceptance,
    riskDisclosureAcceptance,
    twoFaToken,
    mtwoFaToken,
  } = useContext(UserContext);
  const [status, setStatus] = useState();
  const [isPassCodeExist, setIsPassCodeExist] = useState();
  const [otpData, setOtpData] = useState();
  const [isLoaded, setLoaded] = useState(false);
  useEffect(() => {
    setDataTheme(THEMES.LIGHT);
  }, []);

  const isMerchantFlow = getUrlParameter('isMerchant', window.location.search);
  const longTermTokenValidity = getUrlParameter('longTermTokenValidity', location.search);
  const [checked, setChecked] = useState(true);
  const merchantName = getUrlParameter('pmMerchantName', location.search);
  const MERCHANT_LOGO = getUrlParameter('pmMerchantLogo', location.search);
  const pmlEnv = getUrlParameter('pmlEnv', window.location.search);
  const accessTokenMin = getUrlParameter('accessTokenMin', location.search);
  const isMerchantLoginFlow = getUrlParameter('isMerchantLogin', location.search);
  const targetURL = getUrlParameter('target_url', location.search);
  const merchantApiKey = getUrlParameter('apiKey', window.location.search);
  const [isRedirectCheckDone, setIsRedirectCheckDone] = useState(false);

  const { combinedIrData } = useContext(UserContext);
  const irStatus = combinedIrData?.EQUITY[0]?.irStatus;
  const { makeRequest } = useGetApi();
  const { addToast } = useToast();
  const { makeRequest: makePostRequest } = usePostApi();

  const getReturnUrl = useCallback((target_url, requestToken, state, loginId) => {
    const url = target_url ? decodeURIComponent(target_url) : Routes[ROUTE_NAME.HOME].url;
    let query = `?success=true&requestToken=${requestToken}${state ? `&state=${state}` : ''}`;
    if (pmlEnv === 'publisherApi') {
      query = `&success=true&requestToken=${requestToken}${state ? `&state=${state}` : ''}${loginId ? `&loginId=${loginId}` : ''}`;
    }
    window.location.href = `${url}${query}`;
  }, [pmlEnv]);

  const validateAndRedirect = useCallback(async () => {
    try {
      const { data } = await getRequestToken(merchantApiKey, {
        _2fa_token: getCookieValue(COOKIES.TWOFA_TOKEN),
        _2fa_token_expiry: getCookieValue(COOKIES.TWOFA_TOKEN_EXPIRY),
      });
      const {
        request_token: requestToken,
        login_id: loginId,
      } = data;

      const { authorisation_verified: authorizationVerified } = await getMerchantPermission(loginId, requestToken);

      const perUrl = `${Routes[ROUTE_NAME.APP_PERMISSION].url}?requestToken=${requestToken}&apiKey=${merchantApiKey}&target_url=${targetURL}&isMerchantLogin=true&loginId=${loginId}&&authorizationRequired=${authorizationVerified}`;

      if (!authorizationVerified) {
        history.push(perUrl);
      }
      if (getCookieValue(COOKIES.TWOFA_TOKEN) && authorizationVerified) {
        getReturnUrl(targetURL, requestToken, getUrlParameter('state', window.location.search), loginId);
      }
      setIsRedirectCheckDone(true);
    } catch (error) {
    }
  }, [history, merchantApiKey, targetURL, getReturnUrl]);

  useEffect(() => {
    if (isMerchantFlow) {
      LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.RISK_DISCLOSURE_ACCEPTANCE);
      setRiskDisclosureAcceptance(false);
    }
  }, [isMerchantFlow, isMerchantLoginFlow, setRiskDisclosureAcceptance]);

  useEffect(() => {
    if (isMerchantLoginFlow && !isRedirectCheckDone && twoFaToken
    && riskDisclosureAcceptance) {
      validateAndRedirect();
      return;
    }
    if (!isLoggedIn && !(mssoToken && isMerchantFlow)) {
      login();
    }
  }, [isLoggedIn, isMerchantFlow, isMerchantLoginFlow, isRedirectCheckDone,
    login, mssoToken, riskDisclosureAcceptance, twoFaToken, validateAndRedirect]);

  const getFlowType = () => {
    if (!getCookieValue(COOKIES.LOGIN_FLOW)) {
      return 'password';
    }
    const cookieVal = JSON.parse(extractData(getCookieValue(COOKIES.LOGIN_FLOW)));
    return cookieVal?.flow || 'password';
  };

  useEffect(() => {
    if ((isMerchantFlow || pmlEnv === 'developer') && irStatus !== IR_STATUS.ACTIVE && typeof irStatus !== 'undefined') {
      history.replace(
        `${Routes[ROUTE_NAME.MERCHANT_NON_IR].url}?returnUrl=${encodeURIComponent(location.pathname + location.search)}`,
      );
    }
    const getStatus = (config) => {
      if (typeof irStatus !== 'undefined') {
        makeRequest(getTOTPstatus(userId, isMerchantFlow)).then(({ data }) => {
          const flowType = getFlowType();
          const allowQrByPass = !isMerchantFlow && flowType === 'qr' && !pmlEnv;
          if (IR_STATUSES.includes(irStatus)) {
            if (flowType === 'otp') {
              setIsPassCodeExist(data.passcodeEnabled);
              setStatus(VALIDATION_SCREENS.PASSCODE);
            } else if ('totpEnabled' in data) {
              if (data.totpEnabled && config?.totpAllowed && !allowQrByPass) setStatus(VALIDATION_SCREENS.TOTP);
              else if (config?.otpAllowed) {
                sendOTP(userId, isMerchantFlow).then((response) => {
                  LocalStorage.set(
                    OTP_DATA,
                    JSON.stringify(response.data),
                    (moment().add(45000).valueOf() - new Date().getTime()) / 1000,
                  );
                  setOtpData(response.data);
                  setStatus(VALIDATION_SCREENS.OTP);
                }).catch((err) => {
                  let message = ERROR_MSG;
                  const otpRes = JSON.parse(LocalStorage.get(OTP_DATA));
                  if (otpRes && err?.meta?.code === ERROR_CODES_OTP.PM_PC_EC_125) {
                    message = err?.meta?.displayMessage;
                    setOtpData(otpRes);
                    setStatus(VALIDATION_SCREENS.OTP);
                  } else {
                    setOtpData({});
                    setStatus(VALIDATION_SCREENS.OTP);
                  }
                  addToast(message, APPEARANCE_TYPES.FAIL);
                });
              } else {
                setIsPassCodeExist(data.passcodeEnabled);
                setStatus(VALIDATION_SCREENS.PASSCODE);
              }
            }
          } else {
            setIsPassCodeExist(data.passcodeEnabled);
            setStatus(VALIDATION_SCREENS.PASSCODE);
          }
        }).catch(() => {
        });
      }
    };
    fetch(CONFIG.TWOFA_RULE_CONFIG).then((res) => res.json()).then((config) => getStatus(config))
      .catch(() => {
        getStatus({});
      });
  }, [isMerchantFlow, irStatus, history, location.pathname, location.search, userId, makeRequest, addToast, pmlEnv]);

  useEffect(() => {
    if ((twoFaToken || mtwoFaToken) && (isLoggedIn || mssoToken) && !isMerchantLoginFlow) {
      const requestToken = getUrlParameter('requestToken', location.search);
      const state = getUrlParameter('state', window.location.search);
      if (requestToken) {
        if (isMerchantFlow === 'true') {
          deleteCookie(COOKIES.M_TWOFA_TOKEN);
          if (pmlEnv === 'publisherApi' && getCookieValue(COOKIES.TWOFA_TOKEN)) {
            getReturnUrl(targetURL, requestToken, state);
          }
        } else {
          const perUrl = `${Routes[ROUTE_NAME.APP_PERMISSION].url}?requestToken=${requestToken}&apiKey=${merchantApiKey}${state ? `&state=${state}` : ''}`;
          history.push(perUrl);
        }
      } else if (pmlEnv) {
        if (PMLENV_CONFIG[pmlEnv]?.url) window.location.href = PMLENV_CONFIG[pmlEnv]?.url;
        else history.replace(Routes[ROUTE_NAME.HOME].url);
      } else {
        const returnUrl = getUrlParameter('returnUrl', location.search);
        const url = returnUrl ? decodeURIComponent(returnUrl) : Routes[ROUTE_NAME.HOME].url;
        if (url.indexOf(window.location.origin) === 0) {
          window.location.replace(url);
        } else {
          history.replace(url);
        }
      }
    }
  }, [isLoggedIn, history, location.search, mssoToken, isMerchantFlow, pmlEnv,
    isMerchantLoginFlow, targetURL, merchantApiKey, twoFaToken, mtwoFaToken, getReturnUrl]);

  const renderSubmit = (verify, input, correct_length) => (
    <div>
      {
        isMerchantFlow && longTermTokenValidity !== accessTokenMin && (
          <div className={styles.terms}>
            <Checkbox
              checked={checked}
              onChange={() => setChecked(!checked)}
            />
            <span>
              {`Keep login session active for ${longTermTokenValidity} days with ${merchantName[0] + merchantName.toLowerCase().substring(1)}`}
            </span>
          </div>
        )
      }
      <button
        type="button"
        className={styles.submitButton}
        disabled={!(isArrayElementsValid(input) && input.length === correct_length)}
        onClick={() => verify(input, checked)}
      >
        Submit
      </button>
    </div>
  );

  const verify2FAFlow = async (body, verifyFlow) => {
    const requestToken = getUrlParameter('requestToken', location.search);
    const accessTokenParam = getUrlParameter('accessToken', location.search);
    const accessTokenKey = accessTokenParam.toLowerCase() === 'true';
    const state = getUrlParameter('state', window.location.search);
    const authorizationRequired = getUrlParameter('authorizationRequired', location.search);
    const apiKey = getUrlParameter('apiKey', location.search);
    const loginId = getUrlParameter('loginId', location.search);
    const public_access_token_validity = getUrlParameter('publicAccess', location.search);
    const read_access_token_validity = getUrlParameter('readAccess', location.search);
    const refresh_token_validity = getUrlParameter('refreshToken', location.search);

    let data;
    let successMSG;
    const validationRequestBody = {
      request_token: requestToken,
      api_key: apiKey,
      access_token_validity: checked ? longTermTokenValidity : accessTokenMin || 1,
      public_access_token_validity: public_access_token_validity || 1,
      read_access_token_validity: read_access_token_validity || 1,
      refresh_token_validity: refresh_token_validity || 1,
      login_id: loginId,
    };
    if (body.totp) {
      validationRequestBody.validation_code = body.totp;
      validationRequestBody.validation_type = 'totp';
      const response = await (requestToken ? verifyMerchantPassCode(validationRequestBody,
        isMerchantFlow) : verifyFlow({ totp: body.totp }, userId));
      data = response?.data || response;
      successMSG = response?.meta?.displayMessage;
    } else if (body.otp) {
      validationRequestBody.validation_code = body.otp;
      validationRequestBody.validation_type = 'otp';
      validationRequestBody.uuid = otpData?.uuid;
      const response = await (requestToken ? verifyMerchantPassCode(validationRequestBody,
        isMerchantFlow) : verifyFlow({ otp: body.otp, uuid: otpData?.uuid, userId }, userId));
      data = response?.data || response;
      successMSG = SUCCESS_MSG_OTP;
    } else {
      validationRequestBody.validation_code = body.passcode;
      validationRequestBody.validation_type = '2facode';
      const response = await makePostRequest(requestToken ? verifyMerchantPassCode(validationRequestBody,
        isMerchantFlow) : verifyFlow({ passcode: body.passcode }, userId, isMerchantFlow));
      data = response?.data || response;
    }
    let twofa_token; let
      twofa_token_expiry;
    if (requestToken) {
      const { _2fa_token, _2fa_token_expiry } = data;
      twofa_token = _2fa_token;
      twofa_token_expiry = _2fa_token_expiry;
    } else {
      const { pmlToken, sessionExpiryInSec } = data;
      twofa_token = pmlToken;
      twofa_token_expiry = sessionExpiryInSec;
    }
    let expires;
    if (twofa_token_expiry) {
      expires = new Date(twofa_token_expiry * 1000);
    } else {
      expires = new Date();
      expires.setHours(23, 59, 59, 999);
    }
    if (requestToken) {
      setCookie(COOKIES.M_TWOFA_TOKEN, twofa_token, expires);
      if (pmlEnv === 'publisherApi') {
        setCookie(COOKIES.TWOFA_TOKEN, twofa_token, expires);
        setCookie(COOKIES.TWOFA_TOKEN_EXPIRY, twofa_token_expiry, new Date(twofa_token_expiry * 1000));
      }
    } else {
      setCookie(COOKIES.TWOFA_TOKEN, twofa_token, expires);
      setCookie(COOKIES.TWOFA_TOKEN_EXPIRY, twofa_token_expiry, new Date(twofa_token_expiry * 1000));
    }
    const returnUrl = getUrlParameter('returnUrl', location.search);
    if (isMerchantLoginFlow && !isRedirectCheckDone && getCookieValue(COOKIES.TWOFA_TOKEN)) {
      validateAndRedirect();
      return;
    }
    if (requestToken) {
      if (authorizationRequired) {
        const perUrl = `${Routes[ROUTE_NAME.APP_PERMISSION].url}?${accessTokenKey ? `accessToken=${accessTokenKey}&` : '&'}requestToken=${requestToken}&apiKey=${merchantApiKey}${state ? `&state=${state}` : ''}&target_url=${(pmlEnv === 'publisherApi' && encodeURIComponent(targetURL)) || data?.target_url}&loginId=${loginId}&pmMerchantName=${merchantName}&pmMerchantLogo=${MERCHANT_LOGO}&apiKey=${apiKey}${state ? `&state=${state}` : ''}${pmlEnv ? `&pmlEnv=${pmlEnv}` : ''}`;
        history.push(perUrl);
        return;
      }
      getReturnUrl(pmlEnv === 'publisherApi' ? targetURL : data.target_url, requestToken, state);
      return;
    } if (pmlEnv) {
      if (PMLENV_CONFIG[pmlEnv]?.url) window.location.href = PMLENV_CONFIG[pmlEnv]?.url;
      else history.replace(Routes[ROUTE_NAME.HOME].url);
      return;
    }
    const url = returnUrl ? decodeURIComponent(returnUrl) : Routes[ROUTE_NAME.HOME].url;
    if (body.otp || body.totp) addToast(successMSG, APPEARANCE_TYPES.SUCCESS);
    if (url.indexOf(window.location.origin) === 0) {
      window.location.replace(url);
    } else {
      history.replace(url);
    }
  };

  const getFlowComponent = () => {
    switch (status) {
      case VALIDATION_SCREENS.TOTP:
        return (
          <TOTPFlow
            renderSubmit={renderSubmit}
            verify2FAFlow={verify2FAFlow}
          />
        );
      case VALIDATION_SCREENS.PASSCODE:
        return (
          <PasscodeFlow
            renderSubmit={renderSubmit}
            isPassCodeExist={isPassCodeExist}
            setIsPassCodeExist={setIsPassCodeExist}
            verify2FAFlow={verify2FAFlow}
          />
        );
      case VALIDATION_SCREENS.OTP:
        return (
          <OTPFlow
            renderSubmit={renderSubmit}
            otpData={otpData}
            verify2FAFlow={verify2FAFlow}
            setOtpData={setOtpData}
          />
        );
      default:
        return (
          <div className={styles.loaderComponent}>
            <Spinner />
          </div>
        );
    }
  };

  const onLoad = () => {
    setLoaded(true);
  };

  if (isMerchantLoginFlow && !riskDisclosureAcceptance) {
    return <FnoBlocker />;
  }
  if (isMerchantLoginFlow && twoFaToken) {
    return null;
  }

  return (
    <div className={styles.container}>
      <FnoBlocker />
      <div className={styles.wrapper}>
        <div>
          <div className={styles.formWrapper}>
            <div className={styles.OtpWrapper}>
              <img src={`${MERCHANT_LOGO || `${staticImagePath}/common/pm-logo.svg`}`} alt="logo" className={isLoaded ? `${styles.logo}` : `${styles.hidden}`} onLoad={onLoad} />
              {getFlowComponent()}
              {isMerchantFlow && <img src={`${staticImagePath}/common/pm-logo.svg`} alt="logo" className={styles.bottomLogo} />}
            </div>
            <FormBanner />
          </div>
          <div className={styles.aggrement}>
            By proceeding, you agree to the
            <Link to={isMerchantFlow ? `${Routes[ROUTE_NAME.TERMS_AND_CONDITIONS].url}#open-api` : `${Routes[ROUTE_NAME.TERMS_AND_CONDITIONS].url}`} target="_blank"> Terms &amp; Conditions </Link>
            and
            <Link to={Routes[ROUTE_NAME.PRIVACY_POLICY].url} target="_blank"> Privacy Policy </Link>
            of Paytm Money
          </div>
        </div>
      </div>
    </div>
  );
}

export default Passcode;
