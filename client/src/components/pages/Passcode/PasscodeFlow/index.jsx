/* eslint-disable no-unused-vars */
/* eslint-disable no-underscore-dangle */
import React, {
  useState,
  useEffect,
  useContext,
  useCallback,
} from 'react';
import {
  useHistory, useLocation,
} from 'react-router-dom';

import { ROUTE_NAME, COOKIES } from '@utils/enum';
import { UserContext } from '@layout/App/UserContext';
import useOtpInput from '@common/otp/useOtpInput';
import OtpInput from '@common/otp/OtpInput';
import { classNames as cx } from '@utils/style';
import { usePostApi } from '@common/UseApi';
import WithLoader from '@common/WithLoader';
import Spinner from '@common/SkeletonLoader/Loaders/Spinner';
import GAElement from '@common/GAElement';
import { LOGIN_PAGE_GA, PASSCODE_PAGE_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';
import useKeyDown from '@common/useKeyDown';
import { button } from '@commonStyles';
import {
  isArrayElementsValid, getUrlParameter, setCookie, getCookieValue,
} from '@utils';
import { IR_STATUSES } from '@pages/ManagePasscode/enums';
import TitleComponent from '../TitleComponent';
import {
  verifyPassCode, verifyMerchantPassCode, createPassCodeApi, getRequestToken, getMerchantPermission,
} from './api';
import Routes from '../../../../routes';

import styles from './index.scss';
import commonStyles from '../index.scss';
import { PMLENV_CONFIG } from '../config';
import NonIrForgotPasscode from './NonIrForgotPasscode';

const CONSTANTS = {
  INPUT_LENGTH_PASSCODE: 4,
  INPUT_LENGTH_OTP: 6,
  OTP_COOL_DOWN_TIME: 30,
};

function Passcode({
  isPassCodeExist,
  displayImage,
  displayName,
  getTitle,
  renderOtp,
  renderSubmit,
  isDisabled,
  handleSubmitOnClick,
  logout,
  verifyPasscode,
  input,
}) {
  const returnUrl = getUrlParameter('returnUrl', window.location.search);
  const merchantApiKey = getUrlParameter('apiKey', window.location.search);
  const state = getUrlParameter('state', window.location.search);
  const isMerchantFlow = getUrlParameter('requestToken', window.location.search);
  const longTermTokenValidity = getUrlParameter('longTermTokenValidity', window.location.search);
  const accessTokenMin = getUrlParameter('accessTokenMin', window.location.search);
  const history = useHistory();
  const { combinedIrData } = useContext(UserContext);
  const [showNonIrForgotPasscodeMsg, setShowNonIrForgotPasscodeMsg] = useState(false);

  const onLogout = () => {
    sendEvent({
      event_category: LOGIN_PAGE_GA.SWITCH_ACCOUNT_CLICKED.EVENT_CATEGORY,
      event_action: LOGIN_PAGE_GA.SWITCH_ACCOUNT_CLICKED.EVENT_ACTION,
    });
    if (isMerchantFlow) {
      logout({
        merchantApiKey,
        state,
        returnUrl,
      });
    } else {
      logout();
    }
  };

  const handleClick = () => {
    sendEvent({
      event_category: LOGIN_PAGE_GA.FORGOT_PASSCODE_CLICKED.EVENT_CATEGORY,
      event_action: LOGIN_PAGE_GA.FORGOT_PASSCODE_CLICKED.EVENT_ACTION,
    });
    if (!(IR_STATUSES.includes(combinedIrData?.EQUITY[0]?.irStatus))) {
      setShowNonIrForgotPasscodeMsg(true);
    } else {
      history.push(`${Routes[ROUTE_NAME.MANAGE_PASSCODE].url}${window.location.search}`);
    }
  };

  return (
    showNonIrForgotPasscodeMsg
      ? (
        <div className={commonStyles.passcodeWrapper}>
          <NonIrForgotPasscode
            showNonIrForgotPasscodeMsg={showNonIrForgotPasscodeMsg}
            setShowNonIrForgotPasscodeMsg={setShowNonIrForgotPasscodeMsg}
            showHeader
          />
        </div>
      ) : (
        <div className={commonStyles.passcodeWrapper}>
          {isPassCodeExist && (
            <>
              <img src={displayImage} alt="user-img" className={commonStyles.userImg} />
              <TitleComponent title={`Hi ${displayName ? displayName.split(' ')[0] : ''}`} />
            </>
          )}
          {
        !isPassCodeExist && (
          <TitleComponent title="Set Security Pin" />
        )
      }
          <div className={styles.OtpInputContainer}>
            <div className={styles.OtpRow}>
              <div className={styles.otpLabel}>{getTitle()}</div>
              <div>{renderOtp()}</div>
            </div>
            {isPassCodeExist && (
              <GAElement
                gaEventCategory={LOGIN_PAGE_GA.FORGOT_PASSCODE_CLICKED.EVENT_CATEGORY}
                gaEventAction={LOGIN_PAGE_GA.FORGOT_PASSCODE_CLICKED.EVENT_ACTION}
              >
                <div role="presentation" onClick={handleClick} className={styles.forgot}>
                  Forgot Pin?
                </div>
              </GAElement>
            )}
            {!isPassCodeExist && (
              <div className={styles.info}>
                Please set 4 digit pin for enhanced security &amp; authentication on Paytm Money.
              </div>
            )}
          </div>
          {isMerchantFlow && longTermTokenValidity !== accessTokenMin && (
            <div>{renderSubmit(verifyPasscode, input, CONSTANTS.INPUT_LENGTH_PASSCODE)}</div>
          )}

          <div className={styles.ButtonWrapper}>
            {!isPassCodeExist && (
              <button
                className={cx([button.btn, button.btnLarge, button.secondaryBtnFill], {
                  [button.disabledBtnFill]: isDisabled(),
                })}
                onClick={() => {
                  handleSubmitOnClick();
                  sendEvent({
                    event_category: PASSCODE_PAGE_GA.PROCEED_CLICKED.EVENT_CATEGORY,
                    event_action: PASSCODE_PAGE_GA.PROCEED_CLICKED.EVENT_ACTION,
                  });
                }}
                disabled={isDisabled()}
              >
                Proceed
              </button>
            )}
            {isPassCodeExist && (
              <div className={styles.switchAcc}>
                Not your account?
                <GAElement
                  gaEventCategory={LOGIN_PAGE_GA.SWITCH_ACCOUNT_CLICKED.EVENT_CATEGORY}
                  gaEventAction={LOGIN_PAGE_GA.SWITCH_ACCOUNT_CLICKED.EVENT_ACTION}
                >
                  <span
                    className={styles.switchBtn}
                    onClick={onLogout}
                    role="presentation"
                  >
                    Switch Account
                  </span>
                </GAElement>
              </div>
            )}
          </div>
        </div>
      )
  );
}

export function LoadingComponent() {
  return (
    <div className={styles.loadingContainer}>
      <Spinner />
    </div>
  );
}

const PasscodeHOC = WithLoader({ WrappedComponent: Passcode, LoadingComponent });

function PasscodeInput(props) {
  const { renderSubmit, isPassCodeExist, setIsPassCodeExist } = props;
  const {
    displayName,
    displayImage,
    logout,
  } = useContext(UserContext);
  const history = useHistory();
  const location = useLocation();
  const isMerchant = getUrlParameter('isMerchant', window.location.search);
  const longTermTokenValidity = getUrlParameter('longTermTokenValidity', location.search);
  const state = getUrlParameter('state', window.location.search);
  const pmlEnv = getUrlParameter('pmlEnv', window.location.search);
  const accessTokenMin = getUrlParameter('accessTokenMin', window.location.search);
  const apiKey = getUrlParameter('apiKey', location.search);
  const isMerchantLoginFlow = getUrlParameter('isMerchantLogin', location.search);

  const { makeRequest: makePostRequest, inProgress: postReqInProgress } = usePostApi();
  const [passCodeConfirmationScreen, setPassCodeConfirmationScreen] = useState(false);
  const { userId } = useContext(UserContext);

  const createPassCode = async (passCode, confirmPassCode) => {
    if (postReqInProgress) return;
    try {
      await makePostRequest(createPassCodeApi({
        passcode: passCode.join(''),
        confirmPasscode: confirmPassCode.join(''),
      }, userId, isMerchant));
      setIsPassCodeExist(true);
    } catch (err) {
      const errorObj = err?.meta?.code || err?.error_code;
      if (errorObj === 'PM_2FA_PC_EC_110') {
        setIsPassCodeExist(true);
      }
    }
  };

  const proceedToConfirmPasscode = () => {
    setPassCodeConfirmationScreen(true);
  };

  const [
    input, containerRef, onKeyDown, onClick, onChange, resetInput, onPaste,
  ] = useOtpInput(CONSTANTS.INPUT_LENGTH_PASSCODE, () => { }, () => { });

  const [
    passCodeInput, passCodeContainerRef, passCodeOnKeyDown, passCodeOnClick,
    // eslint-disable-next-line no-unused-vars
    passCodeOnChange, passcodeRestInput, onPastePasscode,
  ] = useOtpInput(CONSTANTS.INPUT_LENGTH_PASSCODE, proceedToConfirmPasscode);

  const [
    passCodeConfirmInput, passCodeConfirmContainerRef, passCodeConfirmOnKeyDown,
    // eslint-disable-next-line no-unused-vars
    passCodeConfirmOnClick, passCodeConfirmOnChange, passCodeConfirmRestInput, onPastePasscodeConfirm,
  ] = useOtpInput(
    CONSTANTS.INPUT_LENGTH_PASSCODE,
    (passCodeValue) => createPassCode(passCodeInput, passCodeValue),
    () => { },
  );

  const getReturnUrl = useCallback((target_url, requestToken, stateVar, loginId) => {
    const url = target_url ? decodeURIComponent(target_url) : Routes[ROUTE_NAME.HOME].url;
    let query = `?success=true&requestToken=${requestToken}${stateVar ? `&state=${stateVar}` : ''}`;
    if (pmlEnv === 'publisherApi') {
      query = `&success=true&requestToken=${requestToken}${stateVar ? `&state=${stateVar}` : ''}${loginId ? `&loginId=${loginId}` : ''}`;
    }
    window.location.href = `${url}${query}`;
  }, [pmlEnv]);

  const validateAndRedirect = useCallback(async () => {
    const targetURL = getUrlParameter('target_url', location.search);
    try {
      const { data } = await getRequestToken(apiKey, {
        _2fa_token: getCookieValue(COOKIES.TWOFA_TOKEN),
        _2fa_token_expiry: getCookieValue(COOKIES.TWOFA_TOKEN_EXPIRY),
      });
      const {
        request_token: requestToken,
        login_id: loginId,
      } = data;

      const { authorisation_verified: authorizationVerified } = await getMerchantPermission(loginId, requestToken);

      const perUrl = `${Routes[ROUTE_NAME.APP_PERMISSION].url}?requestToken=${requestToken}&apiKey=${apiKey}
      &target_url=${targetURL}&isMerchantLogin=true&loginId=${loginId}`;
      if (!authorizationVerified) {
        history.push(perUrl);
      }
      if (getCookieValue(COOKIES.TWOFA_TOKEN) && authorizationVerified) {
        getReturnUrl(targetURL, requestToken, getUrlParameter('state', window.location.search), loginId);
      }
    } catch (error) {
    }
  }, [apiKey, getReturnUrl, history, location.search]);

  const verifyPasscode = useCallback(async (inputData, isChecked = false) => {
    try {
      const requestToken = getUrlParameter('requestToken', location.search);
      const accessTokenParam = getUrlParameter('accessToken', location.search);
      const accessToken = accessTokenParam.toLowerCase() === 'true';
      const authorizationRequired = getUrlParameter('authorizationRequired', location.search);
      const loginId = getUrlParameter('loginId', location.search);
      const merchantName = getUrlParameter('pmMerchantName', location.search);
      const MERCHANT_LOGO = getUrlParameter('pmMerchantLogo', location.search);
      const public_access_token_validity = getUrlParameter('publicAccess', location.search);
      const read_access_token_validity = getUrlParameter('readAccess', location.search);
      const refresh_token_validity = getUrlParameter('refreshToken', location.search);

      const validationRequestBody = {
        request_token: requestToken,
        api_key: apiKey,
        access_token_validity: isChecked ? longTermTokenValidity : accessTokenMin,
        public_access_token_validity,
        read_access_token_validity,
        refresh_token_validity,
        login_id: loginId,
        validation_code: inputData.join(''),
        validation_type: '2facode',
      };
      const headers = {};

      const data = await makePostRequest(requestToken ? verifyMerchantPassCode(validationRequestBody,
        isMerchant,
        headers)
        : verifyPassCode({ passcode: inputData.join('') }, userId, isMerchant));

      let twofa_token; let
        twofa_token_expiry;
      if (requestToken) {
        twofa_token = data?._2fa_token;
        twofa_token_expiry = data?._2fa_token_expiry;
      } else {
        const { data: { pmlToken, sessionExpiryInSec } } = data;
        twofa_token = pmlToken;
        twofa_token_expiry = sessionExpiryInSec;
      }
      let expires;
      if (twofa_token_expiry) {
        expires = new Date(twofa_token_expiry * 1000);
      } else {
        expires = new Date();
        expires.setHours(23, 59, 59, 999);
      }
      if (requestToken) {
        setCookie(COOKIES.M_TWOFA_TOKEN, twofa_token, expires);
        if (pmlEnv === 'publisherApi') {
          setCookie(COOKIES.TWOFA_TOKEN, twofa_token, expires);
          setCookie(COOKIES.TWOFA_TOKEN_EXPIRY, twofa_token_expiry, new Date(twofa_token_expiry * 1000));
        }
      } else {
        setCookie(COOKIES.TWOFA_TOKEN, twofa_token, expires);
        setCookie(COOKIES.TWOFA_TOKEN_EXPIRY, twofa_token_expiry, new Date(twofa_token_expiry * 1000));
      }
      if (isMerchantLoginFlow) {
        validateAndRedirect();
        return;
      }
      const returnUrl = getUrlParameter('returnUrl', location.search);
      const merchantApiKey = getUrlParameter('apiKey', window.location.search);
      const targetURL = getUrlParameter('target_url', location.search);
      if (requestToken) {
        if (authorizationRequired) {
          const perUrl = `${Routes[ROUTE_NAME.APP_PERMISSION].url}?${accessToken ? `accessToken=${accessToken}&` : '&'}requestToken=${requestToken}&apiKey=${merchantApiKey}${state ? `&state=${state}` : ''}&target_url=${(pmlEnv === 'publisherApi' && encodeURIComponent(targetURL)) || returnUrl || data?.target_url}&loginId=${loginId}&pmMerchantName=${merchantName}&pmMerchantLogo=${MERCHANT_LOGO}&apiKey=${apiKey}${state ? `&state=${state}` : ''}${pmlEnv ? `&pmlEnv=${pmlEnv}` : ''}`;
          history.push(perUrl);
          return;
        }
        getReturnUrl(pmlEnv === 'publisherApi' ? targetURL : data.target_url, requestToken, state);
        return;
      } if (pmlEnv) {
        if (PMLENV_CONFIG[pmlEnv]?.url) window.location.href = PMLENV_CONFIG[pmlEnv]?.url;
        else history.replace(Routes[ROUTE_NAME.HOME].url);
        return;
      }
      const url = returnUrl ? decodeURIComponent(returnUrl) : Routes[ROUTE_NAME.HOME].url;
      if (url.indexOf(window.location.origin) === 0) {
        window.location.replace(url);
      } else {
        history.replace(url);
      }
    } catch (err) {
      resetInput();
      if (err?.meta?.code === 'PM_PC_EC_106') {
        history.push(Routes[ROUTE_NAME.MANAGE_PASSCODE].url);
      }
    }
  }, [location.search, apiKey, longTermTokenValidity, accessTokenMin, makePostRequest, isMerchant,
    userId, isMerchantLoginFlow, pmlEnv, history, validateAndRedirect, getReturnUrl, state, resetInput]);

  useEffect(() => {
    if (isArrayElementsValid(input) && isPassCodeExist && CONSTANTS.INPUT_LENGTH_PASSCODE === input.length) {
      if (!isMerchant || !(longTermTokenValidity !== accessTokenMin)) {
        verifyPasscode(input);
      }
    }
  }, [accessTokenMin, input, isMerchant, isPassCodeExist, longTermTokenValidity, verifyPasscode]);

  const handleSubmitOnClick = () => {
    if (!isPassCodeExist && !passCodeConfirmationScreen && isArrayElementsValid(passCodeInput)) {
      setPassCodeConfirmationScreen(true);
    } else if (!isPassCodeExist && isArrayElementsValid(passCodeConfirmInput)) {
      createPassCode(passCodeInput, passCodeConfirmInput);
    }
  };

  const getTitle = () => {
    if (!isPassCodeExist && !passCodeConfirmationScreen) {
      return 'Set your pin';
    } if (!isPassCodeExist) {
      return 'Confirm pin';
    } return 'Enter your pin';
  };

  const isDisabled = () => {
    if (!isPassCodeExist && !passCodeConfirmationScreen) {
      return !isArrayElementsValid(passCodeInput);
    } if (!isPassCodeExist) {
      return !isArrayElementsValid(passCodeConfirmInput) || postReqInProgress;
    } return !isArrayElementsValid(input) || postReqInProgress;
  };

  useKeyDown(input, CONSTANTS.INPUT_LENGTH_PASSCODE, verifyPasscode);

  const renderOtp = () => {
    if (!isPassCodeExist && passCodeConfirmationScreen) {
      return (
        <OtpInput
          input={passCodeConfirmInput}
          length={CONSTANTS.INPUT_LENGTH_PASSCODE}
          onChange={passCodeConfirmOnChange}
          onKeyDown={passCodeConfirmOnKeyDown}
          onClick={passCodeConfirmOnClick}
          containerRef={passCodeConfirmContainerRef}
          type="password"
          key="pin"
          onPaste={onPastePasscodeConfirm}
        />
      );
    } if (!isPassCodeExist) {
      return (
        <OtpInput
          input={passCodeInput}
          length={CONSTANTS.INPUT_LENGTH_PASSCODE}
          onChange={passCodeOnChange}
          onKeyDown={passCodeOnKeyDown}
          onClick={passCodeOnClick}
          containerRef={passCodeContainerRef}
          type="password"
          key="verifyPin"
          onPaste={onPastePasscode}
        />
      );
    }
    return (
      <div className={styles.otpContainer}>
        <OtpInput
          input={input}
          length={CONSTANTS.INPUT_LENGTH_PASSCODE}
          onChange={onChange}
          onKeyDown={onKeyDown}
          onClick={onClick}
          containerRef={containerRef}
          type="password"
          key="verifyOtp"
          onPaste={onPaste}
        />
        {postReqInProgress && <Spinner otpSpinner />}
      </div>
    );
  };

  return (
    <PasscodeHOC
      isPassCodeExist={isPassCodeExist}
      isDisabled={isDisabled}
      displayImage={displayImage}
      displayName={displayName}
      getTitle={getTitle}
      renderOtp={renderOtp}
      renderSubmit={renderSubmit}
      logout={logout}
      handleSubmitOnClick={handleSubmitOnClick}
      verifyPasscode={verifyPasscode}
      input={input}
    />
  );
}

export default PasscodeInput;
