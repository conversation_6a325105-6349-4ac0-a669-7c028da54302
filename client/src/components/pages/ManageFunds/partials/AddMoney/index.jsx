/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useRef, useContext, useState } from 'react';

import QuickAddAmount from '@common/QuickAddAmount';
import { classNames as cx } from '@utils/style';
import { useCallbackForEvents } from '@utils/react';
import { FUNDS_PAGE_GA } from '@constants/ga-events';
import { sendEvent, sendPageEvent, sendEventPaymentsRevamp } from '@service/AnalyticsService';
import If from '@common/If';
import { UserContext } from '@layout/App/UserContext';
import useNudge from '@common/useNudge';
import { button as btnStyles } from '@commonStyles';
import { PAYMENT_EVENTS } from '@pages/ManageFunds/enums';
import usePriceInput, {
  PRICE_INPUT_TYPE,
} from '../../../../common/usePriceInput';

import styles from './index.scss';

function AddMoney({
  showQuickAdds = true,
  showInline = false,
  buttonClassName,
  inputStyles,
  handleAddMoney,
  handleWithdrawal,
  quickAddAmounts = [],
  lastPayinAmount = null,
  minValue = 0,
  maxValue = 20000000,
  maxDisplayMessage = '',
  isButtonDisabled = false,
  isHomePage = false,
  defaultInputAmount = minValue,
  addMoneyContainerClass = '',
  showBalance = false,
  currentBalance,
  isFundsPage = false,
  isFirstPayin,
}) {
  const {
    formattedPrice: formattedInputValue,
    price: inputValue,
    onInput: setInputValue,
    priceError: fundInputError,
    setPriceError: setInputError,
  } = usePriceInput({
    defaultValue: defaultInputAmount,
    min: minValue,
    max: maxValue,
    type: PRICE_INPUT_TYPE.ADD_FUNDS,
    maxDigits: 10,
    allowedDecimals: 2,
  });
  const inputFundsRef = useRef(null);
  const [disableAddFundsBtn, setDisableAddFundsBtn] = useState(false);

  const { webConfig } = useContext(UserContext);
  useNudge(JSON.parse(webConfig?.nudgeScreenConfig || null)?.FUNDS, !isHomePage);

  useEffect(() => {
    inputFundsRef.current.focus();
  }, []);

  useEffect(() => {
    if (inputValue === '') {
      setInputError(false);
    }
  }, [inputValue, setInputError]);

  useEffect(() => {
    setInputValue(`${defaultInputAmount}`);
  }, [defaultInputAmount, setInputValue]);

  useEffect(() => {
    if (disableAddFundsBtn) {
      const timer = setTimeout(() => {
        setDisableAddFundsBtn(false);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [disableAddFundsBtn]);

  const addAmount = useCallbackForEvents(
    (amount) => {
      setInputValue(`${+inputValue + amount}`);
    },
    [inputValue],
  );

  const reloadAmount = (amount) => {
    setInputValue(`${amount}`);
  };

  const inputClass = cx(inputStyles, {
    [styles.fundInputError]: fundInputError,
  });

  const isAddButtonDisabled = isButtonDisabled || fundInputError || inputValue === '' || disableAddFundsBtn;

  const addFundButtonClass = cx([btnStyles.btn, buttonClassName], {
    [btnStyles.greenBtnFill]: !isAddButtonDisabled,
    [btnStyles.disabledBtn]: isAddButtonDisabled,
    [btnStyles.disabledBtnFill]: isAddButtonDisabled,
    [styles.addFundBtn]: currentBalance == 0,
  });

  const withdrawFundButtonClass = cx([btnStyles.btn, buttonClassName], {
    [btnStyles.secondaryBtnFill]: !fundInputError,
    [btnStyles.disabledBtn]: fundInputError,
    [btnStyles.disabledBtnFill]: fundInputError,
  });

  const fundInputErrorMessage = fundInputError ? (
    <div
      id="error-msg"
      className={styles.errorMsg}
    >
      {inputValue > maxValue ? maxDisplayMessage : `(${fundInputError})`}
    </div>
  ) : (
    <div className={cx(null, { [styles.extraPadding]: !showInline })} />
  );
  return (
    <div
      className={cx(styles.addMoney, {
        [styles.inlineContainer]: showInline,
        [addMoneyContainerClass]: addMoneyContainerClass,
      })}
    >
      <div>
        <div className={cx([styles.amountInputWrapper], { [styles.fnoAmountWrapper]: showBalance })}>
          <label htmlFor="inputClass">
            <input
              id="inputClass"
              className={inputClass}
              type="text"
              onChange={(e) => setInputValue(e.target.value)}
              onFocus={() => {
                sendEvent({
                  vertical_name: FUNDS_PAGE_GA.ADD_FUNDS_FOCUSED.VERITCAL_NAME,
                  event_category: FUNDS_PAGE_GA.ADD_FUNDS_FOCUSED.EVENT_CATEGORY,
                  event_action: FUNDS_PAGE_GA.ADD_FUNDS_FOCUSED.EVENT_ACTION,
                  screenName: FUNDS_PAGE_GA.ADD_FUNDS_FOCUSED.SCREEN_NAME,
                  eventName: FUNDS_PAGE_GA.ADD_FUNDS_FOCUSED.EVENT_NAME,
                });
              }}
              value={`₹${formattedInputValue}`}
              ref={inputFundsRef}
            />
          </label>
          {!isHomePage && fundInputErrorMessage}
          {showQuickAdds && (
            <div className={styles.quickAddList}>
              {lastPayinAmount && (
                <QuickAddAmount
                  value={lastPayinAmount}
                  addAmount={reloadAmount}
                  showReloadIcon
                />
              )}
              {quickAddAmounts.map((val, index) => (
                <QuickAddAmount key={index} value={val} addAmount={addAmount} />
              ))}
            </div>
          )}
          {showBalance ? (
            <div className={styles.balanceWrapper}>
              <span>Available Balance:</span>
              <span>{currentBalance}</span>
            </div>
          ) : null}
        </div>
        {isHomePage && fundInputErrorMessage}
      </div>
      <div className={cx([styles.btnContainer], {
        [styles.btnWithdrawContainer]: currentBalance > 0,
      })}
      >
        <button
          id="trigger-add-modal"
          onClick={() => {
            setDisableAddFundsBtn(true);
            handleAddMoney(inputValue);
            sendEventPaymentsRevamp({
              event_category: 'addmoney_equity_web',
              event_action: 'addfunds_clicked_web',
              screenName: '/add_funds',
              vertical_name: 'payments',
            }, isFirstPayin);
            if (isFundsPage) {
              sendPageEvent({
                eventDetails: {
                  screenName: 'addfunds_fundspage',
                  eventType: 'openScreen',
                  event_action: 'open_screen',
                },
              });
              sendEvent({
                vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                event_action: PAYMENT_EVENTS.EVENT_ACTION.GENERATE_QR_WEB,
                screenName: PAYMENT_EVENTS.SCREEN_NAME,
                eventName: PAYMENT_EVENTS.EVENT_NAME.CUSTOM_EVENT,
              });
            }
          }}
          className={addFundButtonClass}
          disabled={isAddButtonDisabled}
        >
          Add Funds
        </button>
        <If test={currentBalance > 0}>
          <button
            id="trigger-withdraw-modal"
            onClick={() => {
              handleWithdrawal(inputValue);
              sendEvent({
                event_category: FUNDS_PAGE_GA.WITHDRAW_FUNDS_CLICKED.EVENT_CATEGORY,
                event_action: FUNDS_PAGE_GA.WITHDRAW_FUNDS_CLICKED.EVENT_ACTION,
              });
            }}
            className={withdrawFundButtonClass}
            disabled={fundInputError}
          >
            Withdraw Funds
          </button>
        </If>
      </div>
    </div>
  );
}

export default AddMoney;
