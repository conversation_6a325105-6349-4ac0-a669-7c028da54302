import { ICON_NAME } from '@common/Icon/enums';
import { STATUS } from '@common/ModalStatusHeader';
import { ROUTE_NAME } from '@utils/enum';
import Routes from '@/routes';
import styles from './TransactionStatusModal/index.scss';

const MANAGE_FUNDS = {
  ADD: 'ADD',
  WITHDRAW: 'WITHDRAW',
  INFO_MODAL: 'INFO_MODAL',
  DETAILED_SUMMARY: 'DETAILED_SUMMARY',
};

const FUNDS_STATUS = {
  SUCCESS: 'S',
  FAILED: 'F',
  CANCELLED: 'C',
  INCOMPLETE: 'I',
  OTHERS: 'OTHERS',
  PENDING: 'P',
};

const TRANSACTION_STATUS_ICON_MAP = {
  [FUNDS_STATUS.SUCCESS]: {
    iconName: ICON_NAME.SUCCESS_ICON,
    statusMsg: 'Done',
    statusClass: 'success',
  },
  [FUNDS_STATUS.FAILED]: {
    iconName: ICON_NAME.FAILED,
    statusMsg: 'Failed',
    statusClass: 'failed',
  },
  [FUNDS_STATUS.CANCELLED]: {
    iconName: ICON_NAME.CANCELLED,
    statusMsg: 'Cancelled',
    statusClass: 'cancelled',
  },
  [FUNDS_STATUS.INCOMPLETE]: {
    iconName: ICON_NAME.INCOMPLETE,
    statusMsg: '',
    statusClass: 'incomplete',
  },
  [FUNDS_STATUS.OTHERS]: {
    iconName: ICON_NAME.IN_PROGRESS,
    statusMsg: 'In Progress',
    statusClass: 'pending',
  },
};

const TRANSACTION_HEADER_ICON = {
  [FUNDS_STATUS.SUCCESS]: {
    iconName: ICON_NAME.SUCCESS_WHITE_ICON,
    class: styles.successWrapper,
    status: STATUS.SUCCESS,
  },
  [FUNDS_STATUS.CANCELLED]: {
    iconName: ICON_NAME.CANCELLED,
    class: `${styles.errorWrapper}`,
    status: STATUS.FAILED,
  },
  [FUNDS_STATUS.FAILED]: {
    iconName: ICON_NAME.FAILED,
    class: `${styles.errorWrapper}`,
    status: STATUS.FAILED,
  },
  [FUNDS_STATUS.OTHERS]: {
    iconName: ICON_NAME.PENDING,
    class: `${styles.acceptedWrapper}`,
    status: STATUS.PENDING,
  },
};

const ORDER_STATUS = {
  [FUNDS_STATUS.SUCCESS]: {
    iconName: ICON_NAME.SUCCESS_ICON,
    label: 'Success',
  },
  [FUNDS_STATUS.FAILED]: {
    iconName: ICON_NAME.FAILED,
    label: 'Failed',
  },
  [FUNDS_STATUS.CANCELLED]: {
    iconName: ICON_NAME.CANCELLED,
    label: 'Cancelled',
  },
  [FUNDS_STATUS.OTHERS]: {
    iconName: ICON_NAME.PENDING,
    label: 'Pending',
  },
};

const ORDER_TYPE = {
  ADD: 'ADD',
  WITHDRAW: 'WITHDRAW',
};

const TRANSACTION_TYPE = {
  DEBIT: 'D',
  CREDIT: 'C',
};

const LEDGER_TRANSACTION_STATUS = {
  IN_PROGRESS: 'In Progress',
  COMPLETED: 'Completed',
};

const LEDGER_TRANSACTION_TYPE = {
  D: ICON_NAME.RUPEEK_FAILURE,
  C: ICON_NAME.RUPEEK_SUCCESS,
};

const LEDGER_POPER_MESSAGE = {
  PENDING_SETTLEMENTS: 'Bills & Accrued charges which are yet to be settled. These will be processed on the Settlement date.',
  PREVIOUS_SETTLEMENTS: 'Balance after processing the recent settlement.',
  CLOSING_BALANCE: 'This is the final balance as per the last trading day. Closing Balance = Previous Balance + Pending Settlements',
};

const APPLIED_COUPON_STATUS = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const REQUESTED_AMOUNT = 'Requested Amount';
const ALGO_TRADING = 'algo';
const ALGO_REDIRECT_TO_FUNDS = 'algoRedirectToFunds';

const FAILED_CTA = {
  TRY_AGAIN: 1,
  USE_ANOTHER_BANK_ACC: 2,
  USE_ANOTHER_VPA: 3,
  USE_BANK_TRANSFER: 4,
  CONTINUE: 5,
  GO_TO_WATCHLIST: 6,
  RESET_AMOUNT: 7,
  SET_ANOTHER_AUTOPAY: 8,
  SET_MPIN: 9,
  TRANSFER_LOWER_AMT: 10,
  TRY_AGAIN_LATER: 11,
  USE_ANOTHER_UPI_APP: 12,
};

const FAILED_RETRY = [
  FAILED_CTA.TRY_AGAIN, FAILED_CTA.USE_ANOTHER_VPA, FAILED_CTA.USE_BANK_TRANSFER,
  FAILED_CTA.SET_MPIN, FAILED_CTA.USE_ANOTHER_UPI_APP,
];

const FAILED_CLOSE = [
  FAILED_CTA.CONTINUE, FAILED_CTA.RESET_AMOUNT,
  FAILED_CTA.TRANSFER_LOWER_AMT, FAILED_CTA.TRY_AGAIN_LATER,
];

const FAILED_BTN = (key, cta) => ({
  [FAILED_CTA.TRY_AGAIN]: {
    action: cta,
  },
  [FAILED_CTA.USE_ANOTHER_BANK_ACC]: {
    action: () => cta(Routes[ROUTE_NAME.BANK_ACCOUNT].url),
  },
  [FAILED_CTA.USE_ANOTHER_VPA]: {
    action: cta,
  },
  [FAILED_CTA.USE_BANK_TRANSFER]: {
    action: cta,
  },
  [FAILED_CTA.CONTINUE]: {
    action: cta,
  },
  [FAILED_CTA.GO_TO_WATCHLIST]: {
    action: () => cta(Routes[ROUTE_NAME.WATCHLIST_DASHBOARD].url),
  },
  [FAILED_CTA.RESET_AMOUNT]: {
    action: cta,
  },
  [FAILED_CTA.SET_ANOTHER_AUTOPAY]: {
    action: () => cta(Routes[ROUTE_NAME.AUTOPAY].url),
  },
  [FAILED_CTA.SET_MPIN]: {
    action: cta,
  },
  [FAILED_CTA.TRANSFER_LOWER_AMT]: {
    action: cta,
  },
  [FAILED_CTA.TRY_AGAIN_LATER]: {
    action: cta,
  },
  [FAILED_CTA.USE_ANOTHER_UPI_APP]: {
    action: cta,
  },
}[key]);

export {
  MANAGE_FUNDS,
  FUNDS_STATUS,
  TRANSACTION_STATUS_ICON_MAP,
  TRANSACTION_HEADER_ICON,
  ORDER_STATUS,
  ORDER_TYPE,
  TRANSACTION_TYPE,
  LEDGER_TRANSACTION_STATUS,
  LEDGER_TRANSACTION_TYPE,
  LEDGER_POPER_MESSAGE,
  APPLIED_COUPON_STATUS,
  REQUESTED_AMOUNT,
  ALGO_TRADING,
  ALGO_REDIRECT_TO_FUNDS,
  FAILED_CTA,
  FAILED_BTN,
  FAILED_RETRY,
  FAILED_CLOSE,
};
