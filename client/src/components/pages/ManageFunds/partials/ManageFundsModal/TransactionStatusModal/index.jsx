import React, { useEffect, useState, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { ROUTE_NAME } from '@utils/enum';
import { sendEvent, sendPageEvent, sendEventPaymentsRevamp } from '@service/AnalyticsService';
import { FUNDS_PAGE_GA } from '@constants/ga-events';
import LocalStorage from '@service/LocalStorage';
import { cancelPayout, checkFirstPayin, getFixBroker } from '@pages/ManageFunds/api';
import { usePostApi } from '@common/UseApi';
import { formatTimestampToDateTime } from '@utils';
import { PAYMENT_EVENTS, SESSION_STORAGE } from '@pages/ManageFunds/enums';
import AddFundsInfoModalView from './AddFundsInfoModalView';
import { ALGO_TRADING, FUNDS_STATUS, ORDER_TYPE } from '../config';
import WithdrawFundsInfoModal from './WithdrawFundsInfoModal';
import Routes from '../../../../../../routes';

const TransactionStatusModal = ({
  transactionInfo,
  retryPayment = () => { },
  showStatusCtaBtn,
  type,
  pageSource,
  closeModal,
  fetchTransactionHistory,
  paymentMethodName = '',
  vpaHandle = '',
}) => {
  const history = useHistory();
  const [algoData, setAlgoData] = useState({});
  const [isFirstPayin, setIsFirstPayin] = useState(false);
  const { makeRequest: makeCancelRequest } = usePostApi();

  const fetchPayin = useCallback(async () => {
    try {
      const { data } = await checkFirstPayin(transactionInfo.id);
      setIsFirstPayin(data?.is_first_payin);
    } catch (err) { }
  }, [transactionInfo.id]);

  useEffect(() => {
    const cachedData = { ...LocalStorage.get(ALGO_TRADING) };
    const initiateAlgoFlow = async () => {
      try {
        if (cachedData?.algo) {
          const data = await getFixBroker();
          const obj = {
            plan_id: cachedData?.plan_id,
            fund_description: data?.fund_description,
          };
          setAlgoData(obj);
          LocalStorage.deleteItem(ALGO_TRADING);
        }
      } catch (err) { }
    };
    initiateAlgoFlow();
    fetchPayin();
  }, [fetchPayin]);

  useEffect(() => {
    if (transactionInfo) {
      const { status, isFirstPayin } = transactionInfo;
      let txnStatus = 'others';
      if (status === FUNDS_STATUS.FAILED) {
        txnStatus = 'Failed';
      } else if (status === FUNDS_STATUS.CANCELLED) {
        txnStatus = 'Cancelled';
      } else if (status === FUNDS_STATUS.SUCCESS) {
        txnStatus = 'Success';
      } else if (status === FUNDS_STATUS.PENDING) {
        txnStatus = 'Pending';
      }
      const errorCode = transactionInfo?.error_code
        || transactionInfo?.errorCode;
      sendEventPaymentsRevamp({
        event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
        event_action: PAYMENT_EVENTS.EVENT_ACTION.PAYMENT_CONFIRMATION,
        screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_confirmation,
        vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
        event_label2: txnStatus,
        event_label3: sessionStorage.getItem(SESSION_STORAGE.PAYMENT_METHOD),
        event_label4: vpaHandle,
        event_label5: errorCode,
      }, isFirstPayin);
      sendEvent({
        event_category: FUNDS_PAGE_GA.SUMMARY_DISPLAYED.EVENT_CATEGORY(type.toLowerCase()),
        event_action: FUNDS_PAGE_GA.SUMMARY_DISPLAYED.EVENT_ACTION,
        event_label: FUNDS_PAGE_GA.SUMMARY_DISPLAYED.EVENT_LABEL(
          pageSource,
          transactionInfo?.payment_type?.toLowerCase(),
          txnStatus,
        ),
      });
      if (isFirstPayin) {
        sendEvent({
          event_category: FUNDS_PAGE_GA.FIRST_PAYIN.EVENT_CATEGORY,
          event_action: FUNDS_PAGE_GA.FIRST_PAYIN.EVENT_ACTION,
          event_label: FUNDS_PAGE_GA.FIRST_PAYIN.EVENT_LABEL({
            amt: transactionInfo?.amount,
            dateTime: formatTimestampToDateTime(transactionInfo?.time),
            txnStatus,
          }),
        });
      }
    }
  }, [isFirstPayin, pageSource, transactionInfo, type, paymentMethodName, vpaHandle]);

  const { status, amount, id } = transactionInfo;

  const cancelWithdrawFunds = useCallback(async () => {
    try {
      const body = { amount, id };
      await makeCancelRequest(cancelPayout(body));
      fetchTransactionHistory();
      closeModal();
    } catch (error) { }
  }, [amount, closeModal, fetchTransactionHistory, id, makeCancelRequest]);

  if (!transactionInfo) { return null; }

  const RequiredModal = (type === ORDER_TYPE.ADD) ? AddFundsInfoModalView : WithdrawFundsInfoModal;
  const redirectToWatchlist = () => {
    history.push(Routes[ROUTE_NAME.WATCHLIST_DASHBOARD].url);
  };

  const redirectTo = () => {
    if (algoData?.fund_description) {
      window.open(algoData?.fund_description.tradingCta.cta, '_self');
    } else {
      redirectToWatchlist();
    }
  };

  const redirectToHomePage = () => history.push(Routes[ROUTE_NAME.HOME].url);
  switch (status) {
    case FUNDS_STATUS.SUCCESS:
      return (
        <RequiredModal
          ctaText={algoData?.fund_description ? algoData?.fund_description?.tradingCta?.title : 'Go To Watchlist'}
          onCtaClick={redirectTo}
          transactionInfo={transactionInfo}
          status={FUNDS_STATUS.SUCCESS}
          showStatusCtaBtn={showStatusCtaBtn}
          algoNote={algoData?.fund_description?.note}
          paymentMethodName={paymentMethodName}
          vpaHandle={vpaHandle}
        />
      );
    case FUNDS_STATUS.CANCELLED:
      return (
        <RequiredModal
          ctaText="Go To HomePage"
          onCtaClick={algoData?.fund_description ? redirectToHomePage : redirectToWatchlist}
          transactionInfo={transactionInfo}
          status={FUNDS_STATUS.CANCELLED}
          showStatusCtaBtn={showStatusCtaBtn}
          algoNote={algoData?.fund_description?.note}
          paymentMethodName={paymentMethodName}
          vpaHandle={vpaHandle}
        />
      );
    case FUNDS_STATUS.FAILED:
      return (
        <RequiredModal
          ctaText="Retry Payment"
          transactionInfo={transactionInfo}
          status={FUNDS_STATUS.FAILED}
          onCtaClick={() => {
            retryPayment(amount, algoData?.plan_id);
            sendEvent({
              event_category: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_CATEGORY(type.toLowerCase()),
              event_action: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_ACTION,
              event_label: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_LABEL(
                pageSource, transactionInfo?.payment_type.toLowerCase(),
              ),
            });
          }}
          showStatusCtaBtn={showStatusCtaBtn}
          algoNote={algoData?.fund_description?.note}
          closeModal={closeModal}
          retryPayment={() => retryPayment(amount, algoData?.plan_id)}
          paymentMethodName={paymentMethodName}
          vpaHandle={vpaHandle}
        />
      );
    default:
      return (
        <RequiredModal
          ctaText="Go To Homepage"
          onCtaClick={algoData?.fund_description ? redirectToHomePage : redirectToWatchlist}
          transactionInfo={transactionInfo}
          status={FUNDS_STATUS.OTHERS}
          showStatusCtaBtn={showStatusCtaBtn}
          algoNote={algoData?.fund_description?.note}
          cancelWithdrawFunds={cancelWithdrawFunds}
          paymentMethodName={paymentMethodName}
          vpaHandle={vpaHandle}
        />
      );
  }
};

export default TransactionStatusModal;

