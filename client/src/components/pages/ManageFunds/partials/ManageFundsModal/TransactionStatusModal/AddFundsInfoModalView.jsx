import React from 'react';
import { ICON_NAME, STATICS } from '@common/Icon/enums';
import Icon from '@common/Icon';
import btnStyles from '@commonStyles/button.scss';
import CliboardCopy from '@common/CliboardCopy';
import ModalStatusHeader from '@common/ModalStatusHeader';
import LocalStorage from '@service/LocalStorage';
import { classNames as cx } from '@utils/style';
import { formatPrice, formatTimestampToDateTime, sanitizeHtml } from '@utils';
import TransactionDetails from '../TransactionDetails';
import { TRANSACTION_HEADER_ICON } from '../config';
import styles from './index.scss';
import { TRIAL_DEBIT, SUBSCRIPTION_META_INFO } from '../../../../../../layout/LoggedInLayout/UserConsent/enum';
import { TXN_STATUS } from '../../../../../modules/MiniAppUpgrade/enums';
import FailureCta from './FailureCta';
import { sendEvent, sendEventPaymentsRevamp } from '@service/AnalyticsService';

const AddFundsInfoModalView = ({
  transactionInfo,
  status,
  showStatusCtaBtn = false,
  ctaText = '',
  onCtaClick = () => { },
  algoNote,
  closeModal,
  retryPayment,
  paymentMethodName = '',
  vpaHandle = '',
}) => {
  if (!transactionInfo) { return null; }
  const {
    id,
    amount = 0,
    time,
    status_label: statusLabel,
    status_msg: statusMessage = 'Your top-up is pending.',
    payment_type: paymentType,
    bank_name: bankName,
    bank_acc_no: bankAccNo,
    cta_list = [],
    isFirstPayin,
  } = transactionInfo;
  const transactionDetailsTableData = [
    {
      label: 'Transaction ID',
      value: (
        <div className={styles.field}>
          <div className={styles.fieldValue}>{id}</div>
          <CliboardCopy value={id} />
        </div>
      ),
    }, {
      label: 'Transaction Date',
      value: time ? formatTimestampToDateTime(time) : '',
    }, {
      label: 'Payment Method',
      value: paymentType,
    }, {
      label: 'Bank Name',
      value: bankName,
    }, {
      label: 'Account No.',
      value: bankAccNo,
    },
  ];
  const transactionDetailsWrapper = (showStatusCtaBtn) ? styles.transactionDetailsWithButton
    : styles.transactionDetailsWithoutButton;

  const getCoupnAppliedNote = () => {
    const transactionsInfo = JSON.parse(localStorage.getItem('transactionMetaInfo'));
    if (transactionsInfo && Object.keys(transactionsInfo).length > 0) {
      const couponInfo = transactionsInfo[id];
      if (couponInfo) {
        return couponInfo.text;
      }
    }
    return null;
  };

  const getSubscriptionsMetaInfo = () => {
    const subscriptionMetaInfo = JSON.parse(LocalStorage.get(SUBSCRIPTION_META_INFO));
    if (subscriptionMetaInfo
      && (subscriptionMetaInfo?.trialDebit === TRIAL_DEBIT.AUTO
        || subscriptionMetaInfo?.trialDebit === TRIAL_DEBIT.CONSENT)
      && (subscriptionMetaInfo?.trialEndDate) && status == TXN_STATUS.SUCCESS) {
      return subscriptionMetaInfo?.trialEndDate;
    }
    return null;
  };

  const subscriptionInfo = getSubscriptionsMetaInfo();

  return (
    <div className={styles.wrapper}>
      <div className={`${styles.headerWrapper} ${TRANSACTION_HEADER_ICON[status].class}`}>
        <ModalStatusHeader status={TRANSACTION_HEADER_ICON[status].status}>
          <div className={styles.amount}>
            ₹
            {formatPrice(amount)}
          </div>
          <div className={styles.statusTitle}>
            <Icon
              name={TRANSACTION_HEADER_ICON[status].iconName}
              size={(14 / STATICS.SIZE_MULTIPLIER)}
              className={styles.iconContainer}
            />
            <div>{statusLabel}</div>
          </div>
          <div className={styles.statusMsg}>
            {statusMessage}
          </div>
        </ModalStatusHeader>
      </div>
      <div className={cx([styles.detailsContainer, transactionDetailsWrapper], {
        [styles.removeHeight]: algoNote,
      })}
      >
        <TransactionDetails transactionDetailsTableData={transactionDetailsTableData} />
        {getCoupnAppliedNote() && (
          <div className={styles.PleaseNoteSection}>
            <div className={styles.header}>
              <span>
                <Icon
                  name={ICON_NAME.ANNOUNCEMENT_YELLOW}
                  size={4}
                />
              </span>
              <span>Please Note</span>
            </div>
            <div className={styles.message}>
              {getCoupnAppliedNote()}
            </div>
          </div>
        )}
        {subscriptionInfo
          && (
            <div className={styles.PleaseNoteSection}>
              <div className={styles.header}>
                <span>
                  <Icon
                    name={ICON_NAME.ANNOUNCEMENT_YELLOW}
                    size={4}
                  />
                </span>
                <span>Please Note</span>
              </div>
              <div className={styles.message}>
                Your subscription has been renewed from
                {' '}
                <b>{subscriptionInfo}</b>
                .
              </div>
            </div>
          )}
      </div>
      {algoNote ? (
        <div className={styles.algoNote}>
          <div className={styles.header}>
            <Icon name={ICON_NAME.ANNOUNCEMENT_YELLOW} />
            Please Note
          </div>
          <div className={styles.notes} dangerouslySetInnerHTML={{ __html: sanitizeHtml(algoNote) }} />
        </div>
      ) : null}
      {!cta_list.length && showStatusCtaBtn && (
        <div className={styles.buttonContainer}>
          <button
            id="addFundsCta"
            onClick={() => {
              sendEventPaymentsRevamp({
                event_category: 'addmoney_equity_web',
                event_action: 'cta_clicked',
                screenName: '/equity_payment_confirmation',
                vertical_name: 'payments',
                event_label2: statusLabel,
                event_label3: ctaText,
              }, isFirstPayin);
              onCtaClick();
            }}
            className={cx([btnStyles.btn, btnStyles.greenBtnFill, styles.retryPaymentBtn], {
              [styles.algoBtn]: algoNote,
            })}
          >
            {ctaText}
          </button>
        </div>
      )}
      <FailureCta
        cta_list={cta_list}
        closeModal={closeModal}
        retryPayment={retryPayment}
        transactionInfo={transactionInfo}
        isFirstPayin={isFirstPayin}
        paymentMethodName={paymentMethodName || transactionInfo?.payment_type || ''}
        vpaHandle={vpaHandle || ((transactionInfo?.vpa || '').split('@')[1]) || ''}
      />
    </div>
  );
};

export default AddFundsInfoModalView;

