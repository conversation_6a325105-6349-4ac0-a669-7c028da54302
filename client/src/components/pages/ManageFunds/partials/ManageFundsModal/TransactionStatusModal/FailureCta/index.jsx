import React from 'react';
import { useHistory } from 'react-router-dom';
import btnStyles from '@commonStyles/button.scss';
import { classNames as cx } from '@utils/style';
import { sendEventPaymentsRevamp } from '@service/AnalyticsService';
import { PAYMENT_EVENTS } from '@pages/ManageFunds/enums';
import styles from './index.scss';
import { FAILED_BTN, FAILED_CLOSE, FAILED_RETRY } from '../../config';

function FailureCta({
  cta_list, closeModal, retryPayment, transactionInfo, isFirstPayin, paymentMethodName = '', vpaHandle = '',
}) {
  const history = useHistory();

  const handleClick = (btn) => {
    const methodName = paymentMethodName || transactionInfo?.payment_type || '';
    const vpa = vpaHandle || ((transactionInfo?.vpa || '').split('@')[1]) || '';
    const txnStatus = (transactionInfo?.status_label || transactionInfo?.status || '').toString().toLowerCase();
    sendEventPaymentsRevamp({
      event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
      event_action: PAYMENT_EVENTS.EVENT_ACTION.CTA_CLICKED,
      screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_confirmation,
      vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
      event_label2: txnStatus,
      event_label3: methodName,
      event_label4: vpa,
      event_label5: btn.name,
    }, isFirstPayin);
    if (btn.type === FAILED_RETRY) {
      retryPayment();
    } else if (btn.type === FAILED_CLOSE) {
      closeModal();
    } else if (btn.type === FAILED_BTN) {
      history.push(btn.redirectTo);
    }
  };
  return (cta_list?.length ? (
    <div className={styles.failureCtaWrapper}>
      {cta_list.map((btn, index) => (

        <button
          key={index}
          onClick={() => handleClick(btn)}
          className={cx([btnStyles.btn, btnStyles.secondaryBtnFill], {
            [styles.transparentBtnFill]: cta_list.length > 1 && index === 0,
            [styles.maxWidth]: cta_list.length === 1,
          })}
        >
          {btn.name}
        </button>
      ))}
    </div>
  ) : null
  );
}

export default FailureCta;

