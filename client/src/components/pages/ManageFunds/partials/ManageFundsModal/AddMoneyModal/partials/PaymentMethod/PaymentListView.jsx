import React, { useContext } from 'react';
import RadioButton from '@common/RadioButton';
import {
  PAYMENT_GATEWAYS, PPBL_ID, PYTM_IFSC, UPI_ID, errDisableMsg,
} from '@pages/ManageFunds/config';
import { FUNDS_PAGE_GA } from '@constants/ga-events';
import { UserContext } from '@layout/App/UserContext';
import { classNames as cx } from '@utils/style';
import { sendEvent } from '@service/AnalyticsService';
import { removeWhiteSpaceFromArray } from '@pages/ManageFunds/utils';
import { SESSION_STORAGE } from '@pages/ManageFunds/enums';
import Balance from './Balance';
import { VpaSuggestions } from './VpaSuggestions';
import styles from './index.scss';

const ProccedButton = ({
  onClick, className, disabled, buttonText, vpaList, paymentMethodId, paymentGateway, vpa, setVpaInput,
}) => (
  <>
    <button
      id="fundsModalFooterButton"
      onClick={onClick}
      className={className}
      disabled={disabled}
    >
      {buttonText}
    </button>
    {(paymentMethodId === UPI_ID && paymentGateway !== PAYMENT_GATEWAYS.BILLDESK_PG) ? (
      <VpaSuggestions
        vpaList={vpaList}
        setActiveVpa={setVpaInput}
        activeVpa={vpa}
      />
    ) : null}
  </>
);

const PaymentListView = ({
  onOptionSelect, paymentAllowed, isPaymentAlowedSelected, id, indexKey, isPaymentMethodDisabled, isBtnSelected,
  displayName, bankAccountNumber, paymentMethodId, ppbBalance, failed, getPpbBalanceInfo, inProgress, inputAmountValue,
  paymentMethodOptionList, bankImgSrc, selected, renderVpaInput, handleClick,
  getProceedButtonClass, isProccedDisabled, disableBtn, transId, ifscCode, handlePpblErr,
  setVpaList, vpaList, paymentGateway, vpa, setVpaInput, paymentAllowedMessage, conveyedDisplayMessage, 
  isUpiSelected, setIsUpiSelected, onPaymentMethodClicked,
  // optionData, showQrModal, setShowQrModal, qrCodeBase64, qrValidTime, multiBankQrPay, onQrCancelButtonYes,
}) => {
  const { userId } = useContext(UserContext);

  const handleSelectedVpaList = () => {
    const vpaArr = paymentMethodOptionList.find(
      (bank) => (bank.bankAccountNumber === bankAccountNumber),
    )?.recentlyUsedVpa;
    const temp = new Set(removeWhiteSpaceFromArray(vpaArr)) || [];
    const tempArr = Array.from(temp) || [];
    setVpaList(tempArr);
    setVpaInput({
      vpaValue: tempArr[0] || '',
      vpaErrorMessage: '',
      isVpaValid: true,
    });
  };

  const handlePaymentClick = () => {
    try {
      handlePpblErr(ifscCode);
      onOptionSelect(paymentAllowed && !isPaymentMethodDisabled, indexKey, paymentMethodOptionList);
      onPaymentMethodClicked(indexKey);
      if (paymentAllowed && paymentMethodId === UPI_ID && paymentGateway !== PAYMENT_GATEWAYS.BILLDESK_PG) {
        handleSelectedVpaList();
      }
    } catch (e) {}
  };

  const { errMsg, showPpblMsg } = errDisableMsg({
    paymentMethodId,
    paymentAllowed,
    paymentAllowedMessage,
    conveyedDisplayMessage,
    ppbBalance,
    inputAmt: inputAmountValue,
    ifscCode,
  });

  return (
    <div className={styles.borderWrapper}>
      <div
        role="presentation"
        onClick={handlePaymentClick}
        key={id}
        className={cx(styles.optionWrapper, {
          [styles.disabledOption]: !paymentAllowed || isPaymentMethodDisabled,
        })}
      >
        <div className={styles.optionContainer}>
          <div className={styles.bankInfoContainer}>
            <RadioButton
              selected={isBtnSelected && !isUpiSelected}
              className={isBtnSelected ? styles.selectedMethod : styles.notSelected}
              onRadioClick={() => {
                setIsUpiSelected(false);
                sessionStorage.setItem(SESSION_STORAGE.PAYMENT_METHOD, SESSION_STORAGE.PAYMENT_METHOD_TYPE.NET_BANKING);
                sendEvent({
                  vertical_name: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.VERITCAL_NAME,
                  event_category: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_CATEGORY,
                  event_action: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_ACTION,
                  screenName: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.SCREEN_NAME,
                  eventName: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_NAME,
                  event_label: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_LABEL({
                    customerId: userId,
                  }),
                  event_label2: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_LABEL1({
                    transactionId: transId,
                  }),
                  event_label3: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_LABEL2({
                    paymentMethod: paymentMethodId,
                  }),
                  event_label5: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.EVENT_LABEL4({
                    upi_intent: paymentMethodId === 5 ? 'yes' : 'no',
                  }),
                  value: FUNDS_PAGE_GA.ADD_FUNDS_OPTIONS.VALUE({
                    value: inputAmountValue,
                  }),
                });
              }}
            />
            <div className={cx(styles.bankInfo, {
              [styles.disabledBank]: !paymentAllowed,
            })}
            >
              <div className={styles.bankName}>{displayName}</div>
              {
              bankAccountNumber
                ? (
                  <div className={styles.accNo}>
                    A/c No.
                    {bankAccountNumber}
                  </div>
                )
                : null
            }
              {
              ppbBalance !== null && ifscCode === PYTM_IFSC
                ? (
                  <Balance
                    balance={ppbBalance}
                    failed={failed}
                    getPpbBalanceInfo={getPpbBalanceInfo.bind(null, PPBL_ID)}
                    inProgress={inProgress}
                    amount={inputAmountValue}
                    paymentAllowed={paymentAllowed}
                    showPpblMsg={showPpblMsg}
                  />
                )
                : null
              }
              <div className={styles.balanceInfo}>
                {errMsg}
              </div>
            </div>
          </div>
          <div className={styles.bankImageContainer}>
            <img
              alt="bank"
              src={bankImgSrc?.split(' ').join('')}
            />
          </div>
        </div>
      </div>
      {paymentMethodOptionList.length - 1 === indexKey
      && paymentMethodId === UPI_ID && selected
        ? renderVpaInput(vpa) : null}
      {(paymentMethodOptionList.length - 1 === indexKey
      && selected && isPaymentAlowedSelected && !isPaymentMethodDisabled) ? (
        <ProccedButton
          onClick={handleClick}
          className={getProceedButtonClass(isPaymentAlowedSelected, paymentMethodId)}
          disabled={isProccedDisabled() || disableBtn}
          buttonText={`PAY ₹${inputAmountValue}`}
          bankAccountNumber={bankAccountNumber}
          vpaList={vpaList}
          paymentMethodId={paymentMethodId}
          paymentGateway={paymentGateway}
          setVpaInput={setVpaInput}
          vpa={vpa}
        />
        ) : null}
    </div>
  );
};

export default PaymentListView;
