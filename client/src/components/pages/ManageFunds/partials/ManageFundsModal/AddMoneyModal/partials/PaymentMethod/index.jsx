import React, { useRef } from "react";
import { Link } from "react-router-dom";
import { classNames as cx } from "@utils/style";
import {
  PPB_ID,
  UPI_ID,
  BANK_TRANSFER_ID,
  PAYMENT_GATEWAYS,
  NETBANKING_ID,
  PYTM_IFSC,
} from "@pages/ManageFunds/config";
import { ROUTE_NAME } from "@utils/enum";
import btnStyles from "@commonStyles/button.scss";
import { hasWhiteSpace } from "@pages/ManageFunds/utils";
import Routes from "@/routes";
import BankTransfer from "./BankTransfer";
import styles from "./index.scss";
import QuickRecommend from "./QuickRecommended";
import UPI from "./UPI";
import NetBanking from "./NetBanking";

function PaymentMethod({
  optionData,
  selectedOptionIndex = 0,
  methodIndex,
  onOptionClicked = () => {},
  onPaymentMethodClicked = () => {},
  selected = false,
  ppbBalance,
  getPpbBalanceInfo,
  inProgress,
  failed,
  inputAmountValue,
  onBtnClick,
  disableBtn,
  setDisableBtn,
  renderVpaInput,
  vpa,
  setShowMore,
  isEnabled,
  handleEnableTransferClick,
  userVanDetails,
  vanInProgress,
  paymentGatewayDecider,
  paymentGateway,
  isTpvFlow,
  transId,
  multiBankUpiPay,
  multiBankQrPay,
  onQrCancelButtonYes,
  onQrExpiry,
  qrCodeBase64,
  qrValidTime,
  showQrModal,
  setShowQrModal,
  defaultSelectedPayment,
  setVpaInput,
  isFirstPayin,
  isUpiSelected,
  setIsUpiSelected,
}) {
  const ifscRef = useRef(defaultSelectedPayment?.ifscCode);
  if (!optionData) {
    return null;
  }

  const {
    paymentMethodOptionList,
    paymentMethodId,
    tariffDisplay,
    isPaymentMethodDisabled = false,
    disableMessage = "",
  } = optionData;

  const handlePpblErr = (ifsc) => {
    ifscRef.current = ifsc;
  };

  const onOptionSelect = (paymentAllowed, index) => {
    console.log('onOptionSelect', { paymentAllowed, index, paymentMethodOptionList });
    if (paymentAllowed) {
      onPaymentMethodClicked(methodIndex);
      onOptionClicked(index, paymentMethodOptionList);
      if (isTpvFlow) paymentGatewayDecider(methodIndex, index);
    }
  };

  const isProccedDisabled = () => {
    if (
      ppbBalance !== null &&
      ifscRef.current === PYTM_IFSC &&
      ppbBalance < inputAmountValue
    )
      return true;
    if (
      paymentMethodId === UPI_ID &&
      paymentGateway !== PAYMENT_GATEWAYS.BILLDESK_PG &&
      (!vpa.length || hasWhiteSpace(vpa))
    )
      return true;
    return false;
  };

  const getDefaultVpa = () =>
    paymentMethodOptionList
      .find((_, index) => index === selectedOptionIndex)
      ?.recentlyUsedVpa?.[0]?.replace(/\s+/g, "") || "";

  const getProceedButtonClass = (paymentAllowed) =>
    cx([btnStyles.btn, styles.proceedButton], {
      [btnStyles.greenBtnFill]: paymentAllowed && !disableBtn,
      [btnStyles.disabledBtn]:
        disableBtn || !paymentAllowed || isProccedDisabled(),
      [btnStyles.disabledBtnFill]:
        disableBtn || !paymentAllowed || isProccedDisabled(),
    });

  const handleClick = () => {
    onBtnClick();
    setDisableBtn(true);
  };

  const renderPaymentOptions = () => {
    switch (paymentMethodId) {
      case PPB_ID:
        return (
          <QuickRecommend
            paymentMethodOptionList={paymentMethodOptionList}
            selectedOptionIndex={selectedOptionIndex}
            selected={selected}
            onOptionSelect={onOptionSelect}
            isPaymentMethodDisabled={isPaymentMethodDisabled}
            ppbBalance={ppbBalance}
            failed={failed}
            getPpbBalanceInfo={getPpbBalanceInfo}
            inProgress={inProgress}
            inputAmountValue={inputAmountValue}
            disableMessage={disableMessage}
            renderVpaInput={renderVpaInput}
            handleClick={handleClick}
            getDefaultVpa={getDefaultVpa}
            getProceedButtonClass={getProceedButtonClass}
            isProccedDisabled={isProccedDisabled}
            disableBtn={disableBtn}
            paymentMethodId={paymentMethodId}
            transId={transId}
            handlePpblErr={handlePpblErr}
          />
        );
      case UPI_ID:
        return (
          <UPI
            optionData={optionData}
            paymentMethodOptionList={paymentMethodOptionList}
            selectedOptionIndex={selectedOptionIndex}
            selected={selected}
            onOptionSelect={onOptionSelect}
            isPaymentMethodDisabled={isPaymentMethodDisabled}
            ppbBalance={ppbBalance}
            failed={failed}
            getPpbBalanceInfo={getPpbBalanceInfo}
            inProgress={inProgress}
            inputAmountValue={inputAmountValue}
            disableMessage={disableMessage}
            renderVpaInput={renderVpaInput}
            handleClick={handleClick}
            getDefaultVpa={getDefaultVpa}
            getProceedButtonClass={getProceedButtonClass}
            isProccedDisabled={isProccedDisabled}
            disableBtn={disableBtn}
            multiBankUpiPay={() => {
              multiBankUpiPay();
              setDisableBtn();
            }}
            multiBankQrPay={multiBankQrPay}
            onQrCancelButtonYes={onQrCancelButtonYes}
            onQrExpiry={onQrExpiry}
            qrCodeBase64={qrCodeBase64}
            qrValidTime={qrValidTime}
            showQrModal={showQrModal}
            setShowQrModal={setShowQrModal}
            setDisableBtn={setDisableBtn}
            paymentMethodId={paymentMethodId}
            transId={transId}
            vpa={vpa}
            handlePpblErr={handlePpblErr}
            paymentGateway={paymentGateway}
            setVpaInput={setVpaInput}
            isFirstPayin={isFirstPayin}
            isUpiSelected={isUpiSelected}
            setIsUpiSelected={setIsUpiSelected}
          />
        );
      case NETBANKING_ID:
        return (
          <NetBanking
            paymentMethodOptionList={paymentMethodOptionList}
            selectedOptionIndex={selectedOptionIndex}
            selected={selected}
            onOptionSelect={onOptionSelect}
            isPaymentMethodDisabled={isPaymentMethodDisabled}
            ppbBalance={ppbBalance}
            failed={failed}
            getPpbBalanceInfo={getPpbBalanceInfo}
            inProgress={inProgress}
            inputAmountValue={inputAmountValue}
            disableMessage={disableMessage}
            renderVpaInput={renderVpaInput}
            handleClick={handleClick}
            getDefaultVpa={getDefaultVpa}
            getProceedButtonClass={getProceedButtonClass}
            isProccedDisabled={isProccedDisabled}
            disableBtn={disableBtn}
            paymentMethodId={paymentMethodId}
            transId={transId}
            handlePpblErr={handlePpblErr}
            isUpiSelected={isUpiSelected}
            setIsUpiSelected={setIsUpiSelected}
          />
        );
      case BANK_TRANSFER_ID:
        return (
          <BankTransfer
            data={optionData}
            userVanDetails={userVanDetails}
            setShowMore={setShowMore}
            isEnabled={isEnabled}
            handleEnableTransferClick={handleEnableTransferClick}
            inProgress={vanInProgress}
            transId={transId}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.paymentMethodContainer}>
      {renderPaymentOptions()}
    </div>
  );
}

export default PaymentMethod;
