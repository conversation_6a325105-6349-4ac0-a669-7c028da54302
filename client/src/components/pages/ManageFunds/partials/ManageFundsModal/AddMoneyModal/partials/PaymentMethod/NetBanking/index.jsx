import React from 'react';
import PaymentListView from '../PaymentListView';

function NetBanking({
  onOptionSelect, isPaymentMethodDisabled, ppbBalance, failed, getPpbBalanceInfo, inProgress, inputAmountValue,
  paymentMethodOptionList, disableMessage, selected, renderVpaInput, handleClick, getDefaultVpa,
  getProceedButtonClass, isProccedDisabled, disableBtn, selectedOptionIndex, paymentMethodId, transId, handlePpblErr,
  isUpiSelected, setIsUpiSelected, onPaymentMethodClicked,
}) {
  let isPaymentAlowedSelected = false;
  return (paymentMethodOptionList?.map((option, index) => {
    const {
      id,
      displayName,
      bankAccountNumber,
      bankImgSrc,
      paymentAllowed,
      ifscCode,
      paymentAllowedMessage,
      conveyedDisplayMessage,
    } = option;
    const isBtnSelected = selectedOptionIndex === index && selected;
    if (isBtnSelected) {
      isPaymentAlowedSelected = option.paymentAllowed;
    }
    return (
      <PaymentListView
        key={index}
        id={id}
        paymentAllowedMessage={paymentAllowedMessage}
        conveyedDisplayMessage={conveyedDisplayMessage}
        displayName={displayName}
        bankAccountNumber={bankAccountNumber}
        bankImgSrc={bankImgSrc}
        paymentAllowed={paymentAllowed}
        isPaymentAlowedSelected={isPaymentAlowedSelected}
        paymentMethodId={paymentMethodId}
        onOptionSelect={onOptionSelect}
        indexKey={index}
        isPaymentMethodDisabled={isPaymentMethodDisabled}
        isBtnSelected={isBtnSelected && !isUpiSelected}
        ppbBalance={ppbBalance}
        failed={failed}
        getPpbBalanceInfo={getPpbBalanceInfo}
        inProgress={inProgress}
        inputAmountValue={inputAmountValue}
        paymentMethodOptionList={paymentMethodOptionList}
        disableMessage={disableMessage}
        selected={selected}
        renderVpaInput={renderVpaInput}
        handleClick={handleClick}
        getDefaultVpa={getDefaultVpa}
        getProceedButtonClass={getProceedButtonClass}
        isProccedDisabled={isProccedDisabled}
        disableBtn={disableBtn}
        transId={transId}
        ifscCode={ifscCode}
        handlePpblErr={handlePpblErr}
        isUpiSelected={isUpiSelected}
        setIsUpiSelected={setIsUpiSelected}
        onPaymentMethodClicked={onPaymentMethodClicked}
      />
    );
  })
  );
}
export default NetBanking;
