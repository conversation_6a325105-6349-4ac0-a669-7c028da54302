/* eslint-disable import/no-unresolved */
import React, { useState, useEffect, useRef } from 'react';
import { classNames as cx } from '@utils/style';
import btnStyles from '@commonStyles/button.scss';
import RadioButton from '@common/RadioButton';
import { sendEventPaymentsRevamp } from '@service/AnalyticsService';
import { PAYMENT_EVENTS, PAYMENT_MODE, SESSION_STORAGE } from '@pages/ManageFunds/enums';
import { UPI_OPTION } from '../../../enums';
import styles from './index.scss';

function formatTime(secs) {
  const m = String(Math.floor(secs / 60)).padStart(2, '0');
  const s = String(secs % 60).padStart(2, '0');
  return `${m}:${s}s`;
}

const QRCode = ({
  selectedUpiOption,
  setSelectedUpiOption,
  showQrModal,
  setShowQrModal,
  qrCodeBase64,
  qrValidTime,
  paymentMethodOptionList,
  multiBankQrPay,
  onQrCancelButtonYes,
  onQrExpiry,
  isFirstPayin,
  onUpiRadioClick,
  isUpiSelected,
}) => {
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const [qrTimer, setQrTimer] = useState(qrValidTime / 1000);
  const [showBanks, setShowBanks] = useState();
  const timerRef = useRef();

  const delayedQrExpiry = () => {
    setTimeout(() => {
      onQrExpiry();
    }, 3000);
  };

  useEffect(() => {
    if (!showQrModal) return undefined;

    timerRef.current = setInterval(() => {
      setQrTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          setTimeout(onQrExpiry, 3000);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timerRef.current);
  }, [showQrModal, onQrExpiry]);

  function onGenerateQrCode() {
    sendEventPaymentsRevamp({
      event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
      event_action: PAYMENT_EVENTS.EVENT_ACTION.GENERATE_QR_WEB,
      screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
      vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
    }, isFirstPayin);
    multiBankQrPay();
    setShowQrModal(true);
  }

  function onQrCancelButton() {
    sendEventPaymentsRevamp({
      event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
      event_action: PAYMENT_EVENTS.EVENT_ACTION.CROSS_BUTTON_QR_WEB,
      screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
      vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
      event: PAYMENT_EVENTS.EVENT_NAME.CUSTOM_EVENT,
      event_label: PAYMENT_EVENTS.EVENT_LABEL,
      event_label2: 'yes',
    }, isFirstPayin);
    try {
      onQrCancelButtonYes();
    } catch (err) {
    } finally {
      setShowCancelPopup(false);
      setShowQrModal(false);
    }
  }

  function onUpiRadioClk() {
    onUpiRadioClick(true);
    sessionStorage.setItem(SESSION_STORAGE.PAYMENT_METHOD, SESSION_STORAGE.PAYMENT_METHOD_TYPE.UPI_QR);
  }

  return (
    <div
      className={
        selectedUpiOption === 'qr'
          ? `${styles.upiRadioBox} ${styles.qrRadioBoxSelected}`
          : `${styles.upiRadioBox} ${styles.qrRadioBoxUnselected}`
      }
    >
      <div
        className={styles.qrRadioButton}
        onClick={() => setSelectedUpiOption('qr')}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => {
          if (e.key === 'Enter' || e.key === ' ') setSelectedUpiOption('qr');
        }}
      >
        <RadioButton
          selected={selectedUpiOption === 'qr' && isUpiSelected}
          onRadioClick={() => {
            onUpiRadioClk();
            if (selectedUpiOption !== UPI_OPTION.QR || !isUpiSelected) {
              sendEventPaymentsRevamp({
                event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                event_action: PAYMENT_EVENTS.EVENT_ACTION.PAYMENT_OPTIONS_CLICKED_WEB,
                screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
                vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                event_label3: PAYMENT_MODE.UPI_QR,
                event_label4: 'NA',
              }, isFirstPayin);
            }
          }}
        />
        <span className={styles.qrRadioLabel}>Scan QR Code</span>
      </div>
      <div>
        {/* Always show this text unless QR is selected */}
        {selectedUpiOption !== 'qr' && (
          <div className={styles.qrSubheading}>
            Use any UPI app to scan and pay from a bank account registered to
            Paytm Money, as listed below
          </div>
        )}
        {/* Existing conditional text for when QR is selected */}
        {selectedUpiOption === 'qr' ? (
          <>
            <div className={styles.qrSubheading}>
              Scan and pay using any UPI app linked to your Paytm Money account.
            </div>
            <div className={styles.qrDemoWrapper}>
              <div className={styles.qrDemoBg}>
                <img
                  src="https://static.paytmmoney.com/images/advisors/rest_pages_QR.png"
                  alt="Demo QR"
                  className={styles.qrDemoImg}
                />
              </div>
            </div>
            <button
              className={`${btnStyles.btn} ${btnStyles.primaryBtnFill} ${styles.qrModalGenerateBtn}`}
              onClick={onGenerateQrCode}
            >
              Generate QR Code
            </button>
          </>
        ) : null}
        {selectedUpiOption === 'qr' && (
          <div
            style={{
              marginTop: '1.5rem',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            {showQrModal && (
              <div className={styles.qrModalBackdrop}>
                <div className={cx(
                  styles.qrModal,
                  { [styles.qrModalExpired]: qrTimer === 0 },
                )}>
                  <div
                    className={cx(styles.qrModalHeader, {
                      [styles.qrModalContentBlur]: showCancelPopup,
                    })}
                  >
                    <div className={styles.qrModalTitle}>
                      <span className={styles.qrModalTitleText}>
                        Scan the QR using any UPI App on your phone
                      </span>
                      <button
                        className={styles.qrModalCloseBtn}
                        onClick={() => {
                          if (qrTimer === 0) {
                            setShowQrModal(false);
                          } else {
                            setShowCancelPopup(true);
                          }
                        }}
                        aria-label="Close"
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="12"
                            fill="#222222"
                            fillOpacity="0.08"
                          />
                          <path
                            d="M16 8L8 16M8 8L16 16"
                            stroke="#222222"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    className={cx(styles.qrModalContent, {
                      [styles.qrModalContentBlur]:
                        showCancelPopup || qrTimer === 0,
                    })}
                  >
                    <div className={styles.qrModalQrBox}>
                      {qrCodeBase64 ? (
                        <img
                          src={`data:image/png;base64,${qrCodeBase64}`}
                          alt="QR Code"
                          className={styles.qrImage}
                        />
                      ) : (
                        'Generating QR Code...'
                      )}
                    </div>
                    {qrTimer > 0 ? (
                      <div className={styles.qrTimerBox}>
                        {`QR code is valid for ${formatTime(qrTimer)}`}
                      </div>
                    ) : null}
                  </div>
                  {qrTimer === 0 && (
                    <div className={styles.qrExpiredBox}>
                      QR code has expired
                    </div>
                  )}
                  <div
                    className={cx(styles.bankListContainer, {
                      [styles.qrModalContentBlur]:
                        showCancelPopup || qrTimer === 0,
                    })}
                  >
                    <div className={styles.bankListHeader}>
                      <span className={styles.bankListHeaderText}>
                        Pay using your registered banks only
                      </span>
                      {paymentMethodOptionList.length > 1 && (
                        <span
                          className={styles.bankListToggle}
                          onClick={() => {
                            if (!showBanks) {
                              sendEventPaymentsRevamp({
                                event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                                event_action: PAYMENT_EVENTS.EVENT_ACTION.VIEW_ALL_QR_IMAGE_WEB,
                                screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
                                vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                              }, isFirstPayin);}
                            setShowBanks((prev) => !prev);}}
                          role="button"
                          tabIndex={0}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              if (!showBanks) {
                                sendEventPaymentsRevamp({
                                  event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                                  event_action: PAYMENT_EVENTS.EVENT_ACTION.VIEW_ALL_QR_IMAGE_WEB,
                                  screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
                                  vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                                }, isFirstPayin); }
                              setShowBanks((prev) => !prev);
                            }
                          }}
                        >
                          {showBanks ? 'Hide All' : 'View All'}
                          {showBanks ? (
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              style={{ marginLeft: 4 }}
                            >
                              <path
                                d="M4 10L8 6L12 10"
                                stroke="#0057B8"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          ) : (
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              style={{ marginLeft: 4 }}
                            >
                              <path
                                d="M4 6L8 10L12 6"
                                stroke="#0057B8"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          )}
                        </span>
                      )}
                    </div>
                    {showBanks ? (
                      <div>
                        {paymentMethodOptionList.map((bank, idx) => (
                          <div key={idx} className={styles.bankListItem}>
                            <img
                              src={bank.bankImgSrc}
                              alt={bank.displayName}
                              className={styles.bankListImg}
                            />
                            <span className={styles.bankListName}>
                              {bank.displayName}
                            </span>
                            <span className={styles.bankListAcc}>
                              {bank.bankAccountNumber
                                ? `${'*'.repeat(bank.bankAccountNumber.length - 4)}${bank.bankAccountNumber.slice(-4)}`
                                : ''}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div>
                        {paymentMethodOptionList
                          .slice(0, 1)
                          .map((bank, idx) => (
                            <div key={idx} className={styles.bankListItem}>
                              <img
                                src={bank.bankImgSrc}
                                alt={bank.displayName}
                                className={styles.bankListImg}
                              />
                              <span className={styles.bankListName}>
                                {bank.displayName}
                              </span>
                              <span className={styles.bankListAcc}>
                                {bank.bankAccountNumber
                                  ? `${'*'.repeat(bank.bankAccountNumber.length - 4)}${bank.bankAccountNumber.slice(-4)}`
                                  : ''}
                              </span>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                  {showCancelPopup && (
                    <div className={styles.cancelPopup}>
                      <span className={styles.cancelPopupText}>
                        Are you sure you want to cancel transaction?
                      </span>
                      <div className={styles.cancelPopupBtnContainer}>
                        <button
                          className={`${styles.cancelPopupBtn} ${styles.cancelPopupBtnSecondary}`}
                          onClick={onQrCancelButton}
                        >
                          Yes
                        </button>
                        <button
                          className={`${styles.cancelPopupBtn} ${styles.cancelPopupBtnPrimary}`}
                          onClick={() => {
                            sendEventPaymentsRevamp({
                              event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                              event_action: PAYMENT_EVENTS.EVENT_ACTION.CROSS_BUTTON_QR_WEB,
                              screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
                              vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                              event: PAYMENT_EVENTS.EVENT_NAME.CUSTOM_EVENT,
                              event_label: PAYMENT_EVENTS.EVENT_LABEL,
                              event_label2: 'no',
                            }, isFirstPayin);
                            setShowCancelPopup(false);
                          }}
                        >
                          No
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default QRCode;
