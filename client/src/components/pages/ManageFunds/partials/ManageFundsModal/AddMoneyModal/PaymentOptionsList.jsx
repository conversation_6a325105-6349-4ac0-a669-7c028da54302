import React, {
  useCallback, useEffect, useState, useContext, useRef,
} from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import { ICON_NAME } from '@common/Icon/enums';
import EmptyState from '@common/EmptyState';
import useWebConfig from '@common/useWebConfig';
import { sendEvent, sendPageEvent, sendEventPaymentsRevamp } from '@service/AnalyticsService';
import { FUNDS_PAGE_GA } from '@constants/ga-events';
import { isEqual } from 'lodash';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import { usePostApi, useGetApi } from '@common/UseApi';
import { getEligibilityApi, createVanDetails } from '@pages/BankTransfer/api';
import { UserContext } from '@layout/App/UserContext';
import { FundsSummaryContext } from '@layout/App/FundsSummaryContext';
import { cancelProcessTxn } from '@modules/MiniAppUpgrade/api';
import LocalStorage from '@service/LocalStorage';
import { ROUTE_NAME } from '@utils/enum';
import { ADD_BANK_REDIRECT_PARAM } from '@pages/BankAccounts/enums';
import { removeWhiteSpace } from '@pages/ManageFunds/utils';
import { MODAL_TYPES } from '@common/Modal/enums';
import { formatPrice, mapQueryString } from '@utils';
// import PaytmPaymentBank from './PaytmPaymentBank';
import { button } from '@commonStyles';
import { PAYMENT_EVENTS, PAYMENT_MODE } from '@pages/ManageFunds/enums';
import Routes from '@/routes';
import UpiPayConfirmationModal from '../UpiPayConfirmationModal';
import {
  getPaymentOptions,
  getPpbBalance,
  makePayment,
  getPaymentGateway,
  validateVpa,
  getTransactionInfo,
  checkFirstPayin,
} from '../../../api';
import ManageFundsModalLayout from '../ManageFundsModalLayout';
import styles from './index.scss';
import PaymentMethod from './partials/PaymentMethod';
import VpaInput from './partials/PaymentMethod/VPAInput';
import {
  UPI_LIMIT, DISABLE_MESSAGE, UPI_ID, PPB_ID, PAYMENT_GATEWAYS,
} from '../../../config';
import KnowMore from './partials/KnowMore';
import { ALGO_REDIRECT_TO_FUNDS, ALGO_TRADING, ORDER_TYPE } from '../config';
import { TXN_STATUS } from './enums';
import TransactionStatusModal from '../TransactionStatusModal';
import { PAYMENT_TYPE } from './enums';

const PAYMENT_RESPONSE_TYPES = {
  REDIRECT: 'REDIRECT',
  UPI_TPV_FORM: 'UPI_TPV_FORM',
  UPI_QR_IMAGE: 'UPI_QR_IMAGE',
};

const PaymentOptionsList = ({
  inputAmountValue,
  paymentTransactionId,
  closeModal,
  openModal,
  pageSource,
  clientUniqueTxnId,
  transId,
}) => {
  const { userId } = useContext(UserContext);
  const { getData: refreshFundsSummary } = useContext(FundsSummaryContext);
  const { inProgress, makeRequest, failed } = usePostApi();
  const { makeRequest: makeVanDetailsReq, inProgress: vanDetailsInprogress } = useGetApi();
  const { makeRequest: makeCreateVanReq, inProgress: vanInprogress } = usePostApi({ defaultInProgress: false });
  const [isUpiSelected, setIsUpiSelected] = useState(true);

  const [ppbBalance, setPpbBalance] = useState(null);
  const [selectedMethodIndex, setSelectedMethodIndex] = useState(0);
  const [selectedOptionIndex, setSelectedOptionIndex] = useState(0);
  const [paymentMethods, setPaymentMethods] = useState(null);
  const [disableMakePayment, setDisableMakePayment] = useState(false);
  const [vpaInput, setVpaInput] = useState({
    vpaValue: '',
    vpaErrorMessage: '',
    isVpaValid: true,
  });
  const [disableVpaInput, setDisableVpaInput] = useState(false);
  const [infoToolTipData, setInfoToolTipData] = useState();
  const [paymentGateway, setPaymentGateway] = useState(null);
  const [userVanDetails, setUserVanDetails] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [disableBtn, setDisableBtn] = useState(false);
  const [isTpvFlow, setIsTpvFlow] = useState(true);
  const showUpiPayConfirmation = useRef(false);
  const { search } = useLocation();
  const history = useHistory();
  const { addToast } = useToast();
  const [isFirstPayin, setIsFirstPayin] = useState(false);
  const [isUpiOpen, setIsUpiOpen] = useState(true);
  const [isNetBankingOpen, setIsNetBankingOpen] = useState(false);
  const [isBankTransferOpen, setIsBankTransferOpen] = useState(false);
  const [qrCodeBase64, setQrCodeBase64] = useState('');
  const [showQrModal, setShowQrModal] = useState(false);

  const { webConfig } = useWebConfig();
  const validTime = JSON.parse(webConfig?.paymentQrConfig || '{}');
  const qrValidTime = Number(validTime?.time || 5) * 60 * 1000;
  const qrExpiredRedirectionDelay = 3 * 1000;

  const pollingStarterTimeoutRef = useRef();
  const pollingIntervalRef = useRef();
  const timeoutRef = useRef();
  const hasExpired = useRef(false);

  useEffect(() => {
    const pollingIntervalTime = 5 * 1000;
    const pollingStartDelayTime = 10 * 1000;

    if (qrCodeBase64) {
      pollingStarterTimeoutRef.current = setTimeout(() => {
        pollingIntervalRef.current = setInterval(async () => {
          try {
            const { data: { funds_txns: transactionInfo } } = await getTransactionInfo(transId);
            const { status, type } = transactionInfo;
            if (status === TXN_STATUS.SUCCESS || status === TXN_STATUS.FAILED) {
              setShowQrModal(false);
              clearInterval(pollingIntervalRef.current);
              clearTimeout(timeoutRef.current);
              if (status === TXN_STATUS.SUCCESS) {
                refreshFundsSummary();
              }
              closeModal();
              setTimeout(() => {
                openModal({
                  Component: TransactionStatusModal,
                  componentProps: {
                    transactionInfo,
                    status,
                    type: ORDER_TYPE.ADD,
                    closeModal,
                    paymentMethodName: paymentMethods[selectedMethodIndex]?.paymentMethodName || '',
                    // vpaHandle: ((vpaInput?.vpaValue || '').split('@')[1]) || '',
                  },
                });
              }, 1000);
            }
          } catch (error) {
            console.error('polling txnStatus', error);
          }
        }, pollingIntervalTime);
      }, pollingStartDelayTime);
    }

    return () => {
      clearInterval(pollingIntervalRef.current);
      clearTimeout(pollingStarterTimeoutRef.current);
    };
  }, [qrCodeBase64, paymentTransactionId, addToast, closeModal, qrValidTime, openModal, qrExpiredRedirectionDelay, transId, refreshFundsSummary]);

  const onQrExpiry = useCallback(async () => {
    if (hasExpired.current) return;
    hasExpired.current = true;
    clearInterval(pollingIntervalRef.current);
    clearTimeout(pollingStarterTimeoutRef.current);
    try {
      await closeModal();
    } catch (err) {
      addToast(err?.meta?.displayMessage, APPEARANCE_TYPES.FAIL);
    } finally {
      setTimeout(async () => {
        const { data: { funds_txns: transactionInfo } } = await getTransactionInfo(transId);
        const { status, type } = transactionInfo;
        openModal({
          Component: TransactionStatusModal,
          componentProps: {
            transactionInfo,
            status,
            type: ORDER_TYPE.ADD,
            closeModal,
            paymentMethodName: paymentMethods[selectedMethodIndex]?.paymentMethodName || '',
            // vpaHandle: ((vpaInput?.vpaValue || '').split('@')[1]) || '',
          },
        });
      }, 1000);
    }
  }, [addToast, closeModal, openModal, paymentMethods, selectedMethodIndex, transId, vpaInput?.vpaValue]);

  const onQrCancelButtonYes = async () => {
    try {
      clearInterval(pollingIntervalRef.current);
      clearTimeout(timeoutRef.current);
      clearTimeout(pollingStarterTimeoutRef.current);
      await closeModal();
      // await cancelProcessTxn(paymentTransactionId);
    } catch (err) {
      addToast(err.meta?.displayMessage, APPEARANCE_TYPES.FAIL);
    } finally {
      setTimeout(async () => {
        const { data: { funds_txns: transactionInfo } } = await getTransactionInfo(transId);
        const { status, type } = transactionInfo;

        openModal({
          Component: TransactionStatusModal,
          componentProps: {
            transactionInfo,
            status,
            type: ORDER_TYPE.ADD,
            closeModal,
            paymentMethodName: paymentMethods[selectedMethodIndex]?.paymentMethodName || '',
            // vpaHandle: ((vpaInput?.vpaValue || '').split('@')[1]) || '',
          },
        });
      }, 1000);
    }
  };

  const fetchPayin = useCallback(async () => {
    try {
      const { data } = await checkFirstPayin(clientUniqueTxnId);
      setIsFirstPayin(data?.is_first_payin);
    } catch (err) { }
  }, [clientUniqueTxnId]);

  useEffect(() => {
    fetchPayin();
  }, [fetchPayin]);

  const fetchInfoToolTipData = useCallback(async () => {
    try {
      const res = await fetch('https://static.paytmmoney.com/data/v1/upi-info.json');
      const { data } = await res.json();
      setInfoToolTipData(data);
    } catch (error) { }
  }, []);

  useEffect(() => {
    // Fire payment options page landed event
    sendEventPaymentsRevamp({
      event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
      event_action: PAYMENT_EVENTS.EVENT_ACTION.EQUITY_PAYMENT_OPTIONS_LANDED_WEB,
      screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
      vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
      event: PAYMENT_EVENTS.EVENT_NAME.OPEN_SCREEN,
    }, isFirstPayin);
    fetchInfoToolTipData();
  }, [fetchInfoToolTipData, isFirstPayin]);

  useEffect(() => () => {
    const cancelTransaction = async () => {
      try {
        await cancelProcessTxn(paymentTransactionId);
      } catch (err) { }
    };
    if (!showUpiPayConfirmation.current && paymentTransactionId) {
      cancelTransaction();
    }
  }, [paymentTransactionId]);

  const getIndex = (allPaymentMethods, type) => allPaymentMethods
    .findIndex((element) => element.paymentMethodId === type);

  const getPpbBalanceInfo = useCallback(async (paymentMethodId) => {
    const params = {
      paymentsTxnId: paymentTransactionId,
      paymentMode: paymentMethodId,
    };
    const { data: { availableBalance } } = await makeRequest(getPpbBalance(params));
    setPpbBalance(parseFloat(availableBalance));
  }, [makeRequest, paymentTransactionId]);

  const getPaymentOptionFun = useCallback(async () => {
    try {
      if (paymentTransactionId) {
        const isUpiDisabled = inputAmountValue > UPI_LIMIT;

        const { data: paymentOptionsData = [] } = await getPaymentOptions(paymentTransactionId);
        setIsTpvFlow(paymentOptionsData.isTpvFlow);
        if (!paymentOptionsData.isTpvFlow) {
          const paymentOptionsList = paymentOptionsData.paymentMethods.filter((item) => item.paymentMethodName === 'Net Banking');
          setPaymentMethods(paymentOptionsList);
          return;
        }
        const upiIndex = getIndex(paymentOptionsData?.paymentMethods, UPI_ID);
        // Disabling PPBL check balance
        // const ppbIndex = getIndex(paymentOptionsData?.paymentMethods, PPB_ID);
        // if (ppbIndex !== -1) {
        //   getPpbBalanceInfo(PPBL_ID);
        // }
        if (isUpiDisabled && upiIndex !== -1) {
          const paymentOptions = paymentOptionsData?.paymentMethods;
          paymentOptions[upiIndex].isPaymentMethodDisabled = true;
          paymentOptions[upiIndex].disableMessage = DISABLE_MESSAGE.UPI;
          setPaymentMethods(paymentOptions);
        } else {
          setPaymentMethods(paymentOptionsData?.paymentMethods);
          if (upiIndex === 0 && !paymentOptionsData?.paymentMethods[upiIndex]?.multiBankEnabled
            && paymentOptionsData?.paymentMethods[upiIndex]?.paymentMethodOptionList[0]?.paymentAllowed
          ) {
            const selectedPaymentValue = paymentOptionsData?.paymentMethods[0];
            const paymentMethod = selectedPaymentValue.paymentMethodId;
            const selectedBank = selectedPaymentValue.paymentMethodOptionList[0];
            const bankId = selectedBank?.id;
            const params = {
              id: bankId,
              paymentMethod,
              paymentsTxnId: paymentTransactionId,
            };
            const { data: { paymentGateway: paymentGatewayOption = null } } = await getPaymentGateway(params);
            setPaymentGateway(paymentGatewayOption);
          }
        }
      }
    } catch (error) { }
  }, [inputAmountValue, paymentTransactionId]);

  const fetchVanEligibility = useCallback(async () => {
    try {
      const { meta: statusinfo, data: vanDetails } = await makeVanDetailsReq(getEligibilityApi(userId));

      if ((statusinfo.displayMessage === 'success' || statusinfo.displayMessage === 'Van Already exists')
        && vanDetails !== null) {
        setUserVanDetails(vanDetails);
        setIsEnabled(true);
      } else if (statusinfo.displayMessage === 'success' && vanDetails === null) {
        setIsEnabled(false);
      } else {
        setIsEnabled(false);
      }
    } catch (error) {
    }
  }, [makeVanDetailsReq, userId]);

  const handleEnableTransferClick = useCallback(async () => {
    try {
      const response = await makeCreateVanReq(createVanDetails(userId));
      setUserVanDetails(response.data);
      setIsEnabled(true);
    } catch (err) {
    }
  }, [makeCreateVanReq, userId]);

  useEffect(() => {
    getPaymentOptionFun();
    fetchVanEligibility();
  }, [fetchVanEligibility, getPaymentOptionFun]);

  if (!paymentMethods) { return null; }

  const onOptionClicked = (optionIndex, paymentMethodOptionList) => {
    if (
      (paymentMethodOptionList?.[optionIndex]?.paymentMethodId == 1
        || selectedOptionIndex !== optionIndex)
      && !disableMakePayment
    ) {
      console.log('onOptionClicked', { optionIndex, paymentMethodOptionList });
      setSelectedOptionIndex(optionIndex);
      sendEventPaymentsRevamp({
        event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
        event_action: PAYMENT_EVENTS.EVENT_ACTION.PAYMENT_OPTIONS_CLICKED_WEB,
        screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
        vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
        event_label3: paymentMethodOptionList?.[optionIndex]?.paymentMethodId === 1 ? PAYMENT_MODE.NETBANKING : 'NA',
        event_label4: paymentMethodOptionList?.[optionIndex]?.ifscCode || '',
      }, isFirstPayin);
    }
  };

  const getSelectedOption = () => (
    paymentMethods[selectedMethodIndex].paymentMethodOptionList[selectedOptionIndex]
  );

  const isPaymentMethodSelected = (currentKey) => currentKey === selectedMethodIndex;

  const onPaymentMethodClicked = (key) => {
    if (!isPaymentMethodSelected(key) && !disableMakePayment) {
      setSelectedMethodIndex(key);
      // sendEventPaymentsRevamp({
      //   event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
      //   event_action: PAYMENT_EVENTS.EVENT_ACTION.PAYMENT_OPTIONS_CLICKED_WEB,
      //   screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
      //   vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
      //   event_label3: paymentMethods[key]?.paymentMethodName || '',
      //   event_label4: paymentMethods?.find((item) => item.paymentMethodId === key)?.paymentMethodOptionList[paymentOptionIndex].ifscCode || '',
      // }, isFirstPayin);
    }
  };

  const paymentGatewayDecider = async (methodKey, optionKey) => {
    if (getIndex(paymentMethods, UPI_ID) === methodKey) {
      const selectedPaymentValue = paymentMethods[methodKey];
      const paymentMethod = selectedPaymentValue.paymentMethodId;
      const selectedBank = selectedPaymentValue.paymentMethodOptionList[optionKey];
      const bankId = selectedBank?.id;
      const params = {
        id: bankId,
        paymentMethod,
        paymentsTxnId: paymentTransactionId,
      };
      try {
        const { data: { paymentGateway: paymentGatewayOption = null } } = await getPaymentGateway(params);
        setPaymentGateway(paymentGatewayOption);
      } catch (err) {}
    }
  };

  const handleProceed = (paymentMethod, bankId) => {
    setDisableMakePayment(true);
    sendEvent({
      vertical_name: FUNDS_PAGE_GA.ADD_FUNDS_PAY.VERITCAL_NAME,
      event_category: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_CATEGORY,
      event_action: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_ACTION,
      screenName: FUNDS_PAGE_GA.ADD_FUNDS_PAY.SCREEN_NAME,
      eventName: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_NAME,
      event_label: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_LABEL({
        bankId,
      }),
      event_label2: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_LABEL1({
        transId,
      }),
      event_label3: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_LABEL2({
        paymentMethod,
      }),
      event_label5: FUNDS_PAGE_GA.ADD_FUNDS_PAY.EVENT_LABEL4({
        upiIntent: 'yes',
      }),
      value: FUNDS_PAGE_GA.ADD_FUNDS_PAY.VALUE({
        vpaInput: vpaInput?.value,
      }),
    });
    const params = {
      id: bankId,
      paymentMethod,
      paymentsTxnId: paymentTransactionId,
      ...(paymentMethod?.qrFlowEnabled && { upiFlow: 'UPI_QR', transactionAmount: inputAmountValue }),
      ...(paymentMethod === UPI_ID && vpaInput?.vpaValue && { vpa: vpaInput.vpaValue }),
    };
    const query = search.split('?')[1];
    const queryParams = mapQueryString(query);
    const value = {
      algo: true,
      plan_id: queryParams.plan_id,
      clientUniqueTxnId,
    };
    if (queryParams.type === 'algo') {
      LocalStorage.set(ALGO_TRADING, value);
      LocalStorage.set(ALGO_REDIRECT_TO_FUNDS, true);
    }
    makePayment(params).then(({ data: paymentData, meta: paymentMeta }) => {
      if (paymentData) {
        const { responseType, redirectionUrl } = paymentData;
        if (responseType === PAYMENT_RESPONSE_TYPES.UPI_TPV_FORM) {
          setDisableMakePayment(false);
        } else {
          window.open(redirectionUrl, '_self');
        }
      } else {
        const { displayMessage } = paymentMeta;
        addToast(displayMessage, APPEARANCE_TYPES.FAIL);
        closeModal();
      }
    }).catch((err) => {
      const { meta } = err;
      addToast(meta?.displayMessage, APPEARANCE_TYPES.FAIL);
      closeModal();
    });
  };

  const onUpiPayClick = (methodId, bankId) => {
    openModal({
      Component: UpiPayConfirmationModal,
      type: MODAL_TYPES.POPUP,
      componentProps: {
        closeModal,
        onUpiPayConfirmClickHandler: () => handleProceed(methodId, bankId),
      },
      afterCloseCallback: () => {
        try {
          cancelProcessTxn(paymentTransactionId);
        } catch (err) {}
      },
    });
  };

  const proceedVpa = async (paymentMethod, bankId, multiBankEnabled = false) => {
    const params = {
      vpa: vpaInput.vpaValue,
      paymentsTxnId: paymentTransactionId,
    };
    try {
      if (multiBankEnabled
        || paymentGateway === PAYMENT_GATEWAYS.PAYTM_PG
        || paymentGateway === PAYMENT_GATEWAYS.CASHFREE
      ) {
        const { data: validateData, meta: validateMeta } = await validateVpa(params);
        if (validateData) {
          const { vpa, errorMessage, isValid } = validateData;
          if (isValid) {
            showUpiPayConfirmation.current = true;
            onUpiPayClick(paymentMethod, bankId);
          } else {
            setVpaInput({
              vpaErrorMessage: errorMessage,
              isVpaValid: false,
              vpaValue: removeWhiteSpace(vpa),
            });
            setDisableVpaInput(false);
            setDisableBtn(false);
          }
        } else {
          const { displayMessage } = validateMeta;
          addToast(displayMessage, APPEARANCE_TYPES.FAIL);
        }
      } else {
        showUpiPayConfirmation.current = true;
        onUpiPayClick(paymentMethod, bankId);
      }
    } catch (err) { addToast(err.meta.displayMessage, APPEARANCE_TYPES.FAIL); }
  };

  const multiBankQrPay = () => {
    const upiIndex = getIndex(paymentMethods, UPI_ID);
    const selectedBank = paymentMethods[upiIndex].paymentMethodOptionList[0];
    // Custom params for multiBankQrPay
    const params = {
      paymentsTxnId: paymentTransactionId,
      paymentMethod: selectedBank?.paymentMethodId,
      transactionAmount: inputAmountValue,
      id: selectedBank?.id,
      upiFlow: 'UPI_QR',
    };
    setDisableMakePayment(true);
    makePayment(params).then(({ data: paymentData, meta: paymentMeta }) => {
      if (paymentData?.qrImage) {
        setQrCodeBase64(paymentData?.qrImage);
      } else {
        const { displayMessage } = paymentMeta;
        addToast(displayMessage, APPEARANCE_TYPES.FAIL);
        closeModal();
      }
    }).catch((err) => {
      const { meta } = err;
      addToast(meta?.displayMessage, APPEARANCE_TYPES.FAIL);
      closeModal();
    }).finally(() => {
      setDisableMakePayment(false);
    });
  };

  const multiBankUpiPay = () => {
    const upiIndex = getIndex(paymentMethods, UPI_ID);
    const selectedBank = paymentMethods[upiIndex].paymentMethodOptionList[0];
    proceedVpa(selectedBank?.paymentMethodId, selectedBank?.id, true);
  };

  const proceedToPay = () => {
    const selectedPaymentValue = paymentMethods[selectedMethodIndex];
    const paymentMethod = selectedPaymentValue.paymentMethodId;
    const selectedBank = getSelectedOption();
    const bankId = selectedBank?.id;

    if (paymentMethod === UPI_ID) {
      setDisableVpaInput(true);
      proceedVpa(selectedBank?.paymentMethodId, bankId);
    } else if (paymentMethod === PPB_ID) {
      handleProceed(selectedBank?.paymentMethodId, bankId);
    } else {
      handleProceed(paymentMethod, bankId);
    }
    sendEvent({
      event_category: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_CATEGORY('add'),
      event_action: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_ACTION,
      event_label: FUNDS_PAGE_GA.PROCEED_CLICKED.EVENT_LABEL(
        pageSource, selectedPaymentValue.paymentMethodName.toLowerCase(),
      ),
    });
  };

  const onEnterHandler = (key) => {
    if (key && isEqual(key.keyCode, 13)) {
      proceedToPay();
    }
  };
  const handleAddBankAccount = () => {
    sendEvent({
      event_category: FUNDS_PAGE_GA.ADD_BANK_BUTTON.EVENT_CATEGORY,
      eventName: FUNDS_PAGE_GA.ADD_BANK_BUTTON.EVENT_NAME,
      screenName: FUNDS_PAGE_GA.ADD_BANK_BUTTON.SCREEN_NAME,
      vertical_name: FUNDS_PAGE_GA.ADD_BANK_BUTTON.VERTICAL_NAME,
    });
    sendPageEvent({
      eventDetails: {
        screenName: 'bankaccount_fundssection',
        eventType: 'openScreen',
        event_action: 'open_screen',
      },
    });
    history.push(`${Routes[ROUTE_NAME.BANK_ACCOUNT].url}?${ADD_BANK_REDIRECT_PARAM}=true`);
  };
  const renderVpaInput = (defaultVpa = '', multiBankEnabled = false) => {
    if (!multiBankEnabled && paymentGateway === PAYMENT_GATEWAYS.BILLDESK_PG) return null;
    return (
      <VpaInput
        onChangeHandler={(vpa) => {
          setVpaInput({
            vpaErrorMessage: '',
            vpaValue: vpa,
            isVpaValid: true,
          });
        }}
        vpaValue={vpaInput.vpaValue}
        infoToolTipData={infoToolTipData}
        showVpaError={!vpaInput.isVpaValid}
        vpaErrorMsg={vpaInput.vpaErrorMessage}
        onEnterHandler={onEnterHandler}
        disableVpaInput={disableVpaInput}
        setVpaInput={setVpaInput}
        defaultVpa={defaultVpa}
        multiBankEnabled={multiBankEnabled}
      />
    );
  };

  const defaultSelectedPayment = paymentMethods.find((method) => method.paymentMethodOptionList)
    ?.paymentMethodOptionList[0];

  const handleAccordionClick = (type) => {
    if (type === PAYMENT_TYPE.UPI) setIsUpiOpen((prev) => !prev);
    if (type === PAYMENT_TYPE.NET_BANKING) setIsNetBankingOpen((prev) => !prev);
    if (type === PAYMENT_TYPE.BANK_TRANSFER) setIsBankTransferOpen((prev) => !prev);
  };

  const paymentOptionsList = (
    <div>
      {
        paymentMethods.map((paymentMethod, key) => {
          const isUpi = paymentMethod.paymentMethodName === 'UPI';
          const isNetBanking = paymentMethod.paymentMethodName === 'Net Banking';
          const isBankTransfer = paymentMethod.paymentMethodName === 'Bank Transfer';
          let isOpen = true;
          if (isUpi) isOpen = isUpiOpen;
          if (isNetBanking) isOpen = isNetBankingOpen;
          if (isBankTransfer) isOpen = isBankTransferOpen;
          return (
            <React.Fragment key={key}>
              {key !== 0 && (
                <div style={{ borderTop: '1px solid #ececec', width: '100%' }} />
              )}
              <div className={styles.paymentTypeContainer}>
                <div
                  className={`${styles.paymentOptionsHeader} ${styles.paymentHeader}`}
                  style={{ cursor: (isUpi || isNetBanking || isBankTransfer) ? 'pointer' : 'default', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                  onClick={() => {
                    if (isUpi) handleAccordionClick(PAYMENT_TYPE.UPI);
                    if (isNetBanking) handleAccordionClick(PAYMENT_TYPE.NET_BANKING);
                    if (isBankTransfer) handleAccordionClick(PAYMENT_TYPE.BANK_TRANSFER);
                  }}
                  role="button"
                  tabIndex={0}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      if (isUpi) handleAccordionClick(PAYMENT_TYPE.UPI);
                      if (isNetBanking) handleAccordionClick(PAYMENT_TYPE.NET_BANKING);
                      if (isBankTransfer) handleAccordionClick(PAYMENT_TYPE.BANK_TRANSFER);
                    }
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {paymentMethod.paymentMethodName}
                    {paymentMethod.textLogoUrl && (
                      <div style={{ marginLeft: 8 }}>
                        <img alt="payment" src={paymentMethod.textLogoUrl} className={styles.textLogo} />
                      </div>
                    )}
                  </div>
                  {(isUpi || isNetBanking || isBankTransfer) && (
                    <span>
                      {isOpen ? (
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                          <path d="M6 15L12 9L18 15" stroke="#222222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                          <path d="M6 9L12 15L18 9" stroke="#222222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </span>
                  )}
                </div>
                {/* Accordion content for each payment method */}
                {isOpen && (
                  <PaymentMethod
                    key={key}
                    methodIndex={key}
                    optionData={paymentMethod}
                    selectedOptionIndex={selectedOptionIndex}
                    onOptionClicked={onOptionClicked}
                    onPaymentMethodClicked={onPaymentMethodClicked}
                    selected={isPaymentMethodSelected(key)}
                    ppbBalance={ppbBalance}
                    getPpbBalanceInfo={getPpbBalanceInfo}
                    inProgress={inProgress}
                    failed={failed}
                    inputAmountValue={inputAmountValue}
                    onBtnClick={proceedToPay}
                    disableBtn={disableBtn}
                    setDisableBtn={setDisableBtn}
                    renderVpaInput={renderVpaInput}
                    vpa={vpaInput?.vpaValue}
                    setShowMore={setShowMore}
                    isEnabled={isEnabled}
                    handleEnableTransferClick={handleEnableTransferClick}
                    userVanDetails={userVanDetails}
                    vanInProgress={vanInprogress || vanDetailsInprogress}
                    paymentGatewayDecider={paymentGatewayDecider}
                    paymentGateway={paymentGateway}
                    isTpvFlow={isTpvFlow}
                    transId={transId}
                    multiBankUpiPay={multiBankUpiPay}
                    multiBankQrPay={multiBankQrPay}
                    onQrCancelButtonYes={onQrCancelButtonYes}
                    onQrExpiry={onQrExpiry}
                    qrCodeBase64={qrCodeBase64}
                    qrValidTime={qrValidTime}
                    showQrModal={showQrModal}
                    setIsUpiSelected={setIsUpiSelected}
                    isUpiSelected={isUpiSelected}
                    setShowQrModal={setShowQrModal}
                    defaultSelectedPayment={defaultSelectedPayment}
                    setVpaInput={setVpaInput}
                    isFirstPayin={isFirstPayin}
                  />
                )}
              </div>
            </React.Fragment>
          );
        })
      }
      <div className={styles.addButtonClass}>
        <button
          className={`${button.btn} ${styles.addAccountBtn}`}
          onClick={handleAddBankAccount}
        >
          <span className={styles.spanClass}>+</span>
          Add Bank Account
        </button>
      </div>
    </div>
  );

  const emptyPaymentOptions = (
    <div>
      <EmptyState
        text="No Payment Options available"
        subText="Please try again after sometime"
        iconName={ICON_NAME.NO_PAYMENT_OPTIONS}
        className={styles.emptyState}
      />
    </div>
  );
  if (showMore) {
    return (
      <KnowMore
        handleEnableTransferClick={handleEnableTransferClick}
        isEnabled={isEnabled}
        setShowMore={setShowMore}
      />
    );
  }

  return (
    <ManageFundsModalLayout
      title="Add Funds"
      onFooterButtonClick={proceedToPay}
      childClassName={styles.paymentsContainer}
    >
      <div className={styles.amountContainer}>
        <div className={styles.amountSection}>
          <div>
            ₹
            {formatPrice(inputAmountValue)}
          </div>
          <div>Top up Amount</div>
        </div>
      </div>
      {paymentMethods.length ? paymentOptionsList : emptyPaymentOptions}
    </ManageFundsModalLayout>
  );
};

export default PaymentOptionsList;


