import React, { useState, useContext, useEffect } from 'react';
import { ICON_NAME } from '@common/Icon/enums';
import { Change } from '@common/Prices';
import Icon from '@common/Icon';
import isEmpty from 'lodash/isEmpty';
import { UserContext } from '@layout/App/UserContext';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import CONSTANTS from '@common/UseApi/enums';
import { formatPrice } from '@utils';
// import PaytmPaymentBank from './PaytmPaymentBank';
import ManageFundsModalLayout from '../ManageFundsModalLayout';
import styles from './index.scss';
import InfoTooltip from './InfoTooltip';
import PaymentOptionsList from './PaymentOptionsList';
import { verifyCoupon, initiatePayment } from '../../../api';
import { APPLIED_COUPON_STATUS } from '../config';

const AddMoneyModal = ({
  inputAmountValue,
  paymentTransactionId,
  closeModal,
  openModal,
  pageSource,
  subscriptionData,
  handleSubscriptionUpdate,
  clientUniqueTxnId,
  paymentId,
  retryPayment,
  fetchTransactionHistory,
}) => {
  const [couponText, setCouponText] = useState('');
  const [payId, setPayId] = useState('');
  const [couponStatus, setCouponStatus] = useState(null);
  const [couponCodePaymentId, setCouponCodePaymentId] = useState(null);
  const [couponCodeAmount, setCouponCodeAmount] = useState(null);
  const [showSubscriptionScreen, setShowSubscriptionScreen] = useState(false);
  const [coupnScreenTransactionId, setCoupnScreenTransactionId] = useState('');
  const { userId } = useContext(UserContext);
  const { addToast } = useToast();

  useEffect(() => {
    if (subscriptionData) {
      setShowSubscriptionScreen(!(subscriptionData.consolidatedCharges.totalAmount === 0));
    }
  }, [subscriptionData]);
  const updateModalContent = async () => {
    let params = {};
    if (couponCodePaymentId && couponCodeAmount) {
      params = {
        amount: inputAmountValue,
        coupon_txn_id: couponCodePaymentId,
        coupon_amount: couponCodeAmount,
      };
    } else {
      params = {
        amount: inputAmountValue,
      };
    }
    try {
      const { data: { payment_txn_id: paymentTxnId, id: paymentUniqueId } } = await initiatePayment(params);
      setShowSubscriptionScreen(false);
      setCoupnScreenTransactionId(paymentTxnId);
      setPayId(paymentUniqueId);
      if (couponCodePaymentId && couponCodeAmount) {
        const transactionsInfo = JSON.parse(localStorage.getItem('transactionMetaInfo'));
        const messgae = `Your cash back of ₹${Math.round(couponCodeAmount)} will be credited to your ledger account in 72 hours.`;
        if (transactionsInfo && Object.keys(transactionsInfo).length > 0) {
          transactionsInfo[paymentId] = {
            text: messgae,
          };
          localStorage.setItem('transactionMetaInfo', JSON.stringify(transactionsInfo));
        } else {
          const map = {};
          map[paymentId] = { text: messgae };
          localStorage.setItem('transactionMetaInfo', JSON.stringify(map));
        }
      }
    } catch (error) {
      const message = error?.meta?.displayMessage || CONSTANTS.ERROR_MESSAGE;
      addToast(message, APPEARANCE_TYPES.FAIL);
    }
  };

  const handleApplyCoupon = (e) => {
    if (e && couponText.trim().length > 1) {
      setCouponCodePaymentId(null);
      const body = {
        code: couponText,
        source: 'FIRST_PAYIN',
        metaData: {
          txnAmount: inputAmountValue,
        },
      };
      try {
        verifyCoupon(userId, body).then(({ data }) => {
          if (data) {
            setCouponStatus(APPLIED_COUPON_STATUS.SUCCESS);
            setCouponCodePaymentId(data.transactionId);
            setCouponCodeAmount(data.gratification.reward);
          } else {
            setCouponStatus(APPLIED_COUPON_STATUS.FAILURE);
          }
        })
          .catch(() => {
            setCouponStatus(APPLIED_COUPON_STATUS.FAILURE);
          });
      } catch (err) { }
    }
  };

  const couponCodeStatusText = () => {
    if (couponStatus === APPLIED_COUPON_STATUS.SUCCESS) {
      return { text: 'Promocode successfully applied', color: styles.success };
    } if (couponStatus === APPLIED_COUPON_STATUS.FAILURE) {
      return { text: 'Invalid promo code.', color: styles.failure };
    }
    return { text: '', color: '' };
  };

  const handleCouponChange = (e) => {
    setCouponText(e.target.value);
    setCouponStatus(null);
    if (e.target.value.trim().length === 0) {
      setCouponStatus(null);
    }
  };

  const finalAmount = () => {
    const finalValue = inputAmountValue - subscriptionData?.consolidatedCharges?.amountToBeCharged;
    if (couponCodePaymentId) {
      return finalValue + parseInt(couponCodeAmount, 10);
    }
    return finalValue;
  };

  const removeInvalidCoupon = (event) => {
    if (event) {
      setCouponStatus(null);
      setCouponText('');
    }
  };

  const subscriptionTable = () => (
    !isEmpty(subscriptionData)
    && (
      <>
        <div className={styles.couponCodeSection}>
          <div className={styles.couponInputBox}>
            <input type="text" placeholder="Enter coupon code" value={couponText} onChange={handleCouponChange} />
            {couponStatus === APPLIED_COUPON_STATUS.SUCCESS && (
              <span>
                <Icon
                  name={ICON_NAME.SUCCESS_ICON}
                  size={3}
                />
              </span>
            )}
            {couponStatus === APPLIED_COUPON_STATUS.FAILURE && (
              <span>
                <Icon
                  name={ICON_NAME.FAILED}
                  size={3}
                  onIconClick={(e) => removeInvalidCoupon(e)}
                  className={styles.removeTextCursor}
                />
              </span>
            )}
            {couponStatus === null && <span role="presentation" onClick={handleApplyCoupon} className={couponText.trim().length > 0 ? styles.applyEnabled : styles.applyDisabled}>Apply</span>}
          </div>
          <div className={couponCodeStatusText().color}>{couponCodeStatusText().text}</div>
          {couponStatus === APPLIED_COUPON_STATUS.SUCCESS && (
            <div className={styles.couponInfoMessage}>
              <span>
                <Icon
                  name={ICON_NAME.INFO_DARK}
                  size={3}
                />
              </span>
              You will get
              <span className={styles.boldText}>{`₹${formatPrice(couponCodeAmount)}`}</span>
              {' '}
              as cashback in your ledger account.
            </div>
          )}
        </div>
        <div>
          <div className={styles.additionalChargesSec}>
            <div className={styles.subscriptionHeader}>
              Additional Charges
            </div>
            <div>
              {subscriptionData?.subscriptions.map((subscription, key) => (
                (subscription.totalAmount > 0
                  && (
                    <div key={key}>
                      {key !== 0
                        ? <hr key={key + 1} className={styles.horizontalLine} /> : null}
                      <div className={`${styles.dataRow} ${styles.tableRow}`}>
                        <div className={styles.headingRow}>
                          <div>
                            {' '}
                            {subscription.displayName}
                          </div>
                          <div>
                            <InfoTooltip
                              infoHeading={subscription.displayName}
                              infoText={subscription.description}
                              customBubbleClass={styles.messageWidth}
                            >
                              <Icon
                                name={ICON_NAME.INFO_BLUE}
                                size={2}
                                className={styles.infoIcon}
                              />
                            </InfoTooltip>
                          </div>
                        </div>
                        <div>
                          {subscription.totalAmount > 0 && '- '}
                          {`₹ ${formatPrice(subscription.totalAmount)}`}
                        </div>
                      </div>
                    </div>
                  )
                )
              ))}
              {couponCodePaymentId && (
                <>
                  <hr className={styles.horizontalLine} />
                  <div
                    role="presentation"
                    className={styles.discardCoupon}
                    onClick={() => {
                      setCouponStatus(null);
                      setCouponText('');
                      setCouponCodeAmount(null);
                      setCouponCodePaymentId(null);
                    }}
                  >
                    <Icon
                      name={ICON_NAME.CLOSE_ROUNDED}
                      size={2}
                    />
                  </div>
                  <div className={`${styles.dataRow} ${styles.tableRow}`}>
                    <div className={styles.cashBackText}>
                      <div>Cash back</div>
                      <div>
                        Promocode applied
                        <span>
                          {' '}
                          <Icon
                            name={ICON_NAME.SUCCESS_ICON}
                            size={2}
                          />
                        </span>
                      </div>
                    </div>
                    <div className={styles.success}>{`₹ ${formatPrice(couponCodeAmount)}`}</div>
                  </div>

                </>
              )}
            </div>
            <div className={`${styles.subscriptionBottom}`}>
              <div>Total amount added to investing account.</div>
              <div className={styles.finalAmount}><Change withSign withRupee value={finalAmount()} /></div>
            </div>
          </div>
        </div>
      </>
    )
  );

  return (
    showSubscriptionScreen
      ? (
        <ManageFundsModalLayout
          title="Top-up Investment Account"
          onFooterButtonClick={updateModalContent}
          btnText={`Proceed to pay ₹${formatPrice(inputAmountValue)}`}
          handleSubscriptionUpdate={handleSubscriptionUpdate}
          isTpv={false}
        >
          <div>
            <div className={styles.amountSection}>
              <div>
                ₹
                {formatPrice(inputAmountValue)}
              </div>
              <div>Top up Amount</div>
            </div>
          </div>
          {subscriptionTable()}

        </ManageFundsModalLayout>
      )
      : (
        <PaymentOptionsList
          inputAmountValue={inputAmountValue}
          paymentTransactionId={coupnScreenTransactionId.length > 0 ? coupnScreenTransactionId : paymentTransactionId}
          closeModal={closeModal}
          openModal={openModal}
          pageSource={pageSource}
          clientUniqueTxnId={clientUniqueTxnId}
          transId={paymentId || payId}
          retryPayment={retryPayment}
          fetchTransactionHistory={fetchTransactionHistory}
        />
      )

  );
};

export default AddMoneyModal;

