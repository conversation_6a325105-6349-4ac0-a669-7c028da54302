import React, { useState, useEffect } from "react";
import { classNames as cx } from "@utils/style";
import btnStyles from "@commonStyles/button.scss";
import { PPBL_ID, PYTM_IFSC } from "@pages/ManageFunds/config";
import {
  hasWhiteSpace,
  removeWhiteSpaceFromArray,
} from "@pages/ManageFunds/utils";
import RadioButton from "@common/RadioButton";
import { sendEventPaymentsRevamp } from "@service/AnalyticsService";
import { PAYMENT_EVENTS, PAYMENT_MODE, SESSION_STORAGE } from "@pages/ManageFunds/enums";
import PaymentListView from "../PaymentListView";
import styles from "./index.scss";
import Balance from "../Balance";
import { VpaSuggestions } from "../VpaSuggestions";
import QRCode from "../QR";
import { UPI_OPTION } from "../../../enums";

const MultiBank = ({
  paymentMethodOptionList,
  renderVpaInput,
  inputAmountValue,
  multiBankUpiPay,
  vpa,
  balance,
  failed,
  getPpbBalanceInfo,
  inProgress,
  isDisableBtnForPpbl,
  optionData,
  setVpaInput,
  vpaList,
  multiBankQrPay,
  onQrCancelButtonYes,
  onQrExpiry,
  qrCodeBase64,
  qrValidTime,
  showQrModal,
  setShowQrModal,
  multiBankEnabled,
  isFirstPayin,
  onUpiRadioClick,
  isUpiSelected,
  setIsUpiSelected,
}) => {
  const [selectedUpiOption, setSelectedUpiOption] = useState(UPI_OPTION.QR); // 'qr' or 'vpa'
  const [showBanks, setShowBanks] = useState(false);
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const qrValidTimeSeconds = qrValidTime > 1000 ? Math.floor(qrValidTime / 1000) : qrValidTime;
  const [qrTimer, setQrTimer] = useState(qrValidTimeSeconds);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  useEffect(() => {
    if (selectedUpiOption === 'qr') {
      sessionStorage.setItem(SESSION_STORAGE.PAYMENT_METHOD, SESSION_STORAGE.PAYMENT_METHOD_TYPE.UPI_QR);
    } else if (selectedUpiOption === 'vpa') {
      sessionStorage.setItem(SESSION_STORAGE.PAYMENT_METHOD, SESSION_STORAGE.PAYMENT_METHOD_TYPE.UPI_COLLECT);
    }
  }, [selectedUpiOption]);

  useEffect(() => {
    if (showQrModal) {
      setQrTimer(qrValidTimeSeconds);
    }
  }, [showQrModal, qrValidTimeSeconds]);

  useEffect(() => {
    if (showQrModal && qrTimer > 0) {
      const timerId = setInterval(() => {
        setQrTimer((t) => {
          const newTime = t - 1;
          if (newTime === 0) {
            // QR code expired - fire analytics event
            sendEventPaymentsRevamp({
              event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
              event_action: PAYMENT_EVENTS.EVENT_ACTION.QR_EXPIRED_WEB,
              screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
              vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
            }, isFirstPayin);
          }
          return newTime;
        });
      }, 1000);
      return () => clearInterval(timerId);
    }
    return undefined;
  }, [showQrModal, qrTimer]);

  function onUpiRadioClk(value = true) {
    setIsUpiSelected(!!value);
  }

  return (
    <div className={styles.multiBank}>
      <div className={styles.upiRadioContainer}>
        {optionData?.qrFlowEnabled && (
          <QRCode
            selectedUpiOption={selectedUpiOption}
            setSelectedUpiOption={setSelectedUpiOption}
            showQrModal={showQrModal}
            setShowQrModal={setShowQrModal}
            qrCodeBase64={qrCodeBase64}
            qrValidTime={qrValidTime}
            paymentMethodOptionList={paymentMethodOptionList}
            multiBankQrPay={multiBankQrPay}
            onQrCancelButtonYes={onQrCancelButtonYes}
            onQrExpiry={onQrExpiry}
            isFirstPayin={isFirstPayin}
            onUpiRadioClick={onUpiRadioClk}
            isUpiSelected={isUpiSelected}
          />
        )}
        <div className={styles.upiRadioBox}>
          <div
            style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
            onClick={() => {
              setSelectedUpiOption(UPI_OPTION.VPA);
            }}
            role="button"
            tabIndex={0}
            onKeyPress={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setSelectedUpiOption(UPI_OPTION.VPA);
              }
            }}
          >
            <RadioButton
              selected={selectedUpiOption === UPI_OPTION.VPA && isUpiSelected}
              onRadioClick={() => {
                onUpiRadioClk();
                sessionStorage.setItem(SESSION_STORAGE.PAYMENT_METHOD, SESSION_STORAGE.PAYMENT_METHOD_TYPE.UPI_COLLECT);
                if (selectedUpiOption !== UPI_OPTION.VPA || !isUpiSelected) {
                  console.log('paymentMethodOptionList ::', paymentMethodOptionList);
                  sendEventPaymentsRevamp({
                    event_category: PAYMENT_EVENTS.EVENT_CATEGORY.ADDMONEY_EQUITY,
                    event_action: PAYMENT_EVENTS.EVENT_ACTION.PAYMENT_OPTIONS_CLICKED_WEB,
                    screenName: PAYMENT_EVENTS.SCREEN_NAME.equity_payment_options,
                    vertical_name: PAYMENT_EVENTS.VERTICAL_NAME,
                    event_label3: PAYMENT_MODE.UPI_COLLECT,
                    event_label4: `${vpa.split('@')[1]}`,
                  }, isFirstPayin);
                }
              }}
            />
            <span style={{ marginLeft: 8 }}>Pay with UPI ID</span>
          </div>
          <div>
            <div className={styles.subheading}>
              Enter your UPI id below and complete transaction by accepting
              request on UPI app
            </div>
          </div>
          {selectedUpiOption === UPI_OPTION.VPA && (
            <div>
              {renderVpaInput(vpa, true)}
              <button
                id="fundsModalFooterButton"
                className={cx([btnStyles.btn, styles.proceedButton], {
                  [btnStyles.greenBtnFill]: vpa.length,
                  [btnStyles.disabledBtn]:
                    isDisableBtnForPpbl || !vpa.length || hasWhiteSpace(vpa),
                  [btnStyles.disabledBtnFill]:
                    isDisableBtnForPpbl || !vpa.length || hasWhiteSpace(vpa),
                })}
                onClick={multiBankUpiPay}
                disabled={
                  isDisableBtnForPpbl || !vpa.length || hasWhiteSpace(vpa)
                }
              >
                {`PAY ₹${inputAmountValue}`}
              </button>
              {optionData ? (
                <VpaSuggestions
                  vpaList={vpaList}
                  setActiveVpa={setVpaInput}
                  activeVpa={vpa}
                />
              ) : null}
            </div>
          )}
        </div>
      </div>
      <div className={styles.transferOnlyTitle}>
        <span className={styles.transferOnlyText}>Transfer money only from these account</span>
      </div>
      {paymentMethodOptionList.map((item, index) => (
        <div
          key={index}
          className={cx(styles.bankDetail, {
            [styles.notAllowed]: !item.paymentAllowed,
          })}
        >
          <img className={styles.bankImg} src={item.bankImgSrc} alt="bank" />
          <div className={styles.info}>
            <div className={styles.bankName}>{item.displayName}</div>
            <div className={styles.bankAccNo}>
              A/c No.
              {item.bankAccountNumber
                ? `${'*'.repeat(item.bankAccountNumber.length - 4)}${item.bankAccountNumber.slice(-4)}`
                : ''}
            </div>
            {balance !== null && item.ifscCode === PYTM_IFSC ? (
              <Balance
                balance={balance}
                failed={failed}
                getPpbBalanceInfo={getPpbBalanceInfo.bind(null, PPBL_ID)}
                inProgress={inProgress}
                amount={inputAmountValue}
                paymentAllowed={item.paymentAllowed}
              />
            ) : null}
          </div>
        </div>
      ))}
    </div>
  );
};

function UPI({
  onOptionSelect,
  isPaymentMethodDisabled,
  ppbBalance,
  failed,
  getPpbBalanceInfo,
  inProgress,
  inputAmountValue,
  paymentMethodOptionList,
  disableMessage,
  selected,
  renderVpaInput,
  handleClick,
  getDefaultVpa,
  paymentMethodId,
  getProceedButtonClass,
  isProccedDisabled,
  disableBtn,
  selectedOptionIndex,
  optionData,
  multiBankUpiPay,
  transId,
  vpa,
  handlePpblErr,
  paymentGateway,
  setVpaInput,
  multiBankQrPay,
  onQrCancelButtonYes,
  onQrExpiry,
  qrCodeBase64,
  qrValidTime,
  showQrModal,
  setShowQrModal,
  isFirstPayin,
  isUpiSelected,
  setIsUpiSelected,
}) {
  const [vpaList, setVpaList] = useState([]);

  useEffect(() => {
    let list = [];
    let vpaValue = "";
    if (optionData.multiBankEnabled) {
      const arr = removeWhiteSpaceFromArray(optionData?.recentlyUsedVpa) || [];
      const tempArr = new Set(arr);
      list = Array.from(tempArr) || [];
      vpaValue = list[0] || "";
    } else {
      list = paymentMethodOptionList[0]?.recentlyUsedVpa || [];
      vpaValue = list[0] || "";
    }
    setVpaList(list || []);
    setVpaInput({
      vpaValue: vpaValue || "",
      vpaErrorMessage: "",
      isVpaValid: true,
    });
  }, [optionData, paymentMethodOptionList, setVpaInput]);

  if (optionData?.multiBankEnabled) {
    const isDisableBtnForPpbl =
      ppbBalance &&
      paymentMethodOptionList.some(
        (bank) =>
          bank.ifscCode === PYTM_IFSC &&
          paymentMethodOptionList.length === 1 &&
          ppbBalance < inputAmountValue &&
          bank.paymentAllowed
      );
    return (
      <MultiBank
        paymentMethodOptionList={paymentMethodOptionList}
        renderVpaInput={renderVpaInput}
        inputAmountValue={inputAmountValue}
        multiBankUpiPay={multiBankUpiPay}
        vpa={vpa}
        balance={ppbBalance}
        failed={failed}
        getPpbBalanceInfo={getPpbBalanceInfo}
        inProgress={inProgress}
        isDisableBtnForPpbl={isDisableBtnForPpbl}
        optionData={optionData}
        setVpaInput={setVpaInput}
        vpaList={vpaList}
        multiBankQrPay={multiBankQrPay}
        onQrCancelButtonYes={onQrCancelButtonYes}
        onQrExpiry={onQrExpiry}
        qrCodeBase64={qrCodeBase64}
        qrValidTime={qrValidTime}
        showQrModal={showQrModal}
        setShowQrModal={setShowQrModal}
        multiBankEnabled={!!optionData?.multiBankEnabled}
        isFirstPayin={isFirstPayin}
        isUpiSelected={isUpiSelected}
        setIsUpiSelected={setIsUpiSelected}
      />
    );
  }

  let isPaymentAlowedSelected = false;
  return paymentMethodOptionList?.map((option, index) => {
    const {
      id,
      displayName,
      bankAccountNumber,
      bankImgSrc,
      paymentAllowed,
      ifscCode,
      paymentAllowedMessage,
      conveyedDisplayMessage,
    } = option;
    const isBtnSelected = selectedOptionIndex === index && selected;
    if (isBtnSelected) {
      isPaymentAlowedSelected = option.paymentAllowed;
    }
    return (
      <PaymentListView
        key={index}
        id={id}
        paymentAllowedMessage={paymentAllowedMessage}
        conveyedDisplayMessage={conveyedDisplayMessage}
        displayName={displayName}
        bankAccountNumber={bankAccountNumber}
        bankImgSrc={bankImgSrc}
        paymentAllowed={paymentAllowed}
        isPaymentAlowedSelected={isPaymentAlowedSelected}
        paymentMethodId={paymentMethodId}
        onOptionSelect={onOptionSelect}
        indexKey={index}
        isPaymentMethodDisabled={isPaymentMethodDisabled}
        isBtnSelected={isBtnSelected}
        ppbBalance={ppbBalance}
        failed={failed}
        getPpbBalanceInfo={getPpbBalanceInfo}
        inProgress={inProgress}
        inputAmountValue={inputAmountValue}
        paymentMethodOptionList={paymentMethodOptionList}
        disableMessage={disableMessage}
        selected={selected}
        renderVpaInput={renderVpaInput}
        handleClick={handleClick}
        getDefaultVpa={getDefaultVpa}
        getProceedButtonClass={getProceedButtonClass}
        isProccedDisabled={isProccedDisabled}
        disableBtn={disableBtn}
        transId={transId}
        ifscCode={ifscCode}
        handlePpblErr={handlePpblErr}
        vpaList={vpaList}
        setVpaList={setVpaList}
        paymentGateway={paymentGateway}
        vpa={vpa}
        setVpaInput={setVpaInput}
        optionData={optionData}
        showQrModal={showQrModal}
        setShowQrModal={setShowQrModal}
        qrCodeBase64={qrCodeBase64}
        qrValidTime={qrValidTime}
        multiBankQrPay={multiBankQrPay}
        onQrCancelButtonYes={onQrCancelButtonYes}
      />
    );
  });
}
export default UPI;

