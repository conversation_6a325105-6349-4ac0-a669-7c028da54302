export const PAYMENT_EVENTS = {
  SCREEN_NAME: {
    add_funds: '/addfunds',
    equity_payment_options: '/equity_payment_options',
    equity_payment_confirmation: '/equity_payment_confirmation',

  },
  VERTICAL_NAME: 'payments',
  EVENT_NAME: {
    CUSTOM_EVENT: 'custom_event',
    OPEN_SCREEN: 'openScreen',
  },
  EVENT_CATEGORY: {
    ADDMONEY_EQUITY: 'addmoney_equity_web',
  },
  EVENT_ACTION: {
    ADDFUNDS_CLICKED_WEB: 'addfunds_clicked_web',
    EQUITY_PAYMENT_OPTIONS_LANDED_WEB: 'equity_payment_options_landed_web',
    PAYMENT_OPTIONS_CLICKED_WEB: 'payment_options_clicked_web',
    GENERATE_QR_WEB: 'generate_qr_web',
    CROSS_BUTTON_QR_WEB: 'cross_button_qr_web',
    QR_EXPIRED_WEB: 'qr_expired_web',
    VIEW_ALL_QR_IMAGE_WEB: 'view_all_qr_image_web',
    PAYMENT_CONFIRMATION: 'payment_confirmation',
    CTA_CLICKED: 'cta_clicked',
  },
  EVENT_LABEL: {
    FIRST_FUND: 'first fund',
    RECURRING: 'recurring',
  },
};

export const LABEL = {
  FIRST_FUND: 'first fund',
  RECURRING: 'recurring',
};

export const PAYMENT_MODE = {
  UPI_COLLECT: 'UPI Collect',
  UPI_QR: 'UPI QR',
  NETBANKING: 'Netbanking',
  BANK_TRANSFER: 'Bank Transfer',
};

export const SESSION_STORAGE = {
  PAYMENT_METHOD: 'PAYMENT_METHOD',
  PAYMENT_METHOD_TYPE: {
    UPI_QR: 'UPI QR',
    UPI_COLLECT: 'UPI Collect',
    NET_BANKING: 'Netbanking',
  },
};
