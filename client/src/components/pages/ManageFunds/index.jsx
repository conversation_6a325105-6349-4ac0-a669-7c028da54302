import React, {
  useState,
  useCallback,
  useEffect,
  useContext,
  useRef,
  Suspense,
  lazy,
} from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import isEmpty from 'lodash/isEmpty';
import { useGetApi } from '@common/UseApi';
import { ROUTE_NAME } from '@utils/enum';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import CONSTANTS from '@common/UseApi/enums';
import ErrorView from '@common/WithLoader/ErrorView';
import { useModal } from '@layout/App/ModalContext';
import { UserContext, useIrData } from '@layout/App/UserContext';
import { FundsSummaryContext } from '@layout/App/FundsSummaryContext';
import useNonIrModal from '@common/useNonIrModal';
import { useContextBackgroundUpdate } from '@layout/App/contextBackgroundUpdate';
import moment from 'moment';
import LocalStorage from '@service/LocalStorage';
import { SUBSCRIPTION_TYPE } from '@modules/MiniAppUpgrade/enums';
import { getTransactionDetails } from '@modules/MiniAppUpgrade/api';
import { PmlLogo } from '@common/PmlLoader';
import { sendPageEvent } from '@service/AnalyticsService';
import { mobileBrowser, mapQueryString, getUrlParameter } from '@utils';
import Routes from '../../../routes';
import { transactionTabOptionsUtils, profileStatusMappingUtils } from './utils';
import WithdrawFundsModal from './partials/ManageFundsModal/WithdrawFundsModal';
import AddMoneyModal from './partials/ManageFundsModal/AddMoneyModal';
import DetailedFundsSummaryModal from './partials/ManageFundsModal/DetailedFundsSummary';
import { ALGO_TRADING, MANAGE_FUNDS, ORDER_TYPE } from './partials/ManageFundsModal/config';
import {
  getTransactionInfo,
  initiatePayment,
  getPaymentSuggestions,
  getPayoutBankOptions,
  getTransactionHistory,
  getDetailedFundsSummary,
  getPromptMsg,
  getSubscriptions,
  changePaymentPlan,
} from './api';
import TransactionStatusModal from './partials/ManageFundsModal/TransactionStatusModal';
import { tabOptions, statusFilter, transactionTypeOptions } from './partials/FundsHistoryTable/config';

const Mobile = lazy(() => import('./mobile/index'));
const Desktop = lazy(() => import('./desktop/index'));

const PAGE_NUMBER = 1;
const PAGE_SIZE = 10;
const defaultFundSummary = {};
const SKIP_SUBSCRIPTION_CHARGES = 'skipCharges';
const ManageFunds = () => {
  const [paymentTransactionId, setPaymentTransactionId] = useState('');
  const [paymentId, setPaymentId] = useState('');
  const [inputAmountValue, setInputAmountValue] = useState(0);
  const [isAddButtonDisabled, setIsAddButtonDisabled] = useState(false);
  const [fundsSummary, setFundsSummary] = useState({});
  const [paymentSuggestionInProgress, setPaymentSuggestionInProgress] = useState(true);
  const [fundsHistory, setFundsHistory] = useState({});
  const [bankAccountData, setBankAccountData] = useState([]);
  const [defaultInputAmount, setDefaultInputAmount] = useState(0);
  const [getFundsInProgress, setGetFundsInProgress] = useState(true);
  const [pageSource, addPageSource] = useState('funds');
  const history = useHistory();
  const location = useLocation();
  const { addToast } = useToast();
  const { openModal, closeModal } = useModal();
  const {
    userId,
    displayImage,
    displayName,
    mtfFeatureFlag,
  } = useContext(UserContext);
  const { handleIrAction } = useNonIrModal();
  const [isError, setIsError] = useState(false);
  const [selectedTabId, setSelectedTabId] = useState(1);
  const [currentStatus, setStatus] = useState(statusFilter[0]);
  const [failedHistoryFetch, setFailedHistoryFetch] = useState(false);
  const [transactionTitle, setTransactionTitle] = useState(transactionTypeOptions[0]);
  const [isSubscriptionUpdated, setSubscriptionUpdated] = useState(false);
  const { makeRequest, inProgress: ledgerInProgress, failed: failedLedgerHistoryFetch } = useGetApi();
  const {
    fundsSummary: {
      funds_summary = defaultFundSummary,
    }, inProgress,
  } = useContextBackgroundUpdate(FundsSummaryContext);
  const [paymentSuggestions, setPaymentSuggestions] = useState({});
  const [prompt_msg, setPrompt_msg] = useState();

  const [subscriptionData, setSubsciptionData] = useState(null);
  const [isExpiredOpendModal, setExpiredOpendModal] = useState(false);
  const modalTimerId = useRef(null);
  const algoRef = useRef(null);
  const subscriptionRef = useRef(null);

  useEffect(() => {
    if (location.state?.fromScreen === 'home') {
      sendPageEvent({
        eventDetails: {
          screenName: 'addfunds_homescreen',
          eventType: 'openScreen',
          event_action: 'open_screen',
        },
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => () => {
    // cleanup function
    if (modalTimerId.current !== null) {
      clearTimeout(modalTimerId.current);
      modalTimerId.current = null;
    }
  }, []);

  useEffect(() => {
    const getMsg = async () => {
      try {
        const { data } = await makeRequest(getPromptMsg());
        setPrompt_msg(data?.prompt_msg);
      } catch (error) { }
    };
    getMsg();
  }, [makeRequest, setPrompt_msg]);
  const getModelComponent = useCallback((type) => {
    switch (type) {
      case MANAGE_FUNDS.ADD:
        return AddMoneyModal;
      case MANAGE_FUNDS.WITHDRAW:
        return WithdrawFundsModal;
      case MANAGE_FUNDS.DETAILED_SUMMARY:
        return DetailedFundsSummaryModal;
      default:
        return TransactionStatusModal;
    }
  }, []);

  const showModal = useCallback((type, props = {}) => {
    openModal({
      Component: getModelComponent(type),
      componentProps: props,
    });
  }, [getModelComponent, openModal]);

  const fetchPaymentsSuggestions = useCallback(async () => {
    try {
      const {
        data: { credit, debit, sample_amounts },
      } = await getPaymentSuggestions();
      const creditSuggestions = { ...credit, sample_amounts };
      const debitSuggestions = { ...debit, sample_amounts };
      setPaymentSuggestions({ creditSuggestions, debitSuggestions });
      setIsError(false);
    } catch (error) {
      setIsAddButtonDisabled(true);
      setIsError(true);
    } finally { setPaymentSuggestionInProgress(false); }
  }, []);

  const fetchUserSubscription = useCallback(async (requiredInputValue) => {
    try {
      const { data } = subscriptionRef.current ? subscriptionRef.current : await getSubscriptions(userId);
      LocalStorage.set(SKIP_SUBSCRIPTION_CHARGES, data.consolidatedCharges.totalAmount === 0, (Math.min(moment().endOf('day').valueOf(), moment().add(12, 'hours').valueOf())
          - new Date().getTime()) / 1000);

      if (((isEmpty(data) || (data && data.consolidatedCharges.totalAmount === 0))
        || LocalStorage.get(SKIP_SUBSCRIPTION_CHARGES))) {
        const params = {
          amount: requiredInputValue,
        };
        const { data: { payment_txn_id: paymentTxnId, id: payId } } = await initiatePayment(params);
        setPaymentTransactionId(paymentTxnId);
        setPaymentId(payId);
        setSubscriptionUpdated(true);
      } else {
        setSubsciptionData(data);
        setSubscriptionUpdated(true);
      }
      setIsAddButtonDisabled(false);
    } catch (error) {
      setIsAddButtonDisabled(false);
      const message = error?.meta?.displayMessage || CONSTANTS.ERROR_MESSAGE;
      addToast(message, APPEARANCE_TYPES.FAIL);
    }
  }, [userId, addToast]);

  function handleSubscriptionUpdate() {
    setSubscriptionUpdated(false);
  }

  const handleAddMoney = useCallback((inputVal) => {
    async function addMoney() {
      setInputAmountValue(parseFloat(inputVal));
      const params = { amount: inputVal };
      setIsAddButtonDisabled(true);
      try {
        if (!LocalStorage.get(SKIP_SUBSCRIPTION_CHARGES)) {
          fetchUserSubscription(inputVal);
        } else {
          const { data: { payment_txn_id: paymentTxnId, id: payId } } = await initiatePayment(params);
          setPaymentTransactionId(paymentTxnId);
          setPaymentId(payId);
          setSubscriptionUpdated(true);
          setIsAddButtonDisabled(false);
        }
      } catch (error) {
        setIsAddButtonDisabled(false);
        const message = error?.meta?.displayMessage || CONSTANTS.ERROR_MESSAGE;
        addToast(message, APPEARANCE_TYPES.FAIL);
      }
    }
    addMoney();
  }, [addToast, fetchUserSubscription]);

  const showAddFundsModal = useCallback(() => {
    const clientUniqueTxnId = algoRef.current;
    const props = {
      inputAmountValue,
      paymentTransactionId,
      closeModal,
      openModal,
      pageSource,
      subscriptionData,
      handleSubscriptionUpdate,
      clientUniqueTxnId,
      paymentId,
      retryPayment,
      fetchTransactionHistory,
    };
    showModal(MANAGE_FUNDS.ADD, props);
  }, [inputAmountValue, paymentTransactionId, closeModal, openModal, pageSource,
    subscriptionData, paymentId, showModal, retryPayment, fetchTransactionHistory]);

  // algo trading funds flow ---->
  const changePlanPaymentApi = useCallback(async (plan_id) => {
    const body = {
      customerId: userId,
      newPlanId: plan_id,
    };
    try {
      const { data } = await changePaymentPlan(body);
      algoRef.current = data?.clientUniqueTxnId;
      setInputAmountValue(parseFloat(data.charges.totalAmount));
      setPaymentTransactionId(data.paymentUrl);
      setSubscriptionUpdated(true);
      setIsAddButtonDisabled(false);
    } catch (err) { }
  }, [userId]);

  const isSufficientAmount = useCallback(async (amount) => {
    try {
      const { data } = await getSubscriptions(userId);
      subscriptionRef.current = { data };
      if (amount >= paymentSuggestions?.creditSuggestions?.min
      && amount <= paymentSuggestions?.creditSuggestions?.max && amount >= data?.consolidatedCharges?.totalAmount
      && funds_summary?.trade_balance >= 0) {
        handleAddMoney(amount);
      }
    } catch (error) {
    }
  }, [funds_summary, handleAddMoney, paymentSuggestions, userId]);

  useEffect(() => {
    const query = location.search.split('?')[1];
    const queryParams = mapQueryString(query);
    if (queryParams.type === 'algo') {
      changePlanPaymentApi(queryParams.plan_id);
    }
    if (queryParams.type === 'fms') {
      isSufficientAmount(queryParams?.amount);
    }
  }, [changePlanPaymentApi, handleAddMoney, isSufficientAmount, location.search, paymentSuggestions]);
  // <---- algo trading funds flow

  const fetchTransactionHistory = useCallback(async () => {
    setGetFundsInProgress(true);
    setFailedHistoryFetch(false);
    try {
      const { data } = await getTransactionHistory(
        tabOptions.find(({ id }) => id === selectedTabId).type === ORDER_TYPE.ADD
          ? 'C' : 'D', PAGE_SIZE, PAGE_NUMBER, currentStatus.id,
      );

      setFundsHistory(data);
    } catch (error) {
      setFailedHistoryFetch(true);
    } finally {
      setGetFundsInProgress(false);
    }
  }, [selectedTabId, currentStatus, setFundsHistory, setGetFundsInProgress]);

  useEffect(() => {
    fetchTransactionHistory();
  }, [fetchTransactionHistory]);

  useEffect(() => {
    if (isSubscriptionUpdated) {
      showAddFundsModal();
    }
  }, [showAddFundsModal, isSubscriptionUpdated]);

  const retryPayment = useCallback((amount, planId) => {
    closeModal();
    modalTimerId.current = setTimeout(() => {
      if (planId) {
        changePlanPaymentApi(planId);
      } else {
        handleAddMoney(amount);
      }
    }, 400);
  }, [changePlanPaymentApi, closeModal, handleAddMoney]);

  const showTransactionInfo = useCallback((transactionId, type, showStatusCtaBtn = false) => {
    const algoCachedData = { ...LocalStorage.get(ALGO_TRADING) };
    let transactionData;
    const body = { txnId: transactionId, paymentType: SUBSCRIPTION_TYPE.UPGRADE };
    if (algoCachedData?.clientUniqueTxnId) {
      transactionData = getTransactionDetails(body);
    } else {
      transactionData = getTransactionInfo(transactionId);
    }
    transactionData.then(({ data: { funds_txns: transactionInfo } }) => {
      showModal(MANAGE_FUNDS.INFO_MODAL, {
        transactionInfo,
        retryPayment,
        showStatusCtaBtn,
        type,
        pageSource,
        closeModal,
        fetchTransactionHistory,
      });
    }).catch((error) => {
      const message = error?.meta?.displayMessage || CONSTANTS.ERROR_MESSAGE;
      addToast(message, APPEARANCE_TYPES.FAIL);
    });
  }, [addToast, closeModal, fetchTransactionHistory, pageSource, retryPayment, showModal]);

  const showDetailedSummary = useCallback(() => {
    sendPageEvent({
      eventDetails: {
        screenName: 'funddetails',
        eventType: 'openScreen',
        event_action: 'open_screen',
      },
    });
    getDetailedFundsSummary().then(({ data: { detailed_info } }) => {
      showModal(MANAGE_FUNDS.DETAILED_SUMMARY, {
        detailed_info,
        utilisedAmount: fundsSummary.utilised_amount,
        openingBalance: fundsSummary.opening_balance,
      });
    }).catch((error) => {
      const message = error?.meta?.displayMessage || CONSTANTS.ERROR_MESSAGE;
      addToast(message, APPEARANCE_TYPES.FAIL);
    });
  }, [addToast, fundsSummary.opening_balance, fundsSummary.utilised_amount, showModal]);

  const getTransactionIdFromUrl = useCallback(() => {
    const urlPathArray = location.pathname.split('/');
    const index = urlPathArray.indexOf('funds');
    if (index !== -1 && urlPathArray.length > (index + 1)) {
      const transactionId = urlPathArray[index + 1];
      urlPathArray.pop();
      history.replace(urlPathArray.join('/'));
      return transactionId;
    }
    return null;
  }, [location, history]);

  const getSelectedFundTabFromUrl = useCallback(() => {
    const { search } = location;
    const queryParam = getUrlParameter('view', search);
    if (queryParam === 'ledger') {
      setTransactionTitle(transactionTypeOptions[1]);
    }
  }, [location]);

  const getAddModalFundsInfoFromUrl = useCallback(() => {
    if (!isExpiredOpendModal) {
      const { search } = location;
      const queryParam = getUrlParameter('autoExpiredScreen', search);
      const amount = getUrlParameter('amount', search);
      if (queryParam === 'present') {
        handleAddMoney(amount);
        setExpiredOpendModal(true);
      }
    }
  }, [location, handleAddMoney, isExpiredOpendModal]);

  useEffect(() => {
    getAddModalFundsInfoFromUrl();
  }, [getAddModalFundsInfoFromUrl]);

  const getCreditAmount = useCallback(() => {
    const queryString = location.search.substring(1);
    const queryParams = mapQueryString(queryString);
    const { creditAmount, source } = queryParams;
    if (creditAmount && creditAmount === `${parseInt(creditAmount, 10)}`) {
      addPageSource(source);
      history.replace(location.pathname);
      return creditAmount;
    }
    return null;
  }, [location, history]);

  useEffect(() => {
    const credit = { ...(paymentSuggestions.creditSuggestions || {}) };
    const debit = { ...(paymentSuggestions.debitSuggestions || {}) };
    const summary = {
      ...funds_summary,
      prompt_msg,
      config: {
        credit,
        debit,
      },
    };
    setFundsSummary(summary);
  }, [funds_summary, prompt_msg, paymentSuggestions]);

  const fetchBankDetails = useCallback(() => {
    getPayoutBankOptions(userId).then((res) => {
      const { data } = res;
      setBankAccountData(data);
    });
  }, [userId]);

  const getModalParamsfromUrl = useCallback(() => {
    let transactionId;
    const algoCachedData = { ...LocalStorage.get(ALGO_TRADING) };
    if (algoCachedData?.clientUniqueTxnId) {
      transactionId = algoCachedData?.clientUniqueTxnId;
    } else {
      transactionId = getTransactionIdFromUrl();
    }
    const creditAmount = getCreditAmount();
    if (transactionId) {
      const showStatusCtaBtn = true;
      const type = transactionId.startsWith('EQIN') ? ORDER_TYPE.ADD : ORDER_TYPE.WITHDRAW;
      showTransactionInfo(transactionId, type, showStatusCtaBtn);
    } else if (creditAmount) {
      setDefaultInputAmount(creditAmount);
      handleAddMoney(creditAmount);
    }
  }, [getCreditAmount, getTransactionIdFromUrl, handleAddMoney, showTransactionInfo]);

  const { isInvestmentReady, isInvestmentReadyFO, irStatus } = useIrData();

  const loadInitialData = useCallback(() => {
    if (!isInvestmentReady && !isInvestmentReadyFO) return;
    fetchPaymentsSuggestions();
    fetchBankDetails();
    getModalParamsfromUrl();
    getSelectedFundTabFromUrl();
  }, [isInvestmentReady, isInvestmentReadyFO, fetchPaymentsSuggestions,
    fetchBankDetails, getModalParamsfromUrl, getSelectedFundTabFromUrl]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const handleWithdrawSuccess = useCallback((transactionId) => {
    loadInitialData();
    if (tabOptions.find(({ id }) => id === selectedTabId).type === ORDER_TYPE.WITHDRAW) {
      fetchTransactionHistory();
    }
    setBankAccountData([]);
    showTransactionInfo(transactionId, ORDER_TYPE.WITHDRAW);
  }, [fetchTransactionHistory, loadInitialData, selectedTabId, showTransactionInfo]);

  const handleWithdrawal = (withdrawalAmount = 0) => {
    if (!isEmpty(bankAccountData)) {
      const {
        withdrawal_balance: maxWithdrawalBalance = 0,
        config: { debit: debitConfig },
        trade_balance: tradeBalance = 0,
        collaterals: collateralsValue = 0,
      } = fundsSummary;
      const props = {
        debitConfig,
        bankAccountData,
        handleWithdrawSuccess,
        maxWithdrawalBalance,
        closeModal,
        tradeBalance,
        collateralsValue,
        withdrawalAmount,
        showTransactionInfo,
        openModal,
      };
      showModal(MANAGE_FUNDS.WITHDRAW, props);
    }
  };

  if (!(inProgress || paymentSuggestionInProgress) && isError) {
    return (
      <ErrorView refresh={() => { setPaymentSuggestionInProgress(true); fetchPaymentsSuggestions(); }} />
    );
  }

  const changeSelectedTab = (tab) => {
    if (tab === 'ledgerHistory') {
      setTransactionTitle(transactionTypeOptions[1]);
    } else if (tab === 'mtfHistory') {
      setTransactionTitle(transactionTypeOptions[2]);
    } else {
      setTransactionTitle(transactionTypeOptions[0]);
    }
    const ind = transactionTypeOptions.findIndex((val) => val.id === tab);
    setTransactionTitle(transactionTypeOptions[ind]);
  };
  const navigateToProfilePage = () => history.push(Routes[ROUTE_NAME.USER_PROFILE].url);

  if (mobileBrowser()) {
    return (
      <Suspense fallback={<PmlLogo />}>
        <Mobile
          navigateToProfilePage={navigateToProfilePage}
          displayImage={displayImage}
          displayName={displayName}
          profileStatusMappingUtils={profileStatusMappingUtils}
          irStatus={irStatus}
          isInvestmentReady={isInvestmentReady}
          handleIrAction={handleIrAction}
          handleAddMoney={handleAddMoney}
          handleWithdrawal={handleWithdrawal}
          fundsSummary={fundsSummary}
          isAddButtonDisabled={isAddButtonDisabled}
          defaultInputAmount={defaultInputAmount}
          isInvestmentReadyFO={isInvestmentReadyFO}
          inProgress={inProgress}
          paymentSuggestionInProgress={paymentSuggestionInProgress}
          showDetailedSummary={showDetailedSummary}
        />
      </Suspense>
    );
  }

  return (
    <Suspense fallback={<PmlLogo />}>
      <Desktop
        isInvestmentReady={isInvestmentReady}
        handleIrAction={handleIrAction}
        handleAddMoney={handleAddMoney}
        handleWithdrawal={handleWithdrawal}
        fundsSummary={fundsSummary}
        isAddButtonDisabled={isAddButtonDisabled}
        defaultInputAmount={defaultInputAmount}
        isInvestmentReadyFO={isInvestmentReadyFO}
        inProgress={inProgress}
        paymentSuggestionInProgress={paymentSuggestionInProgress}
        showDetailedSummary={showDetailedSummary}
        transactionTabOptionsUtils={transactionTabOptionsUtils}
        changeSelectedTab={changeSelectedTab}
        getFundsInProgress={getFundsInProgress}
        fundsHistory={fundsHistory}
        showTransactionInfo={showTransactionInfo}
        setFundsHistory={setFundsHistory}
        setGetFundsInProgress={setGetFundsInProgress}
        selectedTabId={selectedTabId}
        setSelectedTabId={setSelectedTabId}
        currentStatus={currentStatus}
        setStatus={setStatus}
        failedHistoryFetch={failedHistoryFetch}
        fetchTransactionHistory={fetchTransactionHistory}
        transactionTitle={transactionTitle}
        makeRequest={makeRequest}
        ledgerInProgress={ledgerInProgress}
        failedLedgerHistoryFetch={failedLedgerHistoryFetch}
        mtfFeatureFlag={mtfFeatureFlag}
      />
    </Suspense>
  );
};

export default ManageFunds;
