import { ENDPOINTS } from '@constants/api-constants';
import ApiService from '@service/ApiService';
import moment from 'moment';

const LEDGER_HISTORY_PARAMS = {
  FROM_DATE: moment().subtract(6, 'days').format('YYYY-MM-DD'),
  TO_DATE: moment().format('YYYY-MM-DD'),
};

const getFundsSummary = () => ApiService(ENDPOINTS.FUNDS.GET_SUMMARY, null);

const getAvailableFundsSummary = (body) => ApiService(ENDPOINTS.FUNDS.GET_ORDERPAD_SUMMARY, null, body);

const getPromptMsg = () => ApiService(ENDPOINTS.FUNDS.GET_PROMPT_MSG);

const getPaymentSuggestions = () => ApiService(ENDPOINTS.FUNDS.GET_PAYMENT_SUGGESTIONS);

const getTransactionHistory = (txnType, pageSize, pageNumber, status) => ApiService(
  ENDPOINTS.FUNDS.GET_TRANSACTION_HISTORY, [txnType, pageSize, pageNumber, status],
);

const getTransactionInfo = (transactionId) => ApiService(ENDPOINTS.FUNDS.GET_TRANSACTION_INFO, transactionId);

const getBankDetails = (userId) => ApiService(ENDPOINTS.FUNDS.GET_BANK_DETAILS, userId);

const initiatePayment = (params) => ApiService(ENDPOINTS.FUNDS.INITIATE_PAYMENT, null, params);

const getPpbBalance = (params) => ApiService(ENDPOINTS.FUNDS.GET_PPB_BALANCE, null, params);

const getPaymentGateway = (params) => ApiService(ENDPOINTS.FUNDS.GET_PAYMENT_GATEWAY, null, params);

const getPaymentOptions = (txnId) => ApiService(ENDPOINTS.FUNDS.GET_PAYMENT_OPTIONS, txnId);

const makePayment = (params) => ApiService(ENDPOINTS.FUNDS.MAKE_PAYMENT, null, params);

const debitFunds = (params) => ApiService(ENDPOINTS.FUNDS.WITHDRAW_FUNDS, null, params);

const getPayoutBankOptions = (userId) => ApiService(ENDPOINTS.FUNDS.GET_PAYOUT_BANK_OPTIONS, userId);

const getSubscriptions = (userId) => ApiService(ENDPOINTS.FUNDS.GET_SUBSCRIPTIONS, userId);

const getLedgerHistory = () => ApiService(
  ENDPOINTS.FUNDS.GET_LEDGER_HISTORY,
  [LEDGER_HISTORY_PARAMS.FROM_DATE, LEDGER_HISTORY_PARAMS.TO_DATE],
);
const validateVpa = (params) => ApiService(ENDPOINTS.FUNDS.VALIDATE_VPA, null, params);

const processVpaPayment = (params) => ApiService(ENDPOINTS.FUNDS.PROCESS_VPA_PAYMENT, null, params);

const getDetailedFundsSummary = () => ApiService(ENDPOINTS.FUNDS.GET_DETAILED_SUMMARY);

const cancelPayout = (body) => ApiService(ENDPOINTS.FUNDS.CANCEL_PAYOUTS, null, body);

const verifyCoupon = (userId, body) => ApiService(ENDPOINTS.FUNDS.VERIFY_COUPON, userId, body);

const changePaymentPlan = (body) => ApiService(ENDPOINTS.FUNDS.CHANGE_PAYMENT_PLAN, null, body);

const getFixBroker = () => fetch('https://static.paytmmoney.com/fixbro/static-data/v1/FixBro.json').then((res) => res.json());

const checkFirstPayin = (txnId) => ApiService(ENDPOINTS.FUNDS.CHECK_FIRST_PAYIN, txnId);

const getMtfHistory = (status, isMtfEnabled) => ApiService(
  ENDPOINTS.FUNDS.GET_MTF_HISTORY,
  [status, isMtfEnabled],
);

export {
  getFundsSummary,
  getPromptMsg,
  getPaymentSuggestions,
  getTransactionHistory,
  getTransactionInfo,
  getBankDetails,
  initiatePayment,
  getPpbBalance,
  getPaymentOptions,
  makePayment,
  debitFunds,
  getPayoutBankOptions,
  getSubscriptions,
  getLedgerHistory,
  validateVpa,
  processVpaPayment,
  getDetailedFundsSummary,
  cancelPayout,
  verifyCoupon,
  getPaymentGateway,
  changePaymentPlan,
  getFixBroker,
  checkFirstPayin,
  getMtfHistory,
  getAvailableFundsSummary,
};
