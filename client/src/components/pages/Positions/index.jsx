import React, { useState, useCallback, useEffect } from 'react';
import { cloneDeep } from 'lodash';
import useGetApi from '@common/UseApi/useGetApi';
import useSecurity from '@pages/Company/useSecurity';
import { getPositionsData, getInterPositionsData } from '@modules/Positions/positionsApi';
import { INTEROPERABILITY_MODES, EXCHANGE_TYPES_MODES } from '@modules/Positions/enums';
import useRefreshPage, { positionFilterFn } from '@common/UseRefreshPage/useRefreshPage';
import { useIrData } from '@layout/App/UserContext';
import useDowntimeConfig from '@common/useDowntimeConfig';
import EmptyState from '@common/EmptyState';
import PositionsHOC from './Positions';
import { MultipleExitContextProvider } from './MultipleExitContext';
import styles from './index.scss';
import { DOWNTIME } from './enums';

function PositionsPage() {
  const { makeRequest, inProgress, failed } = useGetApi();
  const [data, setData] = useState([]);
  const [interoperabilityPositionsData, setInteroperabilityPositionsData] = useState([]);
  const [interoperability, setInteroperability] = useState(INTEROPERABILITY_MODES[0].id);
  const [exchangeType, setExchangeType] = useState(EXCHANGE_TYPES_MODES[0].id);
  const { irStatus, isInvestmentReady, isInvestmentReadyFO } = useIrData();
  const { configJson: { positions_downtime }, fetchInProgress, getDowntimeConfig } = useDowntimeConfig();

  const getData = useCallback(() => ((isInvestmentReady || isInvestmentReadyFO) && getPositionsData().then(
    ({ data: positionsData }) => setData(positionsData),
  )), [isInvestmentReady, isInvestmentReadyFO]);

  const getInteroperabilityData = useCallback(() => ((isInvestmentReady || isInvestmentReadyFO)
  && getInterPositionsData().then(
    ({ data: interPositionsData }) => setInteroperabilityPositionsData(interPositionsData),
  )), [isInvestmentReady, isInvestmentReadyFO]);

  const props = useSecurity();

  const makeRequestRef = useCallback(() => {
    if (interoperability !== INTEROPERABILITY_MODES[0].id) {
      makeRequest(getInteroperabilityData()).catch();
    } else {
      makeRequest(getData()).catch();
    }
  }, [interoperability, makeRequest, getInteroperabilityData, getData]);

  const refreshData = interoperability !== INTEROPERABILITY_MODES[0].id ? getInteroperabilityData : getData;

  useRefreshPage(refreshData, positionFilterFn);

  useEffect(() => {
    const interoperabilityData = [];
    interoperabilityPositionsData.forEach((interPositionVal) => {
      let interPositionObj = cloneDeep(interPositionVal);
      if (interPositionObj?.scrip_ids && interPositionObj.scrip_ids.length) {
        if (interPositionObj.scrip_ids.length === 1) {
          interPositionObj = {
            ...interPositionObj,
            exchange: interPositionObj.scrip_ids[0]?.exchange,
            security_id: interPositionObj.scrip_ids[0]?.security_id,
            tick_size: interPositionObj.scrip_ids[0]?.tick_size,
          };
        } else {
          interPositionObj.scrip_ids.forEach((scripVal) => {
            if (scripVal?.exchange === exchangeType.toUpperCase()) {
              interPositionObj = {
                ...interPositionObj,
                exchange: scripVal?.exchange,
                security_id: scripVal?.security_id,
                tick_size: scripVal?.tick_size,
              };
            }
          });
        }
        interoperabilityData.push(interPositionObj);
      }
    });
    setData(interoperabilityData);
  }, [interoperabilityPositionsData, exchangeType]);

  useEffect(() => {
    makeRequestRef();
  }, [makeRequestRef]);

  const retryDowntime = (e) => {
    e.preventDefault();
    getDowntimeConfig();
  };

  if (positions_downtime?.equity_positions_downtime) {
    return (
      <>
        <div className={`${styles.downTimeHeader}`}>{DOWNTIME.TITLE}</div>
        <EmptyState
          text={positions_downtime?.equity_positions_heading}
          subText={positions_downtime?.equity_positions_message}
          ctaText={DOWNTIME.REFRESH}
          customIconImg={positions_downtime?.equity_positions_img}
          className={styles.portFolioDownTime}
          onCtaClick={retryDowntime}
        />
      </>
    );
  }
  const reqInprogress = (fetchInProgress || !irStatus || (isInvestmentReady || isInvestmentReadyFO)) && inProgress;
  return (
    <MultipleExitContextProvider {...props}>
      <PositionsHOC
        data={data}
        inProgress={reqInprogress}
        failed={failed}
        refresh={makeRequestRef}
        interoperability={interoperability}
        exchangeType={exchangeType}
        setInteroperability={setInteroperability}
        setExchangeType={setExchangeType}
      />
    </MultipleExitContextProvider>
  );
}

export default PositionsPage;
