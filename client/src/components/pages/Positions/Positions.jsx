import React, {
  useState, useMemo, useContext, useEffect,
} from 'react';

import PlaceOrder from '@modules/OrderFlow/PlaceOrder';
import {
  actionControls,
  hasOvernightPosition,
  shapeInitialData,
} from '@modules/Positions/utils';
import PositionsModal from '@modules/Positions/ModalContent';
import { PositionList, ConvertOrder } from '@modules/Positions';
import NumberedTablist from '@common/Tabs/NumberedTablist';
import { TABLE_DATA, TABLE_DATA_OVERALL } from '@modules/Positions/statics';
import { ICON_NAME, staticImagePath } from '@common/Icon/enums';
import EmptyState from '@common/EmptyState';
import Popper from '@common/Popper';
import WithLoader from '@common/WithLoader';
import { useToast } from '@common/Toast';
import Toggle from '@common/Toggle';
import {
  POSITIONS_TAB, STATICS, INTEROPERABILITY_MODES, EXCH<PERSON><PERSON>_TYPES_MODES,
} from '@modules/Positions/enums';
import { useModal } from '@layout/App/ModalContext';
import PositionsLoader from '@pages/Positions/loader';
import { sendEvent } from '@service/AnalyticsService';
import { COMMON_PAGE_GA, POSITIONS_PAGE_GA } from '@constants/ga-events';
import { useHistory } from 'react-router-dom';
import { ROUTE_NAME, INFO_CARD } from '@utils/enum';
import { DraggableModalContext } from '@common/usePlaceOrder';
import InfoCard from '@pages/Portfolio/partials/InfoCard';
import { RETURNS_TYPE, switchOptions } from '@pages/Portfolio/partials/PortfolioTable/config';
import Switch from '@common/Switch';
import { classNames as cx } from '@utils/style';
import AuthorizeMtf from '@modules/MTF/AuthorizeMtf';
import useNudge from '@common/useNudge';
import { UserContext } from '@layout/App/UserContext';
import { mobileBrowser } from '@utils';
import { button } from '@commonStyles';
import styles from './index.scss';
import { PositionsHeader } from './partials';
import Routes from '../../../routes';
import { useMultipleExitContext } from './MultipleExitContext';

const isMobile = mobileBrowser();

function Positions({
  data, interoperability, setInteroperability, exchangeType, setExchangeType,
}) {
  const responseData = useMemo(() => shapeInitialData(data), [data]);
  const [activeTab, setActiveTab] = useState(responseData.tabs[0].id);
  const [activePositionTab, setPositionTab] = useState({});
  const { addToast } = useToast();
  const { openModal, closeModal } = useModal();
  const { openDraggableModal } = useContext(DraggableModalContext);
  const [openedMarketDepth, setOpenedMarketDepth] = useState(-1);
  const [showControl, setShowControl] = useState(-1);
  const hasOvernightPositions = hasOvernightPosition(data);
  const [activeSwitch, setActiveSwitch] = useState(hasOvernightPositions
    ? RETURNS_TYPE.OVERALL : RETURNS_TYPE.TODAY);
  const {
    selectedPositions, openExitPositions, setUncheckedForAll, setInteropsShow = () => {},
  } = useMultipleExitContext();

  const { webConfig } = useContext(UserContext);

  useNudge(JSON.parse(webConfig?.nudgeScreenConfig || null)?.POSITIONS);

  const handlePositionTab = (tab) => {
    setPositionTab(tab);
  };

  useEffect(() => {
    setShowControl(-1);
  }, [openedMarketDepth]);

  useEffect(() => {
    if (interoperability === INTEROPERABILITY_MODES[1].id) {
      setInteropsShow(true);
    } else {
      setInteropsShow(false);
    }
  }, [interoperability, setInteropsShow]);

  const handleControls = ({
    type, positionData, handleExpansion, pmlId,
  }) => (
    actionControls({
      type,
      positionData,
      handleControls,
      components: { PositionsModal, PlaceOrder, ConvertOrder },
      addToast,
      openModal,
      openDraggableModal,
      openedMarketDepth,
      setOpenedMarketDepth,
      activeTab,
      handleExpansion,
      pmlId,
      closeModal,
    })
  );

  const history = useHistory();

  const checkForTabClosed = (id) => {
    if (id === POSITIONS_TAB.CLOSE) {
      setUncheckedForAll(true);
    }
  };

  return (
    <div className={styles.container}>
      <PositionsHeader
        positions={responseData}
        activeSwitch={activeSwitch}
      />
      <InfoCard
        infoCardObject={INFO_CARD.POSITIONS}
        className={styles.SleekCard}
        messageClass={styles.messageClass}
        closeIconClass={styles.close}
      />
      <AuthorizeMtf />
      <div className={styles.content}>
        { data.length && !isMobile
          ? (
            <div className={cx(styles.toggleContainer, {
              [styles.toggleOn]: interoperability === INTEROPERABILITY_MODES[1].id,
            })}
            >
              {
              interoperability === INTEROPERABILITY_MODES[1].id
                ? (
                  <div className={styles.exchangeModeContainer}>
                    <div className={styles.exchangeHeadingText}>{STATICS.LIVE_PRICE_TEXT}</div>
                    <Switch
                      options={EXCHANGE_TYPES_MODES}
                      active={exchangeType}
                      setSwitch={(selected) => setExchangeType(selected)}
                    />
                  </div>
                )
                : null
            }
              <div className={styles.interoperabilityContentContainer}>
                <Popper
                  iconName={ICON_NAME.INFO_DARK}
                  activeIconName={ICON_NAME.INFO_DARK}
                  iconSize={2}
                  showDropDownIcon={false}
                  iconStyles={styles.iconStyles}
                  className={styles.popper}
                  closeIcon
                  closeIconClassName={styles.iconClose}
                  closeIconSize={1.91}
                >
                  <div
                    onClick={(e) => e.stopPropagation()}
                    role="presentation"
                  >
                    <div className={styles.interoperabilityInfoText}>
                      {STATICS.INTEROPERABILITY_INFO}
&nbsp;
                      <a
                        href="https://www.paytmmoney.com/blog/introducing-interoperability-positions-view/"
                        className={styles.link}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {STATICS.INTEROPERABILITY_KNOW_MORE}
                      </a>
                    </div>
                  </div>
                </Popper>
                <div className={styles.interoperabilityText}>
                  {STATICS.INTEROPERABILITY}
                </div>
                <Toggle
                  options={INTEROPERABILITY_MODES}
                  activeId={interoperability}
                  onClickCb={({ id }) => setInteroperability(id)}
                  mode="toggle"
                />
              </div>
            </div>
          )
          : null}
        <div className={styles.header}>
          <NumberedTablist
            options={responseData.tabs}
            activeTab={activeTab}
            className={(hasOvernightPositions && selectedPositions.length === 0)
              ? styles.scrolledTabItem : styles.defaultTabItem}
            setTab={(id) => {
              setOpenedMarketDepth(-1);
              setActiveTab(id);
              checkForTabClosed(id);
              sendEvent({
                event_category: POSITIONS_PAGE_GA.L1_CLICKED.EVENT_CATEGORY,
                event_action: POSITIONS_PAGE_GA.L1_CLICKED.EVENT_ACTION,
                event_label: POSITIONS_PAGE_GA.L1_CLICKED.EVENT_LABEL(
                  responseData.tabs.find(
                    (tab) => tab.id === id,
                  ).label.toLowerCase(),
                ),
              });
            }}
          />
          {selectedPositions.length ? (
            <button
              onClick={openExitPositions}
              className={cx([button.btn, button.btnLarge, button.secondaryBtnFill, styles.btn])}
            >
              <img src={`${staticImagePath}/positions/exit.svg`} alt="exit" />
              {`Exit Position${selectedPositions.length > 1 ? 's' : ''}`}
            </button>
          ) : null}
          {hasOvernightPositions && selectedPositions.length === 0 && (
          <Switch
            options={switchOptions}
            active={activeSwitch}
            setSwitch={setActiveSwitch}
          />
          )}
        </div>
        {
          data.length ? (
            <PositionList
              tableData={activeSwitch === RETURNS_TYPE.TODAY ? TABLE_DATA : TABLE_DATA_OVERALL}
              options={responseData[activeTab]}
              positionType={activeTab}
              handlePositionTab={handlePositionTab}
              activeTab={activePositionTab}
              onClick={handleControls}
              openedMarketDepth={openedMarketDepth}
              setOpenedMarketDepth={setOpenedMarketDepth}
              showControl={showControl}
              setShowControl={setShowControl}
              activeSwitch={activeSwitch}
              interoperability={interoperability}
            />
          ) : (
            <EmptyState
              text={STATICS.TEXT}
              subText={STATICS.SUB_TEXT}
              ctaText={STATICS.CTA_TEXT}
              iconName={ICON_NAME.EMPTY_POSITIONS}
              className={styles.emptyState}
              onCtaClick={() => {
                history.push(Routes[ROUTE_NAME.MOVERS].url);
                sendEvent({
                  event_category: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_CATEGORY,
                  event_action: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_ACTION,
                  event_label: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_LABEL('positions'),
                });
              }}
            />
          )
        }
      </div>
    </div>
  );
}

const PositionsHOC = WithLoader({ WrappedComponent: Positions, LoadingComponent: PositionsLoader });

export {
  PositionsHOC as default,
  Positions,
};
