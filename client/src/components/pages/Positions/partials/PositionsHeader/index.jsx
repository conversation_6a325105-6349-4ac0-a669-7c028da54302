import React from 'react';
import PropTypes from 'prop-types';

import { STATICS, POSITIONS_TAB } from '@modules/Positions/enums';
import useCombineStockFeeds from '@modules/Positions/useCombineStockFeeds';
import { hasOvernightPosition, resultDecider, updateHeaderValues } from '@modules/Positions/utils';

import { Change } from '@common/Prices';
import Header from '@common/Header';
import { SquareOffMsg } from '@pages/Home/partials/PositionsCard/PositionsDetailsCard';
import { RETURNS_TYPE } from '@pages/Portfolio/partials/PortfolioTable/config';
import useSort from '@common/useSort';
import usePositionFeed from '@modules/Positions/usePositionFeed';
import { mobileBrowser, formatPrice, roundValue } from '@utils';

import styles from './index.scss';
import { findPnl } from './utils';

function PositionsHeader({ positions, activeSwitch }) {
  const headerValues = updateHeaderValues(
    useCombineStockFeeds({ positions: positions[POSITIONS_TAB.ALL] }),
    positions[POSITIONS_TAB.ALL],
  );
  const requiredStreams = usePositionFeed();
  const { data } = useSort({
    rawData: positions.All,
    requiredFeedFxn: requiredStreams,
  });
  const { realised, unrealised } = findPnl(data, activeSwitch);
  const DataToRender = [{
    label: STATICS.PROFIT_LOSS,
    id: STATICS.PROFIT_LOSS,
    value: resultDecider({
      value: headerValues?.PL,
      component: Change({
        value: headerValues?.PL,
        withRupee: true,
        withSign: true,
        className: styles.profitLossValue,
      }),
    }),
  }];

  if (hasOvernightPosition(positions[POSITIONS_TAB.ALL])) {
    DataToRender.push({
      label: STATICS.OVERALL_PROFIT_LOSS,
      id: STATICS.OVERALL_PROFIT_LOSS,
      value: resultDecider({
        value: headerValues?.overallPL,
        component: Change({
          value: headerValues?.overallPL,
          withRupee: true,
          withSign: true,
          className: styles.profitLossValue,
        }),
      }),
    });
  }

  DataToRender.push({
    label: STATICS.TRADE_VALUE,
    id: STATICS.TRADE_VALUE,
    value: resultDecider({
      value: headerValues?.TV,
      component: formatPrice(headerValues?.TV),
    }),
  }, {
    label: STATICS.REALISED_PNL,
    id: STATICS.REALISED_PNL,
    value: `${(roundValue(realised) < 0) ? '-' : ''}₹ ${formatPrice(realised, 2)}`,
  }, {
    label: STATICS.UNREALISED_PNL,
    id: STATICS.UNREALISED_PNL,
    value: `${(roundValue(unrealised) < 0) ? '-' : ''}₹ ${formatPrice(unrealised, 2)}`,
  });

  if (mobileBrowser()) {
    return (
      <div className={styles.mobile}>
        <div>
          <div className={styles.label}>
            {activeSwitch === RETURNS_TYPE.OVERALL
              ? STATICS.OVERALL_PROFIT_LOSS : STATICS.PROFIT_LOSS}
          </div>
          <Change
            value={activeSwitch === RETURNS_TYPE.OVERALL ? headerValues?.overallPL : headerValues?.PL}
            className={styles.profitLossValue}
            withRupee
            withSign
          />
        </div>
      </div>
    );
  }

  return (
    <>
      <Header
        header={STATICS.TODAY_POSITION}
        options={DataToRender}
        showStatus
      />
      <SquareOffMsg openPositions={positions[POSITIONS_TAB.OPEN]} />
    </>
  );
}

PositionsHeader.propTypes = {
  positions: PropTypes.objectOf(Object).isRequired,
};

export default PositionsHeader;
