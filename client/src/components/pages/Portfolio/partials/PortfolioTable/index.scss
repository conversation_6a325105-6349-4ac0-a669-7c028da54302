@import '../../../../../styles/main';

$filtersBorderColor: rgba(173, 175, 182, .2);
$toggleBackgroundColor: rgba(238, 156, 22, .2);
$caTagBackgroundColor: rgba(238, 156, 22, .1);
$betaBackground: rgba(238, 156, 22, .1);

.rowContainer {
  @include respond(phone) {
    padding-bottom: 10rem;
  }
}

.searchIcon {
  margin-left: 1rem;
  margin-right: 1rem;
}

.closeIcon {
  cursor: pointer;
}

.wealthdesk {
  background-color: $light-yellow;
  padding: 1rem 1.5rem;
  color: $grey1;
  display: flex;
  justify-content: space-between;

  @include typography(h7);
}

.knowMore {
  color: $secondary-color;

  @include typography(h7, semibold);
}

.filterHeader {
  padding: .9rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: .2rem;
  margin-right: 1rem;

  @include respond(phone) {
    background-color: $forever-white;
  }
}

.tableRow {
  padding-right: 3.5rem;

  @include respond(phone) {
    padding-right: 1.2rem;
  }

  .borderTag {
    @include respond(phone) {
      padding-right: 0;
      display: flex;
      flex-direction: column;
      row-gap: 1.2rem;
    }
  }

  > div:not(:last-child) {
    min-width: 12rem;

    @include respond(phone) {
      min-width: 100%;
    }
  }

  > div:nth-child(1) {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }

  > div:nth-child(2) {
    min-width: 6rem;

    @include respond(phone) {
      min-width: auto;
    }
  }

  > div:nth-child(3) {
    min-width: 9rem;
    white-space: nowrap;

    @include respond(phone) {
      min-width: auto;
      padding-top: 1.2rem;
    }
  }

  > div:nth-child(4) {
    min-width: 11rem;

    @include respond(phone) {
      min-width: auto;
    }
  }

  > div:nth-child(7) {
    min-width: 10rem;

    @include respond(phone) {
      min-width: auto;
    }
  }
}

.tableHead {
  padding-right: 3.5rem;

  > div {
    min-width: 12rem;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;
    display: flex;
  }

  > div:nth-child(2) {
    min-width: 6rem;
  }

  > div:nth-child(3) {
    min-width: 9rem;
    white-space: nowrap;
  }

  > div:nth-child(4) {
    min-width: 11rem;
  }

  > div:nth-child(7) {
    min-width: 10rem;
  }
}

.transactiontableHeader {
  padding-right: 3.5rem;

  > div {
    min-width: 12rem;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;
    display: flex;
  }

  > div:nth-child(2) {
    min-width: 7rem;
  }

  > div:nth-child(3) {
    min-width: 9rem;
    white-space: nowrap;
  }

  > div:nth-child(4) {
    min-width: 11rem;
  }

  > div:nth-child(7) {
    min-width: 10rem;
  }
}

.transactionTableRow {
  padding-right: 3.5rem;

  > div:not(:last-child) {
    min-width: 12rem;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;
  }

  > div:nth-child(2) {
    min-width: 6rem;
  }

  > div:nth-child(3) {
    min-width: 9rem;
    white-space: nowrap;
  }

  > div:nth-child(4) {
    min-width: 11rem;
  }

  > div:nth-child(7) {
    min-width: 10rem;
  }
}

.transactionHeader {
  padding-right: 3.5rem;

  > div {
    min-width: 10.8rem;
    justify-content: flex-end;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;
  }
}

.transactionRow {
  color: $grey2;
  padding-right: 3.5rem;

  @include typography(h8);
  @include defaultTableRow;

  > div {
    min-width: 10.9rem;
    justify-content: flex-end;
    word-break: break-all;
  }

  > div:nth-child(1) {
    width: 100%;
    justify-content: flex-start;
  }
}

.companytransactionHeader {
  > div {
    justify-content: flex-start;
  }

  > div:nth-child(1) {
    width: 100%;
  }

  > div:nth-child(2) {
    min-width: 16.5rem;
  }

  > div:nth-child(3) {
    min-width: 13.5rem;
  }

  > div:nth-child(4) {
    min-width: 15rem;
  }

  > div:nth-child(5) {
    min-width: 15rem;
  }
}

.companytransactionRow {
  color: $grey2;

  @include typography(h8);
  @include defaultTableRow;

  > div {
    justify-content: flex-start;
  }

  > div:nth-child(1) {
    width: 100%;
  }

  > div:nth-child(2) {
    min-width: 16.5rem;
  }

  > div:nth-child(3) {
    min-width: 13.5rem;
  }

  > div:nth-child(4) {
    min-width: 15rem;
  }

  > div:nth-child(5) {
    min-width: 15rem;
  }
}

.headerButtons {
  display: flex;
  align-items: center;
}

.SleekCard {
  margin-top: 2rem;
  border-radius: .5rem;
  position: relative;

  @include box-shadow(0, 2px, 6px);
}

.messageClass {
  padding-left: 1rem;

  @include typography(h6);
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  cursor: pointer;
}

.transactionType {
  @include txnType($grey2);
}

.tabContainer {
  position: relative;
  display: flex;
  align-items: center;
  height: 4rem;
  background-color: $default;
  margin-top: 2rem;
  padding-left: 1.5rem;

  @include typography(h7);

  @include respond(phone) {
    margin-top: 0;
    padding-left: 0;
  }

  .switch {
    position: absolute;
    right: 1rem;
  }

  .etfTitle {
    margin-right: 1rem;
    color: $grey2;
  }

  .bondTitle {
    margin-right: 1rem;
    color: $grey2;
    margin-left: 1rem;
  }
}

.emptyState {
  flex: 1 0 0;
  margin-top: 2rem;

  @include respond(phone) {
    margin-bottom: 4rem;
  }
}

.results {
  color: $grey3;
  padding-left: .8rem;

  @include typography(h8);
}

.toggleWrapper {
  position: relative;
  top: .3rem;

  @include respond(phone) {
    top: .2rem;
  }
}

.bondToggleWrapper {
  position: relative;
  top: .3rem;
}

.toggleEnabled {
  > div:first-child {
    /* stylelint-disable-next-line declaration-no-important */
    background: $toggleBackgroundColor !important;

    &::after {
      /* stylelint-disable-next-line declaration-no-important */
      background: $yellow !important;
    }
  }
}

.displayName {
  position: relative;

  > div:first-child {
    padding-right: 3rem;
  }

  &::after {
    position: absolute;
    right: 1rem;
    content: 'ETF';
    border: solid .1rem $toggleBackgroundColor;
    border-radius: .4rem;
    padding: .2rem .4rem;
    color: $yellow;
  }
}

.stockControls {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 3rem;

  > div:first-child {
    position: relative;
    margin-bottom: 2rem;

    > button:first-child {
      position: absolute;
      top: 55%;
      right: 50%;
    }

    > button:nth-child(2) {
      position: absolute;
      top: 55%;
    }
  }

  .moreOptions {
    right: 5%;
  }
}

.caTag {
  border-radius: .4rem;
  background-color: $caTagBackgroundColor;
  margin: .5rem 0 0 1rem;
  padding: .1rem .5rem;
  color: $yellow;
  z-index: 2;
  height: 1.5rem;
  margin-right: .5rem;

  @include typography(h9, semibold);
}

.utilizedQuantity {
  color: $grey2;
  display: flex;
  justify-content: flex-end;

  > span {
    margin-left: 1rem;
  }
}

.etf {
  margin-left: 1rem;
  border: solid .1rem $toggleBackgroundColor;
  border-radius: .4rem;
  padding: .2rem .4rem;
  color: $yellow;

  @include respond(phone) {
    height: fit-content;

    @include typography(h8);
  }
}

.bond {
  margin-left: 1rem;
  border: solid .1rem $toggleBackgroundColor;
  border-radius: .4rem;
  padding: .2rem .4rem;
  color: $yellow;

  @include respond(phone) {
    height: fit-content;

    @include typography(h8);
  }
}

.scripts {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.rowContent {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.displayNameText {
  text-overflow: ellipsis;
  margin-bottom: .2rem;

  @include respond(phone) {
    margin-bottom: 0;

    @include typography(h6, semibold);
  }
}

.currentValue {
  color: $grey0;
  white-space: nowrap;
  margin-bottom: .2rem;

  @include typography(h6, upperbold);

  @include respond(phone) {
    margin-bottom: 0;
  }
}

.greenBorder {
  @include respond(phone) {
    > div:nth-child(3) {
      border-left: .3rem solid $green;
      padding-left: .5rem;
      margin-left: 1rem;
    }
  }

  > div:first-child {
    border-left: .3rem solid $green;
    padding-left: .5rem;
    margin-left: 1rem;
  }
}

.redBorder {
  @include respond(phone) {
    > div:nth-child(3) {
      border-left: .3rem solid $red;
      padding-left: .5rem;
      margin-left: 1rem;
    }
  }

  > div:first-child {
    border-left: .3rem solid $red;
    padding-left: .5rem;
    margin-left: 1rem;
  }
}

.qty {
  color: $grey3;
  white-space: nowrap;

  @include typography(h8);

  @include respond(phone) {
    @include typography(h7);
  }
}

.flex {
  display: flex;
  align-items: center;
}

.holdingRow {
  padding-top: 1.2rem;
  padding-bottom: 1.2rem;
  height: auto;
}

.basePL > div {
  @include typography(h7);
}

.containerHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.linkWrapper {
  display: flex;
  align-items: center;
  border-top: .1rem solid $grey4;
  background-color: $default;
  height: 4rem;
  padding-left: 1.5rem;
  border-radius: 0 0 4px 4px;
}

.linkText {
  display: flex;
  color: $secondary-hyper-link;
  text-decoration: none;

  @include typography(h8, semibold);

  > span {
    margin-right: .5rem;
  }
}

.beta {
  margin-top: -.6rem;
  margin-right: -1rem;
  color: $yellow;
  padding: 0 .2rem;
  height: 1rem;
  background-color: $betaBackground;

  @include typography(h9, bold);
}

.pnlTag {
  display: flex;
}
