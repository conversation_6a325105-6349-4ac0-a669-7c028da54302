import React, {
  useState, useMemo, useCallback, useEffect,
} from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import usePlaceOrder from '@common/usePlaceOrder';
import {
  Change, ChangeWithPercent, Percent,
} from '@common/Prices';
import Search from '@common/Search';
import StockControls from '@common/StockControls';
import CategoryFilter from '@common/CategoryFilter';
import Switch from '@common/Switch';
import Lottie from 'lottie-react';
import TableHeader from '@common/Table/Header';
import { getExchangeFromCode } from '@pages/Home/partials/MarketIndices/api';
import {
  PRODUCT_TYPES,
  ROUTE_NAME,
  HOLDINGS_SORT,
  INSTRUMENTS,
  EDIS_BULK_AUTH_KEY,
} from '@utils/enum';
import DropdownOptions from '@modules/Positions/common/DropdownOptions';
import If from '@common/If';
import { TYPE } from '@common/SkeletonLoader/enums';
import isEmpty from 'lodash/isEmpty';
import SkeletonLoader from '@common/SkeletonLoader';
import WithLoader from '@common/WithLoader';
import EmptyState from '@common/EmptyState';
import { ICON_NAME } from '@common/Icon/enums';
import ExpandableTableRow from '@common/ExpandableTableRow';
import { useModal } from '@layout/App/ModalContext';
import { MODAL_TYPES } from '@common/Modal/enums';
import LocalStorage from '@service/LocalStorage';
import { classNames as cx } from '@utils/style';
import Icon from '@common/Icon';
import ButtonTablist from '@common/Tabs/ButtonTablist';
import useCorporateActionData from '@common/useCorporateActionData';
import FloatingSortControl from '@common/Table/FloatingSortControl';
import { sendEvent } from '@service/AnalyticsService';
import {
  mobileBrowser,
  formatPrice,
  getUrlParameter,
  setQueryParam,
  mapQueryString,
  isBondInstrument,
  sanitizeHtml,
} from '@utils';
import { table } from '@commonStyles';
import HoldingsAuthorization from '../HoldingsAuthorization';
import EventAnimation from './lottie/Event_ActionIcon.json';
import SleekCardAnalytics from './partials/SleekCardAnalytics';
import Routes from '../../../../../routes';
import CaTagsSidebar from '../CaTagsSidebar';
// import PortfolioInsights from '../Insights';
import { category, initialFilters, WEALTHDESK } from '../../enums';
import { getArrayIntersection } from '../../utils';
import useWealthdeskData from './useWealthdeskData';
import PortfolioAnalytics from '../PortfolioAnalytics';
import useFilterCategory from './useFilterCategory';

import styles from './styles';

import {
  getHeaderOptions,
  RETURNS_TYPE,
  switchOptions,
  getReturnsFields,
  MORE_OPTIONS,
  scopeList,
  SCOPE,
  mobileSortOptions,
  DEFAULT_PORTFOLIO_SORT,
} from './config';
import Transactions from './Transactions';

export const PortfolioRow = ({
  pml_id,
  display_name,
  remaining_quantity,
  utilized_quantity,
  cost_price,
  current_price,
  current_value,
  security_id,
  exchange_code,
  tick_size,
  sibling_security_id,
  sibling_tick_size,
  isin_code,
  exchange_inst_name,
  context: { returnsType },
  index,
  onClick,
  segment,
  symbol,
  lot_size,
  quantity,
  searchText,
  ...restProps
}) => {
  const { openModal } = useModal();
  const { caData } = useCorporateActionData({ isin: isin_code, segment, exchange: getExchangeFromCode(exchange_code) });
  const showCaTagsModal = () => openModal({
    type: MODAL_TYPES.SIDEPANE,
    Component: CaTagsSidebar,
    componentProps: { caData },
  });
  const returnFields = getReturnsFields(returnsType);
  const { buy, sell } = usePlaceOrder(
    display_name,
    getExchangeFromCode(exchange_code),
    security_id,
    {
      isin: isin_code,
      tickSize: tick_size / 100,
      siblingSecurityId: sibling_security_id,
      siblingTickSize: sibling_tick_size ? sibling_tick_size / 100 : 0,
      productType: PRODUCT_TYPES.DELIVERY,
      forceExchangeSelect: true,
      showProfitLoss: true,
      instrument_Id: pml_id,
      instrumentType: exchange_inst_name,
      exch_symbol: symbol,
    },
  );

  const isEtf = exchange_inst_name === INSTRUMENTS.ETF;
  const isBond = isBondInstrument(exchange_inst_name);

  const boldQuery = (str, query) => {
    const n = str.toUpperCase();
    const q = query.toUpperCase();
    const x = n.indexOf(q);
    if (!q || x === -1) {
      return str;
    }
    const l = q.length;
    return `${str.substr(0, x)}<b>${str.substr(x, l)}</b>${str.substr(x + l)}`;
  };

  const getRowContent = () => (
    <div className={styles.borderTag}>
      <div className={styles.rowContent}>
        <div
          className={styles.displayNameText}
          dangerouslySetInnerHTML={{
            __html: searchText.length
              ? sanitizeHtml(boldQuery(display_name, searchText)) : display_name,
          }}
        />

        <div className={styles.currentValue}>{`₹ ${formatPrice(current_value)}`}</div>

      </div>

      <div className={styles.rowContent}>
        <div className={styles.flex}>
          <div className={styles.qty}>
            {`${remaining_quantity} x ${formatPrice(cost_price)}`}
          </div>
          <If test={isEtf && !caData.length}>
            <span className={styles.etf}>ETF</span>
          </If>
          <If test={isBond && !caData.length}>
            <span className={styles.bond}>Bond</span>
          </If>
        </div>

        <ChangeWithPercent
          value={restProps[returnFields.value]}
          percent={restProps[returnFields.percent]}
          className={styles.basePL}
        />
      </div>
    </div>
  );

  if (mobileBrowser()) {
    return (
      <div
        className={cx([styles.tableRow, table.plainTableRow, styles.holdingRow], {
          [styles.redBorder]: restProps[returnFields.value] < 0,
          [styles.greenBorder]: restProps[returnFields.value] > 0,
        })}
        role="presentation"
        onClick={onClick}
      >
        {getRowContent()}
      </div>
    );
  }

  const handleBuy = () => {
    buy();
    sendEvent({
      event_category: 'order',
      event_action: 'order_entry_from_portfolio_buy',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
    sendEvent({
      event_category: 'order',
      event_action: 'order_entry_from_portfolio',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
  };
  const handleSell = () => {
    sell({ initialQuantity: remaining_quantity });
    sendEvent({
      event_category: 'order',
      event_action: 'order_entry_from_portfolio_sell',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
    sendEvent({
      event_category: 'order',
      event_action: 'order_entry_from_portfolio',
      event_label: 'orders',
      vertical_name: 'stocks',
      screenName: '/stocks_orders',
    });
  };

  return (
    <>
      <div
        className={cx(table.rowControlsHolder)}
        role="presentation"
        data-testid="portfolioRow"
        onClick={onClick}
      >
        <div
          className={styles.displayNameText}
          dangerouslySetInnerHTML={{
            __html: searchText.length
              ? sanitizeHtml(boldQuery(display_name, searchText)) : display_name,
          }}
        />
        <div className={styles.scripts}>
          <div className={styles.stockControls}>
            <StockControls
              buy={handleBuy}
              sell={handleSell}
            />
            <If test={caData.length}>
              <span
                role="presentation"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  showCaTagsModal();
                }}
              >
                <Lottie animationData={EventAnimation} style={{ marginLeft: '1rem', marginTop: '0.5rem' }} />
              </span>
            </If>
            {utilized_quantity > 0 ? (
              <span className={styles.utilizedQuantity}>
                <Icon name={ICON_NAME.SELLING_BRIEFCASE} />
                <span>{`${utilized_quantity}`}</span>
              </span>
            ) : null}
            <If test={isEtf && !caData.length}>
              <span className={styles.etf}>ETF</span>
            </If>
            <If test={isBond && !caData.length}>
              <span className={styles.bond}>Bond</span>
            </If>
            <div
              role="presentation"
              className={cx([table.rowControls])}
            >
              {!isBond
                ? (
                  <DropdownOptions
                    options={MORE_OPTIONS}
                    pmlId={pml_id}
                    instrumentType={exchange_inst_name}
                    segment={segment}
                    tick_size={tick_size}
                    security_id={security_id}
                    displayName={display_name}
                    lot_size={quantity || lot_size}
                    isin={isin_code}
                    exchange={getExchangeFromCode(exchange_code)}
                  />
                ) : null}
            </div>
          </div>
        </div>
      </div>
      <div role="presentation" onClick={onClick}>{remaining_quantity}</div>
      <div role="presentation" onClick={onClick}>{formatPrice(cost_price)}</div>
      <div role="presentation" onClick={onClick}>{formatPrice(current_price)}</div>
      <div role="presentation" onClick={onClick}>{`₹ ${formatPrice(current_value)}`}</div>
      <div role="presentation" onClick={onClick}>
        <Change value={restProps[returnFields.value]} withRupee withSign />
      </div>
      <div role="presentation" onClick={onClick}>
        <Percent value={restProps[returnFields.percent]} withSign />
      </div>
    </>
  );
};

function Rows({
  tableData, tableContext, setActiveRow, activeIndex, segment, searchText, wealthdeskMismatchMessage,
}) {
  const history = useHistory();
  const onClick = useCallback((pml_id, instrument_type) => {
    const isBond = isBondInstrument(instrument_type);

    if (isBond) {
      history.push(`${Routes[[ROUTE_NAME[instrument_type]]].url}/${pml_id}`);
    } else {
      history.push(`${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${pml_id}`);
    }
  }, [history]);
  const [showWealthdeskMessage, setShowWealthdeskMessage] = useState(true);
  const noteMessage = {
    note:
  <>
    <b>Note: </b>
    {wealthdeskMismatchMessage}
  </>,
  };

  return (
    <div className={styles.rowContainer}>
      <If test={wealthdeskMismatchMessage && tableData.length && showWealthdeskMessage}>
        <div className={styles.wealthdesk}>
          <div>
            {noteMessage.note}
          </div>
          <span role="presentation" onClick={() => setShowWealthdeskMessage(false)}>
            <Icon name={ICON_NAME.CLOSE} size={1.5} className={styles.closeIcon} />
          </span>
        </div>
      </If>
      {
        mobileBrowser() ? tableData.map((item, key) => (
          <PortfolioRow
            key={item.isin_code}
            segment={segment}
            symbol={item.bse_symbol || item.nse_symbol}
            index={key}
            context={tableContext}
            searchText={searchText}
            {...item}
            onClick={onClick.bind(null, item.pml_id)}
          />
        )) : tableData.map((item, key) => (
          <ExpandableTableRow
            toggleFunction={setActiveRow.bind(null, activeIndex === key ? null : key)}
            className={styles.tableRow}
            headerComponent={(
              <PortfolioRow
                key={item.isin_code}
                searchText={searchText}
                segment={segment}
                symbol={item.bse_symbol || item.nse_symbol}
                index={key}
                context={tableContext}
                {...item}
                onClick={onClick.bind(null, item.pml_id, item.exchange_inst_name)}
              />
            )}
            isActiveRow={key === activeIndex}
            key={key}
            childComponent={(
              <Transactions isin={item.isin_code} current_price={item.current_price} />
            )}
          />
        ))
      }
    </div>
  );
}

const LoadingComponent = () => (
  <SkeletonLoader
    type={mobileBrowser() ? TYPE.WIDGET : TYPE.TABLE}
    rows={5}
    columns={7}
    className={styles.tableRow}
  />
);

const RowsHOC = WithLoader({ WrappedComponent: Rows, LoadingComponent });

const PortfolioTable = ({
  tableData, activeSort, handleSorting, inProgress, failed, segment,
}) => {
  const defaultScope = () => {
    if (window.location.href.includes('analytics')) {
      return scopeList[1].id;
    }
    // if (window.location.href.includes('insights')) {
    //   return scopeList[2].id;
    // }
    return scopeList[0].id;
  };
  const { pathname, search } = useLocation();
  const history = useHistory();
  const returnsType = decodeURIComponent(getUrlParameter('returns', search)) || RETURNS_TYPE.OVERALL;
  const headerOptions = useMemo(() => getHeaderOptions(returnsType), [returnsType]);
  const tableContext = useMemo(() => ({ returnsType }), [returnsType]);
  const [activeIndex, setActiveRow] = useState();
  const [selectedScope, setSelectedScope] = useState(defaultScope());
  const [refresh, setRefresh] = useState(false);
  const [holdingsAuthorisationData, setHoldingsAuthorisationData] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [holdingTableData, setHoldingTableData] = useState(tableData);
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
  const [isMounted, setMounted] = useState(false);
  const [listedItems, setListedItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filterList, setFilterList] = useState(initialFilters);
  const [filterData, setFilterData] = useState(tableData);
  const sectors = useMemo(() => tableData.map((item) => item.sector), [tableData]);
  const [wealthdeskMismatchMessage, setWealthdeskMismatchMessage] = useState(null);
  const [clonedActiveSort, setClonedActiveSort] = useState(activeSort);
  const {
    wealthDeskData, showDowntime, wealthdeskMismatch, updatedTableData,
  } = useWealthdeskData({ tableData });
  const [showDowntimeMessage, setShowDowntimeMessage] = useState(false);
  const { filterCategory, sectorWiseData } = useFilterCategory({ tableData, wealthDeskData });

  useEffect(() => {
    if (!searchText && ![].concat(...Object.values(filterList)).length) {
      setHoldingTableData(tableData);
    } else {
      const tempHoldings = [];
      setHoldingTableData((prevState) => {
        tableData.forEach((item) => {
          const temp = prevState.filter((data) => data.isin_code === item.isin_code);
          if (temp.length) {
            tempHoldings.push(item);
          }
        });
        return tempHoldings;
      });
    }
    if ((clonedActiveSort !== activeSort)) {
      setClonedActiveSort(activeSort);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableData, activeSort, clonedActiveSort]);

  useEffect(() => {
    if (wealthdeskMismatch) {
      setWealthdeskMismatchMessage(WEALTHDESK.MESSAGE);
    }
  }, [wealthdeskMismatch]);

  useEffect(() => {
    setShowDowntimeMessage(showDowntime);
  }, [showDowntime]);

  useEffect(() => {
    if (holdingTableData && holdingTableData.length) {
      const holdingsForAuth = holdingTableData.filter((data) => data.remaining_quantity > 0);
      const formattedHoldings = tableData.filter((item) => item.remaining_quantity > 0).map((holding) => ({
        isin: holding?.isin_code,
        name: holding?.display_name,
        qty: holding?.remaining_quantity,
      }));
      const edisBulkAuthPayload = {
        trade_type: 'PRE',
        isin_list: formattedHoldings,
      };
      LocalStorage.set(EDIS_BULK_AUTH_KEY, edisBulkAuthPayload, '', false);
      if (!isMounted && holdingTableData[0]?.current_value && holdingTableData[0]?.current_value !== 0.00) {
        LocalStorage.set(HOLDINGS_SORT, LocalStorage.get(HOLDINGS_SORT, false) || DEFAULT_PORTFOLIO_SORT, '', false);
        handleSorting(LocalStorage.get(HOLDINGS_SORT, false));
        setMounted(true);
      }
      if (holdingsForAuth.length) {
        setHoldingsAuthorisationData(holdingsForAuth);
      } else {
        setHoldingsAuthorisationData(null);
      }
    } else {
      setHoldingsAuthorisationData(null);
    }
  }, [holdingTableData, isMounted, handleSorting, tableData]);

  const updateScope = (scope) => {
    if (scope === SCOPE.ANALYTICS) {
      sendEvent({
        event_category: 'equity_portfolio',
        event_action: 'portfolioanalytics_clicked',
        event_label: 'portfolio',
        vertical_name: 'stocks',
        screenName: '/portfolioinsights',
      });
      history.replace('/portfolio/analytics');
    // } else if (scope === SCOPE.INSIGHTS) {
    //   history.replace('/portfolio/insights');
    } else if (scope === SCOPE.PNL_TRACKING) {
      history.replace('/p&l');
    } else {
      history.replace('/portfolio');
    }
    setSelectedScope(scope);
  };

  const handleReturnsTypeChange = useCallback(
    (nextReturnsType) => {
      if (returnsType === nextReturnsType) {
        return;
      }
      const currReturnFields = getReturnsFields(returnsType);
      const queryString = search.substring(1);
      const queryParams = mapQueryString(queryString);
      setQueryParam({
        query: {
          ...queryParams,
          ...{ returns: encodeURIComponent(nextReturnsType) },
        },
        pathname,
        replace: history.push,
      });
      if (activeSort.id !== currReturnFields.value && activeSort.id !== currReturnFields.percent) {
        return;
      }
      const nextReturnFields = getReturnsFields(nextReturnsType);
      const nextSortOption = {
        sortingOrder: activeSort.sortingOrder,
        id: activeSort.id === currReturnFields.value ? nextReturnFields.value : nextReturnFields.percent,
      };
      handleSorting(nextSortOption);
    },
    [returnsType, activeSort, handleSorting, search, history, pathname],
  );

  const searchDataForFilters = useCallback((data) => {
    let searchedData = [];
    if (searchText.length) {
      searchedData = data.filter((item) => item.display_name.toLowerCase()
        .includes(searchText.toLowerCase()));
      setHoldingTableData(searchedData);
    }
  }, [searchText]);

  const clearSearchText = useCallback(() => {
    setHoldingTableData(filterData);
    setSearchText('');
  }, [filterData]);

  const handleSearch = useCallback((e) => {
    sendEvent({
      event_category: 'equity_portfolio',
      event_action: 'holdingsearch_clicked',
      event_label: 'portfolio',
      vertical_name: 'stocks',
      screenName: '/my_portfolio',
    });
    setSearchText(e.target.value);
    if (e.target.value === '') {
      if ([].concat(...Object.values(filterList)).length) {
        setHoldingTableData(filterData);
      } else {
        setHoldingTableData(tableData);
      }
    }
  }, [filterData, filterList, tableData]);

  useEffect(() => {
    if (searchText.length) {
      searchDataForFilters([].concat(...Object.values(filterList)).length ? filterData : tableData);
    }
  }, [tableData, searchDataForFilters, filterList, filterData, searchText.length]);

  useEffect(() => {
    const filters = [];
    const checkArray = ['profitLoss', 'instrument'];
    const checkArrayForCount = ['inProfit', 'inLoss', 'neutral'];
    category.forEach((element) => filters.push({
      ...element,
      data: element.data.filter((ele) => {
        const values = filterCategory[element.labelId];
        return values[ele.id].length > 0 || checkArrayForCount.includes(ele.id);
      }).map((item) => {
        const newItem = { ...item };
        newItem.value = checkArrayForCount.includes(item.id)
          ? newItem.value
          : `${newItem.value} (${filterCategory[element.labelId][item.id].length})`;
        return newItem;
      }),
    }));
    filters.forEach((element, index) => {
      if (!element.data.length) {
        filters.splice(index, 1);
      }
      if (checkArray.includes(element.labelId) && element.data.length === 1) {
        filters.splice(index, 1);
      }
      if (!(checkArray.includes(element.labelId)) && element.data.length === 1) {
        filters[index].disabled = true;
      }
    });
    setCategories(filters);
  }, [filterCategory]);

  const handleSelectedFilters = useCallback((activeTab) => {
    const filteredData = [];
    const newTableData = [].concat(...Object.values(activeTab)).includes('wealthdesk') ? updatedTableData : tableData;
    Object.keys(activeTab).forEach((key) => {
      let arr = [];
      activeTab[key].forEach((ele) => {
        const arrVals = filterCategory[key][ele];
        arr = [...new Set([...arr, ...arrVals])];
      });
      if (arr.length) filteredData.push(arr);
    });
    const filteredHoldings = filteredData.length
      ? newTableData.filter((item) => getArrayIntersection(filteredData).includes(item.isin_code)) : [];
    setFilterList(activeTab);
    const data = [].concat(...Object.values(activeTab)).length ? filteredHoldings : tableData;
    setHoldingTableData(data);
    setFilterData(data);
    searchDataForFilters(data);
  }, [filterCategory, searchDataForFilters, tableData, updatedTableData]);

  const resetFilter = useCallback(() => {
    setHoldingTableData(tableData);
    setFilterList(initialFilters);
    searchDataForFilters(tableData);
  }, [searchDataForFilters, tableData]);

  useEffect(() => {
    const list = [];
    const uniqueSectors = sectors.filter((item,
      index) => sectors.indexOf(item) === index);
    uniqueSectors.forEach((sectorName) => list.push({
      name: `${sectorName} (${sectorWiseData[sectorName].length})`,
      labelId: 'sector',
      id: sectorName,
      checked: false,
    }));
    setListedItems(list);
  }, [sectorWiseData, sectors, tableData]);

  // const portfolioInsights = useMemo(() => (
  //   <PortfolioInsights />
  // ), []);

  const handleFilter = () => {
    sendEvent({
      event_category: 'equity_portfolio',
      event_action: 'holdingfilter_clicked',
      event_label: 'portfolio',
      vertical_name: 'stocks',
      screenName: '/my_portfolio',
    });
  };

  const showCategoryFilter = () => (
    <CategoryFilter
      category={categories}
      handleSelectedFilters={handleSelectedFilters}
      filterList={filterList}
      listedItemTitle="Sector"
      showDowntimeMessage={showDowntimeMessage}
      setShowDowntimeMessage={setShowDowntimeMessage}
      listedItems={listedItems}
      resetFilter={resetFilter}
      initialFilters={initialFilters}
      setIsFilterOpen={handleFilter}
    />
  );

  return (
    <div>
      <div className={styles.containerHeader}>
        <If test={!mobileBrowser()}>
          <ButtonTablist
            options={scopeList}
            activeTab={selectedScope}
            setTab={updateScope}
          />
        </If>
        <If test={holdingsAuthorisationData && selectedScope == SCOPE.HOLDINGS && !mobileBrowser()}>
          <HoldingsAuthorization holdings={holdingsAuthorisationData} />
        </If>
      </div>
      <If test={selectedScope == SCOPE.ANALYTICS && !isEmpty(tableData)}>
        <SleekCardAnalytics onRefresh={() => setRefresh(!refresh)} />
      </If>
      <If test={!inProgress && isEmpty(tableData) && selectedScope == SCOPE.HOLDINGS}>
        <EmptyState
          text="You don't have any holdings yet"
          subText="Buy stocks to get started"
          ctaText="Buy Stock"
          iconName={ICON_NAME.EMPTY_POSITIONS}
          className={styles.emptyState}
        />
      </If>
      {/* <If test={!inProgress && isEmpty(tableData) && selectedScope == SCOPE.INSIGHTS}>
        <EmptyState
          text="No stocks in your portfolio right now :("
          subText="Start investing to see portfolio insights"
          ctaText="Place Order"
          iconName={ICON_NAME.EMPTY_ANALYTICS}
          className={styles.emptyState}
        />
      </If> */}
      <If test={!inProgress && isEmpty(tableData) && selectedScope == SCOPE.ANALYTICS}>
        <EmptyState
          text="No stocks in your portfolio right now :("
          subText="Start investing to see portfolio analytics"
          ctaText="Place Order"
          iconName={ICON_NAME.EMPTY_ANALYTICS}
          className={styles.emptyState}
        />
      </If>
      <If test={selectedScope == SCOPE.ANALYTICS && !isEmpty(tableData)}>
        <PortfolioAnalytics toRefresh={refresh} />
      </If>
      {/* <If test={selectedScope === SCOPE.INSIGHTS && !mobileBrowser() && !isEmpty(tableData)}>
        {portfolioInsights}
      </If> */}
      <If test={selectedScope == SCOPE.HOLDINGS && !isEmpty(tableData)}>
        <div className={styles.tabContainer}>
          <If test={!isMobileSearchOpen && mobileBrowser()}>
            <div role="presentation" onClick={() => setIsMobileSearchOpen(true)}>
              <Icon name={ICON_NAME.SEARCH} className={styles.searchIcon} size={3} />
            </div>
          </If>
          <If test={isMobileSearchOpen && mobileBrowser()}>
            <div role="presentation" onClick={() => setIsMobileSearchOpen(false)}>
              <Icon name={ICON_NAME.BACK_ARROW} className={styles.searchIcon} size={3} />
            </div>
          </If>
          <If test={!mobileBrowser()}>{showCategoryFilter()}</If>
          <If test={isMobileSearchOpen || !mobileBrowser()}>
            <div className={styles.filterHeader}>
              <Search
                placeholder="Search in your holdings"
                handleOnSearch={handleSearch}
                searchQuery={searchText}
                clearText={clearSearchText}
                showSearchButton={false}
              />
            </div>
          </If>
          <If test={mobileBrowser()}>{showCategoryFilter()}</If>
          <If test={!isMobileSearchOpen}>
            <Switch
              options={switchOptions}
              active={returnsType}
              setSwitch={handleReturnsTypeChange}
              containerClassName={styles.switch}
            />
          </If>
        </div>
        <div>
          <If test={!mobileBrowser()}>
            <TableHeader
              className={styles.tableHead}
              options={headerOptions}
              active={activeSort}
              onClick={(selectedTab) => {
                LocalStorage.set(HOLDINGS_SORT, selectedTab, '', false);
                handleSorting(selectedTab);
              }}
            />
          </If>
          <If test={!holdingTableData.length && (searchText.length || [].concat(...Object.values(filterList)).length)}>
            <EmptyState
              text={`${[].concat(...Object.values(filterList)).length ? 'No result found in the filtered holdings'
                : 'No result found'}`}
              subText={`${[].concat(...Object.values(filterList)).length
                ? 'Try changing or removing the filter, or your search selection'
                : 'Try changing your search selection'}`}
              iconName={ICON_NAME.EMPTY_SEARCH}
              className={styles.emptyState}
            />
          </If>
          <RowsHOC
            tableData={holdingTableData}
            tableContext={tableContext}
            segment={segment}
            inProgress={inProgress}
            failed={failed}
            activeIndex={activeIndex}
            setActiveRow={setActiveRow}
            searchText={searchText}
            wealthdeskMismatchMessage={wealthdeskMismatchMessage}
          />
          <If test={mobileBrowser()}>
            <FloatingSortControl
              options={mobileSortOptions}
              onAction={(selectedTab) => {
                LocalStorage.set(HOLDINGS_SORT, selectedTab, '', false);
                handleSorting(selectedTab);
              }}
            />
          </If>
        </div>
      </If>
    </div>
  );
};

export default PortfolioTable;
