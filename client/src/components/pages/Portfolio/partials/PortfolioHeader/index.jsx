import React from 'react';
import { Change, ChangeWithPercent, ChangeIcon } from '@common/Prices';
import Header from '@common/Header';
import { useLocation } from 'react-router-dom';
import { mobileBrowser, getUrlParameter } from '@utils';
import MarginPledgeCta from './partials/MarginPledgeCta';
import styles from './index.scss';
import { STATICS } from '../../enums';
import { RETURNS_TYPE } from '../PortfolioTable/config';

const getReturnsValue = (returnsValue, returnsPercent) => (
  <div className={styles.returnsContainer}>
    <div className={styles.valueContainer}>
      <ChangeWithPercent
        value={returnsValue}
        percent={returnsPercent}
        withRupee
        alignPercentCenter
        className={styles.changeWithPercentClass}
      />
    </div>
    <ChangeIcon className={styles.arrow} value={returnsValue} />
  </div>
);

const PortfolioHeader = ({
  total_investment,
  current_value,
  today_returns,
  today_returns_percent,
  overall_returns,
  overall_returns_percent,
  inProgress,
}) => {
  const { search } = useLocation();

  const dataToRender = [{
    label: STATICS.TOTAL_INVESTMENT,
    id: STATICS.TOTAL_INVESTMENT,
    value: <Change value={total_investment} withRupee className={styles.value} />,
  }, {
    label: STATICS.CURRENT_VALUE,
    id: STATICS.CURRENT_VALUE,
    value: <Change value={current_value} withRupee className={styles.value} />,
  }, {
    label: STATICS.OVERALL_RETURNS,
    id: STATICS.OVERALL_RETURNS,
    value: getReturnsValue(overall_returns, overall_returns_percent),
  }, {
    label: STATICS.TODAY_RETURNS,
    id: STATICS.TODAY_RETURNS,
    value: getReturnsValue(today_returns, today_returns_percent),
  }];

  if (mobileBrowser()) {
    const returnsType = decodeURIComponent(getUrlParameter('returns', search)) || RETURNS_TYPE.OVERALL;
    const { label, value } = dataToRender.find((item) => item.id.toLowerCase().includes(returnsType));

    return (
      <div className={styles.mobile}>
        <div>
          <div className={styles.label}>{label}</div>
          {value}
        </div>
        <div className={styles.row}>
          <div>
            <div className={styles.label}>Total Investment</div>
            {dataToRender.find((item) => item.id === STATICS.TOTAL_INVESTMENT).value}
          </div>
          <div>
            <div className={styles.label}>Current Value</div>
            {dataToRender.find((item) => item.id === STATICS.CURRENT_VALUE).value}
          </div>
        </div>
      </div>
    );
  }
  return (
    <Header
      showStatus
      header={STATICS.PORTFOLIO_VALUE}
      options={dataToRender}
      inProgress={inProgress}
      customComponent={<MarginPledgeCta />}
    />
  );
};

export default PortfolioHeader;
