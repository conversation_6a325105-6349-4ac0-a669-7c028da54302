import React, { useContext, useCallback, useEffect } from 'react';
import { HoldingsContext } from '@modules/Holdings';
import { useContextBackgroundUpdate } from '@layout/App/contextBackgroundUpdate';
import { INFO_CARD } from '@utils/enum';
import { OffMarketContext } from '@layout/Portfolio/OffMarketContext';
import If from '@common/If';
import useDowntimeConfig from '@common/useDowntimeConfig';
import EmptyState from '@common/EmptyState';
import {
  useLocation,
} from 'react-router-dom';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import EdisCollection from '@pages/Home/partials/EdisCollection';
import EdisCollectionMobile from '@pages/Home/partials/EdisCollection/EdisCollectionMobile';
import { DraggableModalContext } from '@common/usePlaceOrder';
import { MODAL_TYPES } from '@common/Modal/enums';
import { useToast } from '@common/Toast';
import { EDIS_AUTHORISATION_KEY } from '@pages/Home/partials/EdisCollection/enum';
import CDSLBulkAuthorisation from '@modules/OrderFlow/CDSLBulkAuthorisation';
import { useModal } from '@layout/App/ModalContext';
import { useHistory } from 'react-router';
import { mobileBrowser, getUrlParameter } from 'utils';
import PortfolioHeader from './partials/PortfolioHeader';
import PortfolioTable from './partials/PortfolioTable';
import styles from './index.scss';
import InfoCard from './partials/InfoCard';
import OffmarketNotification from './partials/OffmarketNotification';
import { FAILURE_TOAST_MESSAGE, EDIS_COLLECTION_PARAMETER, DOWNTIME } from './enums';

function PortfolioPage() {
  const {
    sortedHoldings,
    overview,
    activeSort,
    handleSorting,
    apiResponse,
    inProgress,
    failed,
  } = useContextBackgroundUpdate(HoldingsContext);
  const { configJson: { holdings_downtime }, fetchInProgress, getDowntimeConfig } = useDowntimeConfig();

  const history = useHistory();

  const { holdingsWithMismatch } = useContext(OffMarketContext);

  const { pathname, search } = useLocation();
  const { openDraggableModal, handleClose } = useContext(DraggableModalContext);

  const { addToast } = useToast();

  const isEdisAuthCollection = pathname.includes(EDIS_AUTHORISATION_KEY);

  const { openModal, closeModal } = useModal();

  const edisBulkAuthModal = useCallback(() => {
    openModal({
      Component: CDSLBulkAuthorisation,
      componentProps: { closeModal },
      type: mobileBrowser() ? MODAL_TYPES.BOTTOM_SHEET : MODAL_TYPES.LARGE_POPUP,
    });
  }, [closeModal, openModal]);

  const getEdisParameterFromUrl = useCallback(() => {
    const collectionId = getUrlParameter(EDIS_COLLECTION_PARAMETER.COLLECTION_ID, search);
    const qty = getUrlParameter(EDIS_COLLECTION_PARAMETER.QTY, search);
    const purpose = getUrlParameter(EDIS_COLLECTION_PARAMETER.PURPOSE, search);
    const scriptName = getUrlParameter(EDIS_COLLECTION_PARAMETER.SCRIPT_NAME, search);
    const isin = getUrlParameter(EDIS_COLLECTION_PARAMETER.ISIN, search);

    return {
      collectionId, qty, purpose, scriptName, isin,
    };
  }, [search]);

  useEffect(() => {
    if (isEdisAuthCollection && !mobileBrowser()) {
      const {
        collectionId, qty, purpose, scriptName, isin,
      } = getEdisParameterFromUrl();

      if (isin && collectionId && qty) {
        openDraggableModal({
          type: MODAL_TYPES.POPUP,
          Component: EdisCollection,
          componentProps: {
            collectionId, qty, purpose, scriptName, isin, handleClose, edisBulkAuthModal,
          },
        });
      } else {
        addToast(FAILURE_TOAST_MESSAGE, APPEARANCE_TYPES.FAIL);
      }
    }
  }, [addToast, edisBulkAuthModal, getEdisParameterFromUrl, handleClose, isEdisAuthCollection, openDraggableModal]);

  const retryDowntime = (e) => {
    e.preventDefault();
    getDowntimeConfig();
  };

  if (holdings_downtime?.equity_holdings_downtime) {
    return (
      <>
        <div className={`${styles.downTimeHeader}`}>{DOWNTIME.TITLE}</div>
        <EmptyState
          text={holdings_downtime?.equity_holdings_heading}
          subText={holdings_downtime?.equity_holdings_message}
          ctaText={DOWNTIME.REFRESH}
          customIconImg={holdings_downtime?.equity_holdings_img}
          className={styles.portFolioDownTime}
          onCtaClick={retryDowntime}
        />
      </>
    );
  }

  if (isEdisAuthCollection && mobileBrowser()) {
    const {
      collectionId, qty, purpose, scriptName, isin,
    } = getEdisParameterFromUrl();
    if (collectionId && qty && purpose && scriptName && isin) {
      return (
        <EdisCollectionMobile
          collectionId={collectionId}
          qty={qty}
          purpose={purpose}
          scriptName={scriptName}
          isin={isin}
          edisBulkAuthModal={edisBulkAuthModal}
        />
      );
    }
    history.push('./');
    addToast(FAILURE_TOAST_MESSAGE, APPEARANCE_TYPES.FAIL);
  }

  return (
    <div>
      <PortfolioHeader
        {...overview}
        inProgress={inProgress || fetchInProgress}
        failed={failed}
      />
      <InfoCard
        infoCardObject={INFO_CARD.HOLDINGS}
        className={styles.SleekCard}
        messageClass={styles.messageClass}
        closeIconClass={styles.close}
      />
      <If test={holdingsWithMismatch.length > 0 && !mobileBrowser()}>
        <OffmarketNotification />
      </If>
      <If test={!inProgress}>
        <div className={styles.tableContainer}>
          <PortfolioTable
            tableData={sortedHoldings}
            segment={apiResponse[0]?.segment}
            activeSort={activeSort}
            handleSorting={handleSorting}
            inProgress={inProgress || fetchInProgress}
            failed={failed}
          />
        </div>
      </If>
    </div>
  );
}

export default PortfolioPage;
