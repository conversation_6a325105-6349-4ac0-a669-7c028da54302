import React, { useState, useEffect, useContext } from 'react';
import { useIrData } from '@layout/App/UserContext';
import BannerCarousel from '@pages/Home/partials/PromoBanners/partials/BannerCarousel';
import HeatMap from '@pages/Fno/partials/HeatMaps';
import FundCard from '@pages/Home/partials/FundCard';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { INFO_CARD } from 'utils/enum';
import SleekCard from '@common/SleekCard';
import { sendPageEvent } from '@service/AnalyticsService';
import { UserContext } from 'layout/App/UserContext';
import useNudge from '@common/useNudge';
import Mover from '../../modules/FnoMovers/FnoMovers';
import styles from './index.scss';
import FiiDiiCard from './partials/FiiDiiCard';
import { MarketIndices } from '../Home/partials/MarketIndices';
import FnoNews from './partials/FnoNews';
import FnoPositionsCard from './partials/FnoPositonCard';
import SwitchView from './partials/SwitchView';
import { MARKET_MOVERS, VIEW } from './enum';
import CONFIG from '../../../config';
import MarketRadar from './partials/MarketRader';
import MarketScanner from './partials/MarketScanner';
import MarketGrowthCard from './partials/MarketGrowth';

function FnoDashboard() {
  const { irStatus } = useIrData();
  const [view, setView] = useState(VIEW[0].id);
  const { equityCardInfo: { [INFO_CARD.FNO_DASHBOARD]: infoCard } } = useLoggedInContext();
  const [cardConfig, setCardConfig] = useState();
  const [showMessage, setShowMessage] = useState(false);

  const { webConfig } = useContext(UserContext);

  useNudge(JSON.parse(webConfig?.nudgeScreenConfig || null)?.FNO_DASHBOARD);

  useEffect(() => {
    // openScreen Events || Pulse Events
    sendPageEvent({
      eventDetails: {
        screenName: 'f&o_dashboard',
        vertical_name: 'derivatives',
        event_action: 'open_screen',
      },
    });
  }, []);

  useEffect(() => {
    if (infoCard && infoCard.message) {
      setCardConfig(infoCard);
      setShowMessage(true);
    } else {
      setShowMessage(false);
    }
  }, [infoCard]);
  if (!irStatus) { return null; }
  return (
    <div>
      {showMessage && (
      <SleekCard
        cardConfig={{
          preIconClass: styles.statusIcon,
          icon: cardConfig.icon,
          ...cardConfig,
        }}
        setShowMessage={() => {
          setShowMessage(!showMessage);
        }}
        showClose
        className={styles.sleekCard}
        closeIconClass={styles.close}
        messageClass={styles.messageClass}
      />
      )}
      <div className={styles.fnoWrapper}>
        <div className={styles.leftSection}>
          <MarketIndices
            url={CONFIG.FNO_MARKET_INDICES_URL}
            enableOptionChainLink
            scrollCount={2.8}
            isFno
          />
          <div className={styles.fundsPosition}>
            <FundCard />
            <FnoPositionsCard derivativesOnly />
          </div>
          <MarketGrowthCard />
        </div>
        <div className={styles.rightSection}>
          <FnoNews />
          <div className={styles.bannerCarousel}>
            <BannerCarousel />
          </div>
          <FiiDiiCard />
        </div>
      </div>
      <HeatMap />
      <div className={styles.sectionHeader}>
        <span>{MARKET_MOVERS}</span>
        <SwitchView view={view} setView={setView} />
      </div>
      <Mover isWidget view={view} />
      <MarketScanner />
      <MarketRadar />
    </div>
  );
}

export default FnoDashboard;
