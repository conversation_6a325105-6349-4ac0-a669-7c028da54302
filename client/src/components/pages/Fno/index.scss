@import '../../../styles/main';

.fnoWrapper {
  width: 100rem;
  display: flex;
  justify-content: space-between;
}

.leftSection {
  flex: 66rem;
  margin-right: 1.5rem;
}

.displayNone {
  display: none;
}

.rightSection {
  flex: 32rem;
}

.positionsWrapper {
  display: flex;
}

.bannerCarousel {
  margin-top: 4.4rem;
  margin-bottom: 6.9rem;

  > div {
    width: 29.2rem;

    img {
      height: 12.6rem;
    }
  }
}

.marketIndices {
  margin-top: -2rem;

  > div {
    margin-bottom: .9rem;
  }
}

.heatMapsContainer {
  padding: 1.5rem 0;
}

.buttonsListContainer {
  margin-bottom: 1rem;
}

.sectionHeader {
  color: $grey1;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  padding: .2rem;
  margin-right: 4.1rem;

  @include typography(h5, semibold);
}

.heatMapSectionHeader {
  color: $grey1;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  padding: .2rem;
  margin-right: 1.1rem;

  @include typography(h5, semibold);
}

.headTabList {
  > div {
    overflow-x: auto;
    scrollbar-width: none;
    margin-top: 1rem;

    > * {
      white-space: nowrap;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.headTab {
  display: inline-block;
  text-align: center;
  min-width: 14rem;
  max-width: 14rem;
  overflow: hidden;
  text-overflow: ellipsis;
}

.heatMapsTabContent {
  padding: 1.5rem 1.5rem 0;
  background-color: $default;
  margin-top: .2rem;
}

.heatMapsTabContainer {
  background-color: $default;

  > div:nth-child(1) {
    padding: 1.5rem 1.5rem 3rem;
  }
}

.colorRepresentedTextContainer {
  width: 27rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1.5rem;
}

.colorCodedText {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: $grey2;

  @include fontSize(h8);
}

.circled {
  width: 1rem;
  height: 1rem;
  border-radius: 100%;
  margin-right: .6rem;
}

.allBuiltUp {
  border-right-color: $red;
  border-top-color: $green;
  border-bottom-color: $soft-blue;
  border-left-color: $yellow;
  border-width: .5rem;
  border-style: solid;
  height: 0;
  width: 0;
  transform: rotate(45deg);
}

.longBuiltup {
  background-color: $soft-green;
}

.shortBuiltup {
  background-color: $pink;
}

.shortCovering {
  background-color: $navy-blue;
}

.longUnwinding {
  background-color: $dark-yellow;
}

.lowOpacity,
.sbuLowOpacity {
  opacity: .5;
}

.sbuMediumOpacity {
  opacity: .7;
}

.mediumOpacity {
  opacity: .75;
}

.sbuHighOpacity {
  opacity: .85;
}

.highestOpacity,
.sbuHighestOpacity {
  opacity: 1;
}

.boldText {
  @include fontWeight(bold);
}

.heatMapsBoxContainer {
  display: flex;
  flex-wrap: wrap;
  max-height: 30rem;
  overflow-y: auto;
  cursor: pointer;
}

.FullheatMapsBoxContainer {
  display: flex;
  flex-wrap: wrap;
  max-height: 48rem;
  overflow-y: auto;
  cursor: pointer;
}

.heatMapsBoxWrapper {
  width: 15.6%;
  height: 6.4rem;
  margin-right: .4rem;
  margin-bottom: .4rem;
}

.heatMapsBoxContentWrapper {
  width: 100%;
  height: 100%;
  color: $grey0;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @include typography(h7);
}

.heatMapsBoxContent {
  height: 100%;
  border-radius: 5px;
}

.heatMapsBoxContentInfo {
  position: absolute;
  top: 1.6rem;
  left: 2rem;

  > div:nth-child(2) {
    color: $grey0;
    margin-top: .5rem;

    @include typography(h6, bold);
  }
}

.heatMapsBoxSymbol {
  color: $grey0;

  @include typography(h7, default);
}

.emptyState {
  flex: 1 0 0;
  min-height: 32rem;
}

.toolTip {
  position: relative;
}

.tooltipBubble {
  background: $default;
  position: absolute;
  width: 33.4rem;
  height: 20.3rem;
  z-index: 10;
  border-radius: 8px;

  @include box-shadow(0, -2px, 10px);

  &::after {
    content: '';
    position: absolute;
    bottom: 70%;
    border-top: .6rem solid transparent;
    border-bottom: .6rem solid transparent;
  }
}

.tooltipBubbleRight {
  &::after {
    right: 100%;
    border-right: .6rem solid $default;
  }
}

.tooltipBubbleLeft {
  &::after {
    left: 100%;
    border-left: .6rem solid $default;
  }
}

.tooltipHeading {
  display: flex;
  flex-direction: column;
  margin: 1.5rem 1.2rem 1rem;
  color: $grey0;
  border-bottom: solid .1rem $grey4;
  padding-bottom: 1.2rem;
}

.tooltipCompanyName {
  opacity: .8;

  @include typography(h6, semibold);
}

.tooltipCompanyPricingInfo {
  display: flex;
}

.companyHoldings {
  line-height: 3rem;
  color: $grey0;

  @include typography(h4, bold);
}

.pl {
  margin-left: 1rem;
  margin-bottom: .7rem;
  position: relative;

  > div,
  > span {
    @include typography(h7);
  }
}

.tooltipPositionName {
  margin-left: auto;
  padding: .4rem .8rem;
  border-radius: .2rem;
  color: $default;
  align-self: center;

  @include typography(h8);
}

.tooltipOpenInterest {
  display: flex;
  flex-direction: column;
  margin: 0 1.2rem;

  > div {
    display: flex;

    > div {
      width: 7.8rem;
      padding-bottom: .8rem;
    }
  }
}

.tooltipOpenInterestFirstRow {
  margin-left: auto;
  color: $grey3;

  @include typography(h8);
}

.tooltipOpenInterestSecondRow,
.tooltipOpenInterestThirdRow,
.tooltipOpenInterestFourthRow {
  color: $grey2;

  @include typography(h7);
}

.tooltipOpenInterestSecondRow :first-child,
.tooltipOpenInterestThirdRow :first-child,
.tooltipOpenInterestFourthRow :first-child {
  color: $grey1;

  @include fontWeight(bold);
}

.tooltipOpenInterestFourthRow {
  padding-top: .8rem;
  margin-top: .6rem;
  margin-bottom: 1.5rem;
  border-top: solid .1rem $grey4;
}

.positive {
  color: $green;
}

.negative {
  color: $red;
}

.oiFieldsContainer {
  width: 25rem;
  display: flex;
  justify-content: space-between;
  padding-bottom: 1.8rem;

  /* stylelint-disable declaration-no-important */
  > div:first-child {
    margin-left: 1rem !important;
    width: 18rem !important;
  }
  /* stylelint-enable declaration-no-important */
}

.dropdown {
  width: 9rem;
  height: 3rem;
  padding: .2rem 1rem .4rem .8rem;
  margin-bottom: 1.5rem;
  border-radius: .4rem;
  background-color: $grey4;
  color: $grey2;

  > div:first-child {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.optionClass {
  height: 3rem;
}

.dropdownClass {
  max-height: 19rem;
  overflow: auto;
}

.disabled {
  width: 5rem;
  height: 3rem;
  border: transparent;
  background-color: $grey4;
  border-radius: .4rem;
  color: $grey2;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 3rem;

  @include fontSize(h8);
}

.colorRepresentationalContainer {
  width: 16rem;
  display: flex;
  justify-content: space-between;

  .colorCodedText {
    @include fontSize(h7);
  }
}

.oiFooterContainer {
  display: flex;
  justify-content: center;
}

.pcrContent,
.volumePcrContent {
  padding: 0 1rem;

  @include fontSize(h7);
}

.pcrLabel,
.volumePcrLabel {
  color: $grey3;
}

.pcrValue,
.volumePcrValue {
  color: $grey1;
  padding-left: .5rem;
}

.iframeContainer {
  flex: 1 0 0;
  position: relative;
  height: 38rem;
}

.iframe {
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: .4rem;
}

.loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $default;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fnoSearchContainer {
  margin-left: 1rem;
  border-radius: 4px;
  border: solid 1px $grey4;
  width: 17rem;

  input {
    padding: .5rem 0;

    @include fontSize(h8);

    &::placeholder {
      @include fontSize(h8);
    }
  }

  /* stylelint-disable declaration-no-important */
  img {
    width: .9rem !important;
    height: .9rem !important;
  }

  > div:first-child {
    height: 3rem !important;
  }
  /* stylelint-enable declaration-no-important */

  > div:last-child {
    top: 3rem;
  }

  label {
    > div:first-child {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.colorRepresentationWrapper {
  margin-top: 1rem;
  border-top: 1px solid $grey4;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;

  > div:nth-child(2) {
    color: $primary-color;
    cursor: pointer;

    @include typography(h7, semibold);
  }
}

.viewAllArrow {
  border: solid;
  border-width: .2rem .2rem 0 0;
  border-color: $primary-color;
  display: inline-block;
  padding: .3rem;
  transform: rotate(45deg);
  cursor: pointer;
}

.activeTab {
  /* stylelint-disable-next-line declaration-no-important */
  color: $primary-color !important;
  border-bottom: solid 2px $primary-color;
}

.buttonsContainer {
  border-bottom: 1px solid $grey4;
}

.subTabList {
  > div {
    overflow-x: auto;
    scrollbar-width: none;
    padding-top: 1rem;
    padding-bottom: 1rem;

    > * {
      white-space: nowrap;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.headingStyle {
  margin-bottom: 3rem;

  @include typography(h5, bold);
}

.scanner_header {
  margin: 2rem 0;

  @include typography(h5, semibold);
}

.fundsPosition {
  width: 66rem;
  display: flex;
  justify-content: space-between;
}

.sleekCard {
  width: 97%;
  margin-bottom: 1.5rem;
  position: relative;
  border: .1rem solid rgbaColor($sunshine);
  border-radius: 1rem;
  padding: .5rem;
}

.messageClass {
  padding: .5rem;
  margin-right: 1.5rem;

  @include typography(h7);
}

.statusIcon {
  transform: translateY(.7rem);
}

.close {
  position: absolute;
  right: 1rem;
  cursor: pointer;
}
