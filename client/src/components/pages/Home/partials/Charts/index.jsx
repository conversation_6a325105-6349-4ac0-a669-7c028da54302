import React, { useState, useEffect, useContext } from 'react';

import Charts from '@pages/Company/partials/Charts';
import { EXCHANGE } from '@utils/enum';
import { useGetApi } from '@common/UseApi';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { ChartsProvider } from '@pages/Company/partials/Charts/chartsContext';
import LightViewCharts from '@pages/Company/partials/LightViewCharts';
import LocalStorage from '@service/LocalStorage';
import BannerCarousel from '@pages/Home/partials/PromoBanners/partials/BannerCarousel';
import Holdings from '@pages/Home/partials/Charts/partials/Holdings';
import NonIRChartIndices from '@pages/Home/partials/Charts/partials/NonIRChartIndices';
import { LOCAL_STORAGE_KEY } from '@pages/Home/enums';
import Returns from '@pages/Home/partials/Charts/partials/Returns';

import { HoldingsContext } from '@modules/Holdings';
import { classNames as cx } from '@utils/style';
import useDowntimeConfig from '@common/useDowntimeConfig';
import { mobileBrowser, renameKeys } from '@utils';
import styles from './index.scss';
import ChartIndices from './partials/ChartIndices';
import EquityDowntime from './partials/EquityDowntime';

function HomeChartComponent({ isInvested }) {
  const { selectedChart } = useLoggedInContext();
  const { makeRequest } = useGetApi();
  const [chartIndexOptions, setChartIndexOptions] = useState(null);
  const [selectedChartIndex, setChartIndex] = useState(() => (isInvested ? null : { rangeParam: '5y' }));

  useEffect(() => {
    async function fetchData() {
      try {
        const res = await fetch('https://static.paytmmoney.com/data/v1/indices/chart-indices.json');
        const { data } = await res.json();
        const chartIndicesData = data.map((option) => renameKeys(
          {
            instrument: 'instrumentType',
            security_id: 'securityId',
          }, option,
        ))
          .map((option) => (
            {
              ...option,
              isLoading: false,
              toggleDetails: false,
            }
          ));
        const chartIndex = {};
        chartIndex[EXCHANGE.NSE] = chartIndicesData.filter((option) => (option.exchange === EXCHANGE.NSE));
        chartIndex[EXCHANGE.BSE] = chartIndicesData.filter((option) => (option.exchange === EXCHANGE.BSE));
        setChartIndexOptions(chartIndex);
      } catch (error) { }
    }
    makeRequest(fetchData());
  }, [makeRequest]);

  useEffect(() => {
    const savedChartIndex = LocalStorage.get(LOCAL_STORAGE_KEY.CHART_INDEX, false);
    if (chartIndexOptions) {
      if (!isInvested) {
        setChartIndex({ ...chartIndexOptions[EXCHANGE.NSE][0], rangeParam: '5y' });
      } else if (savedChartIndex) {
        setChartIndex(savedChartIndex);
      } else {
        setChartIndex(chartIndexOptions[EXCHANGE.NSE][0]);
      }
    }
  }, [chartIndexOptions, isInvested]);

  return (
    <div className={cx(styles.wrapper, {
      [styles.isNotInvested]: !isInvested,
    })}
    >
      <ChartsProvider
        {...selectedChartIndex}
      >
        {
          isInvested && (
            <ChartIndices
              selectedChartIndex={selectedChartIndex}
              setChartIndex={setChartIndex}
              chartIndexOptions={chartIndexOptions}
            />
          )
        }
        {
          !isInvested && (
            <NonIRChartIndices chartIndex={chartIndexOptions} />
          )
        }
        { selectedChart === SELECT_CHART_MODES[1].id
          ? (
            <LightViewCharts
              chartIndices
              hideShadow
              hideRange={!isInvested}
              containerClass={cx(styles.chartsContainer, {
                [styles.notInvested]: !isInvested,
                [styles.invested]: isInvested,
              })}
            />
          )
          : (
            <Charts
              chartIndices
              hideShadow
              hideRange={!isInvested}
              containerClass={cx(styles.chartsContainer, {
                [styles.notInvested]: !isInvested,
                [styles.invested]: isInvested,
              })}
            />
          )}
        {!isInvested && (
          <div className={styles.chartsBottomCards}>
            <Returns />
            <BannerCarousel />
          </div>
        )}
      </ChartsProvider>
    </div>
  );
}

function HomeChart({ isInvested }) {
  const { overview, inProgress } = useContext(HoldingsContext);
  return (
    <>
      {isInvested && <HoldingsWrapper overview={overview} inProgress={inProgress} />}
      {!mobileBrowser() && <HomeChartComponent isInvested={isInvested} />}
    </>
  );
}

const HoldingsWrapper = ({ overview, inProgress }) => {
  const { configJson: { holdings_downtime }, fetchInProgress, getDowntimeConfig } = useDowntimeConfig();

  if (holdings_downtime?.equity_holdings_downtime) {
    return (
      <EquityDowntime
        heading={holdings_downtime?.equity_holdings_heading}
        message={holdings_downtime?.equity_holdings_message}
        icon={holdings_downtime?.equity_holdings_img}
        getStocksPortfolio={getDowntimeConfig}
      />
    );
  }
  return <Holdings overview={overview} inProgress={inProgress || fetchInProgress} />;
};

export default HomeChart;
