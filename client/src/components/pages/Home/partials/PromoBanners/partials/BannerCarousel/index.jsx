import React, { useRef } from 'react';

import 'slick-carousel/slick/slick.css';
import Carousel from '@common/Carousel';
import { isEmpty } from 'lodash';
import WithLoader from '@common/WithLoader';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import { sendEvent } from '@service/AnalyticsService';
import usePromoBanners from '../../usePromoBanners';
import LoadingBlock from '../LoadingBlock';
import styles from './index.scss';

const SliderComponent = ({ banners }) => {
  const sliderRef = useRef(null);
  const smallBannerOptions = {
    className: styles.sliderWrapper,
    dots: true,
    arrows: true,
    infinite: true,
    autoplay: true,
    speed: 1000,
    pauseOnHover: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    ref: sliderRef,
  };
  const handleOnClick = (param) => {
    if (param.includes('margin-pledge')) {
      sendEvent({
        event_category: 'marginpledge',
        event_action: 'margin pledge_entry_stockdashboardbanner',
        event_label: 'marginpledge',
        vertical_name: 'stocks',
        screenName: '/marginpledge',
      });
    }
  };
  return (
    <div className={styles.sliderContainer}>
      {!isEmpty(banners) && (
      <Carousel options={smallBannerOptions}>
        {banners.map(({ image_url, url }, index) => (
          <a href={url} key={index} aria-label="banner link" onClick={() => handleOnClick(url)}>
            <div className={styles.imgWrap}>
              <img src={image_url} alt="" />
            </div>
          </a>
        ))}
      </Carousel>
      )}
    </div>
  );
};

const LoadingComponent = () => (
  <SkeletonLoader type={TYPE.CUSTOM} CustomLoader={LoadingBlock} />
);

const SliderHOC = WithLoader({ WrappedComponent: SliderComponent, LoadingComponent });

function BannerCarousel() {
  const { banners, inProgress } = usePromoBanners();

  return (
    <div className={styles.container}>
      <SliderHOC banners={banners} inProgress={inProgress} />
    </div>
  );
}

export default BannerCarousel;
