import React from 'react';
import TableContainer from '@common/Table';
import Pagination from '@common/Pagination';
import EmptyState from '@common/EmptyState';
import { ICON_NAME } from '@common/Icon/enums';
import WithLoader from '@common/WithLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import SkeletonLoader from '@common/SkeletonLoader';
import { useHistory } from 'react-router-dom';
import { COMMON_PAGE_GA } from '@constants/ga-events';
import { ROUTE_NAME } from '@utils/enum';
import { sendEvent } from '@service/AnalyticsService';
import { mobileBrowser } from '@utils';
import Routes from '../../../routes';
import styles from './styles';
import TableRow from './partials/TableRow';

const headerOptions = [{
  id: 'order_date_time',
  name: 'Time',
}, {
  id: 'display_name',
  name: 'Name',
}, {
  id: 'display_product',
  name: 'Type',
}, {
  id: 'order_value',
  name: 'Order Value',
}, {
  id: 'quantity',
  name: 'Quantity',
}, {
  id: 'avg_traded_price',
  name: 'Avg Price',
}, {
  id: 'price',
  name: 'Price',
}, {
  id: 'display_status',
  name: 'Status',
}];

function Table({
  results = [], totalCount, openDetails, pageNumber, changePage, inProgressPage,
}) {
  return (
    <>
      <TableContainer options={headerOptions} className={styles.tableRow} showHeader={!mobileBrowser()}>
        {results.map((item, index) => (
          <TableRow key={index} item={item} openDetails={openDetails} />
        ))}
      </TableContainer>
      {inProgressPage && (
      <SkeletonLoader
        type={TYPE.TABLE}
        rows={8}
        columns={mobileBrowser() ? 2 : 8}
        className={styles.loaderTableRow}
      />
      )}
      {results.length
        ? (
          <Pagination
            pageNumber={pageNumber}
            totalCount={totalCount}
            changePage={changePage}
          />
        )
        : null}
    </>
  );
}

function LoadingComponent() {
  return (
    <TableContainer options={headerOptions} className={styles.loaderTableRow} isNested showHeader={!mobileBrowser()}>
      <SkeletonLoader type={TYPE.TABLE} rows={8} columns={mobileBrowser() ? 2 : 8} className={styles.loaderTableRow} />
    </TableContainer>
  );
}

function EmptyComponent({ filterUsed, getInitialData }) {
  const history = useHistory();
  return (
    <EmptyState
      iconName={ICON_NAME.EMPTY_PAST_ORDERS}
      text={filterUsed ? 'No orders found for the selection' : "You haven't placed any orders yet"}
      subText={filterUsed ? 'Try changing your search/filter selection' : 'Buy or Sell stocks to get started'}
      ctaText={filterUsed ? 'Go back to Past Orders' : 'Place Order'}
      className={styles.emptyContainer}
      onCtaClick={() => {
        if (filterUsed) getInitialData();
        else history.push(Routes[ROUTE_NAME.MOVERS].url);
        sendEvent({
          event_category: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_CATEGORY,
          event_action: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_ACTION,
          event_label: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_LABEL('past orders'),
        });
      }}
    />
  );
}

const TableHOC = WithLoader({ WrappedComponent: Table, LoadingComponent, EmptyComponent });

export default TableHOC;
