@import '../../../../../styles/main';

.detailsBtn {
  justify-content: flex-end;
}

.tableRow {
  cursor: pointer;

  @include respond(phone) {
    height: 8.3rem;
  }

  @include respond(phone) {
    > div:nth-child(2) {
      /* stylelint-disable-next-line declaration-no-important */
      justify-content: flex-start !important;

      > div:nth-child(1) {
        margin-bottom: .5rem;
      }
    }
  }

  > * {
    text-align: right;
  }

  > div:nth-child(1),
  div:nth-child(2),
  div:nth-child(3),
  div:last-child {
    text-align: left;
  }
}

.tradedQty {
  /* stylelint-disable-next-line declaration-no-important */
  text-align: right !important;
}

.loaderTableRow {
  > * {
    min-width: 10rem;
    justify-content: flex-end;
    text-align: right;
  }

  > div:nth-child(1) {
    min-width: 14rem;
    justify-content: flex-start;
  }

  > div:nth-child(3) {
    justify-content: flex-start;
    min-width: 11rem;
  }

  > div:nth-last-child(2) {
    min-width: 16rem;
  }

  > div:nth-child(2) {
    width: 100%;
    justify-content: flex-start;
  }

  > div:last-child {
    justify-content: flex-start;
    text-align: left;
    min-width: 12rem;
  }
}

.emptyContainer {
  min-height: calc(100vh - 13rem);
}

.tags {
  display: flex;
  align-items: center;
}

.tag {
  display: flex;
  color: $grey3;
  justify-content: center;
  align-items: center;
  height: 2rem;
  padding: 0 1rem;
  border-radius: .24rem;
  border: solid .1rem $grey3;
  margin-right: 1rem;

  @include typography(h8);

  @include respond(phone) {
    height: 1.8rem;
    padding: .3rem .6rem;
  }
}

.rowDivider {
  > div > *:first-child {
    padding-bottom: .5rem;
  }
}

.alignContent {
  justify-content: space-between;
}

.time {
  color: $grey3;

  @include typography(h8);
}
