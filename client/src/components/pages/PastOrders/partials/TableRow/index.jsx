import React from 'react';
import { COLORMAP, CONSTMAP } from '@pages/Orders/TodayOrders/OrderDataTable/constants';
import orderStyles from '@pages/Orders/TodayOrders/OrderDataTable/partials/TableRow/orders-table-common.scss';
import styles from '@pages/Orders/TodayOrders/OrderDataTable/styles';
import { ICON_NAME } from '@common/Icon/enums';
import { getPrice } from '@pages/Orders/TodayOrders/OrderDataTable/partials/TableRow';
import RedirectToCharts from '@pages/Orders/TodayOrders/OrderDataTable/partials/RedirectToCharts';
import Button from '@common/Table/Button';
import StackedRow from '@common/Table/StackedRow';
import { classNames as cx } from '@utils/style';
import { table } from '@commonStyles';
import { getTime, formatPrice, mobileBrowser } from '@utils';
import pastOrderStyles from '../../styles';

function TableRow({
  item, openDetails,
}) {
  const orderValue = item.quantity * (item.avg_traded_price || item.price);

  if (mobileBrowser()) {
    const BottomEndSlot = () => {
      if (item.display_status === CONSTMAP.SUCCESSFUL) {
        return (
          <div className={pastOrderStyles.tradedQty}>
            {`${item.traded_qty} x ${getPrice(item.order_type, item.price, item.display_status, item.avg_traded_price, item.trigger_price)}`}
          </div>
        );
      }
      return (<div>{`${item.traded_qty}/${item.quantity}`}</div>);
    };

    return (
      <div
        role="presentation"
        className={cx([pastOrderStyles.tableRow, table.plainTableRow, styles.rowDivider, pastOrderStyles.alignContent])}
        onClick={openDetails.bind(null, { data: { ...item, order_value: orderValue } })}
      >
        <StackedRow
          slotTopStart={(
            <div className={styles.stockName}>
              {item.display_name}
              <span className={orderStyles.exchange}>
                {item?.exchange}
              </span>
            </div>
)}
          slotTopEnd={(
            <div className={`${orderStyles[COLORMAP[item.display_status]]} ${styles.status}`}>
              {item.display_status}
            </div>
)}
          slotBottomStart={(
            <div className={styles.column}>
              <div className={styles.tags}>
                <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
                  {item?.txn_type}
                </span>
                <div className={styles.tag}>{item.display_product}</div>
              </div>
              <div className={pastOrderStyles.time}>{getTime({ dateTime: item.order_date_time, format: 'DD MMM YYYY HH:mm A' })}</div>
            </div>
)}
          slotBottomEnd={<BottomEndSlot />}
        />
      </div>
    );
  }
  return (
    <div
      role="presentation"
      className={`${pastOrderStyles.tableRow} ${table.stripedFlatTableRow}`}
      onClick={openDetails.bind(null, { data: { ...item, order_value: orderValue } })}
    >
      <div>{getTime({ dateTime: item.order_date_time, format: 'DD MMM YYYY HH:mm' })}</div>
      <div className={`${table.rowControlsHolder}`}>
        <div className={styles.name}>
          <div className={orderStyles.displayName}>
            <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
              {item?.txn_type}
            </span>
            <span className={styles.stockName}>
              {item.display_name}
              <span className={orderStyles.exchange}>
                {item?.exchange}
              </span>
            </span>
            {
              openDetails
              && (
                <div className={table.rowControls}>
                  <Button
                    message="Details"
                    onClick={(e) => {
                      e.stopPropagation();
                      openDetails({ data: { ...item, order_value: orderValue } });
                    }}
                    icon={ICON_NAME.INFO}
                  />
                  <RedirectToCharts scrip={item} />
                </div>
              )
            }
          </div>
        </div>
      </div>
      <div>{item.display_product}</div>
      <div>{`₹ ${formatPrice(orderValue)}`}</div>
      <div>{`${item.traded_qty} / ${item.quantity}`}</div>
      <div>{formatPrice(item.avg_traded_price)}</div>
      <div>{getPrice(item.order_type, item.price, item.display_status, item.avg_traded_price, item.trigger_price)}</div>
      <div className={`${orderStyles[COLORMAP[item.display_status]]} ${styles.status}`}>
        {item.display_status}
      </div>
    </div>
  );
}

export default TableRow;
