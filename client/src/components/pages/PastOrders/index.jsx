import React, {
  useState, useCallback, useEffect,
} from 'react';
import { useHistory } from 'react-router-dom';
import DateTime from '@common/DateTime';
import usePagination from '@common/Pagination/usePagination';
import { useModal } from '@layout/App/ModalContext';
import { ICON_NAME } from '@common/Icon/enums';
import { useIrData } from '@layout/App/UserContext';
import If from '@common/If';
import Search from '@common/Search';
import CategoryFilter from '@common/CategoryFilter';
import { ORDER_ACTION_TYPES } from '@pages/Orders/TodayOrders/OrderDetails/enums';
import { getChartsRedirectionUrl } from '@pages/Orders/TodayOrders/OrderDataTable/partials/RedirectToCharts/utils';
import { button } from '@commonStyles';
import { mobileBrowser } from '@utils';
import OrderDetails from '../Orders/TodayOrders/OrderDetails';
import { orderBucketFromStatus } from '../Orders/TodayOrders/OrderDetails/utils';
import { getInitialDateRange, DATE_FILTER_FORMAT } from './utils';
import fetchPastOrders from './api';
import PastOrdersTable from './Table';
import styles from './index.scss';
import { initialFilters, category } from './enums';
import TradeBook from './TradeBook';

function PastOrders() {
  const { readinessDate } = useIrData();
  const [dateRange, setDateRange] = useState(getInitialDateRange(readinessDate));
  const [apiDateRange, setApiDateRange] = useState(dateRange);
  const { openModal } = useModal();
  const [filterUsed, setFilterUsed] = useState(false);
  const [search, setSearch] = useState('');
  const [apiSearch, setApiSearch] = useState(search);
  const [filterList, setFilterList] = useState(initialFilters);
  const [isFilterOpen, setIsFilterOpen] = useState(true);
  const history = useHistory();

  useEffect(() => {
    setDateRange(getInitialDateRange(readinessDate));
  }, [readinessDate]);

  const getResults = useCallback(
    (pageNumber) => fetchPastOrders(apiDateRange, pageNumber, apiSearch, filterList),
    [apiDateRange, apiSearch, filterList],
  );

  const results = usePagination({ getResults });

  function handleDateChange(dateFilterKey, changedMoment) {
    if (!changedMoment.isValid()) { // if not valid moment
      return;
    }
    const updatedRange = {
      ...dateRange,
      [dateFilterKey]: changedMoment.valueOf(),
    };
    setDateRange(updatedRange);
  }

  function setDateRangeForApi() {
    setFilterUsed(true);
    setApiDateRange(dateRange);
  }

  const handleOrderDetailAction = ({ data, orderAction }) => {
    switch (orderAction) {
      case ORDER_ACTION_TYPES.CHARTS: {
        const { security_id, exchange, segment } = data;
        getChartsRedirectionUrl(security_id, exchange, segment)
          .then((url) => history.push(url)).catch(() => {});
        break;
      }
      default:
        break;
    }
  };

  const openDetailModal = ({ data }) => {
    const tabStatus = orderBucketFromStatus(data.display_status);
    openModal({
      Component: OrderDetails,
      componentProps: {
        orderData: data,
        tabStatus,
        isPastOrder: true,
        openDetailModal: handleOrderDetailAction,
      },
      closeIcon: tabStatus === 'Successful' ? ICON_NAME.CLOSE_LIGHT : ICON_NAME.CLOSE_POPUP,
    });
  };

  const handleSubmit = () => {
    setFilterUsed(true);
    setApiSearch(search);
  };

  const getInitialData = () => {
    setSearch('');
    setApiSearch('');
    setDateRange(getInitialDateRange(readinessDate));
    setApiDateRange(getInitialDateRange(readinessDate));
    setFilterList(initialFilters);
  };
  const handleSelectedFilters = useCallback((filters) => {
    if (!filterUsed) setFilterUsed(true);
    setFilterList(filters);
  }, [filterUsed]);

  return (
    <div className={styles.container}>
      <If test={!mobileBrowser()}>
        <div className={styles.title}>
          <div className={styles.filters}>
            <DateTime
              dateFormat={DATE_FILTER_FORMAT}
              value={dateRange.from}
              minDate={readinessDate}
              maxDate={dateRange.to}
              onChange={handleDateChange.bind(null, 'from')}
            />
            <span>to</span>
            <DateTime
              dateFormat={DATE_FILTER_FORMAT}
              value={dateRange.to}
              minDate={dateRange.from}
              maxDate={Date.now()}
              onChange={handleDateChange.bind(null, 'to')}
              alignLeft
            />
            <button
              type="button"
              className={`${button.btn} ${styles.btnView}`}
              onClick={setDateRangeForApi}
            >
              View
            </button>
          </div>
        </div>
      </If>
      <If test={!mobileBrowser()}>
        <div className={styles.filterHeader}>
          <CategoryFilter
            category={category}
            handleSelectedFilters={handleSelectedFilters}
            filterList={filterList}
            resetFilter={() => setFilterList(initialFilters)}
            initialFilters={initialFilters}
            setIsFilterOpen={setIsFilterOpen}
            isFilterOpen={isFilterOpen}
          />
          <TradeBook
            setIsFilterOpen={setIsFilterOpen}
            isFilterOpen={isFilterOpen}
            filterUsed={filterUsed}
          />
          <Search
            placeholder="Search by company name"
            handleOnSearch={(e) => setSearch(e.target.value)}
            searchQuery={search}
            handleSubmit={handleSubmit}
            clearText={() => { setSearch(''); setApiSearch(''); }}
          />
        </div>
      </If>
      <PastOrdersTable
        {...results}
        openDetails={openDetailModal}
        filterUsed={filterUsed}
        refresh={getResults}
        getInitialData={getInitialData}
      />
    </div>
  );
}

export default PastOrders;
