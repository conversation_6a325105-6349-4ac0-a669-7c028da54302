@import '../../../styles/main';

.title {
  color: $grey1;
  display: flex;
  justify-content: flex-end;
  margin-top: -3rem;
  align-items: center;
  margin-bottom: 1.6rem;
  margin-left: auto;
  width: fit-content;

  @include typography(h4);
}

.filters {
  display: flex;
  align-items: center;

  > span {
    margin: 0 1rem;
    color: $grey2;

    @include typography(h7);
  }
}

.btnView {
  margin-left: 1rem;
  padding: .5rem 1.4rem .7rem;
  border-radius: 3px;

  @include btnFill($grey2);
}

.container {
  @include respond(phone) {
    padding-top: 1rem;
  }
}

.filterHeader {
  background-color: $default;
  padding: .9rem .9rem .9rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
