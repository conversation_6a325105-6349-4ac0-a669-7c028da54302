import { debounce } from 'lodash';
import React, { useMemo, useContext, useEffect } from 'react';
import { useHistory, Link } from 'react-router-dom';
import PropTypes from 'prop-types';

import WithLoader from '@common/WithLoader';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import LiveIcon from '@common/LiveIcon';
import { ROUTE_NAME } from '@utils/enum';
import { ChangeWithPercent, ChangeIcon } from '@common/Prices';
import { RESPONSE_TYPES, REQUEST_TYPES } from '@service/dataConfig';
import { classNames as cx } from '@utils/style';
import Icon from '@common/Icon';
import { ICON_NAME, staticImagePath } from '@common/Icon/enums';
import useResultFromFeed from '@common/useResultFromFeed';
import styles from '@pages/Company/partials/Header/styles';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { BookmarkedIcon, NotbookmarkedIcon } from '@modules/WatchlistSidebar/partials/Searchbox/Suggestion';
import Search from '@modules/FnoOptionChain/partials/Search';
import OptionScrip from '@pages/Company/partials/OptionScrip';
import If from '@common/If';
import CustomDropdown from '@common/CustomDropdown';
import ChartsSelector from '@pages/Company/partials/ChartsSelector';
import { button as btnStyles } from '@commonStyles';
import { emptyFn, formatPrice } from '@utils';
import Routes from '@/routes';
import indexStyles from './index.scss';

const requiredFeedResponse = [RESPONSE_TYPES.INDEX];
function IndexHeader(props) {
  const {
    id,
    name,
    exchange,
    securityId,
    toggleDetails,
    toggleExpand,
    toggleDisabled,
    hideChartIcon,
    hideBookmarkIcon,
    showFnoSearch,
    optionsConfig,
    selectedOption,
    onChange,
    currentTab,
    currentLtp = null,
    setCurrentLtp = emptyFn,
  } = props;
  const { handleExpansion, stocksToWatchlistMap, isAdvancedChartSelected } = useContext(WatchlistContext);
  const history = useHistory();
  const watchlistsOfSId = stocksToWatchlistMap[id] || [];

  const isFutureOptionPage = history.location.pathname.includes('option-chain') || history.location.pathname.includes('future-contract');

  const ohlcData = useResultFromFeed(
    REQUEST_TYPES.INDEX,
    requiredFeedResponse,
    { securityId },
  );

  const percentageChange = useMemo(
    () => (ohlcData ? (((ohlcData.lastTradePrice - ohlcData.pClose) / ohlcData.pClose) * 100) : null),
    [ohlcData],
  );

  const ltp = ohlcData?.lastTradePrice;

  useEffect(() => {
    if (currentLtp === null && ltp) {
      setCurrentLtp(ltp);
    }
  }, [currentLtp, ltp, setCurrentLtp]);

  const change = useMemo(
    () => ((ohlcData?.lastTradePrice && ohlcData?.pClose) ? (ohlcData.lastTradePrice - ohlcData.pClose) : null),
    [ohlcData],
  );

  const onFnoSearchClick = (suggestion) => {
    if (history.location.pathname.includes('option-chain')) {
      history.push(`${Routes[ROUTE_NAME.FNO_OPTION_CHAIN].url}/${suggestion.id}/${suggestion.name}/${suggestion.exchange}`);
    }
    if (history.location.pathname.includes('future-contract')) {
      history.push(`${Routes[ROUTE_NAME.FUTURE_CONTRACT].url}/${suggestion.id}/${suggestion.name}/${suggestion.exchange}`);
    }
  };

  const debounceToggleExpand = useMemo(() => (toggleExpand ? debounce(toggleExpand, 300) : null), [toggleExpand]);

  const onBackClick = () => history.goBack();

  return (
    <>
      <div className={isFutureOptionPage ? indexStyles.indexCompanyName : styles.companyName}>
        <div role="presentation" onClick={onBackClick} className={styles.backArrowIcon}>
          <img src={`${staticImagePath}/common/back-arrow.svg`} alt="Back Icon" />
        </div>
        <span>
          {name}
          <span className={isFutureOptionPage ? indexStyles.exchange : indexStyles.hidden}>{exchange}</span>
          <Link to={`${Routes[ROUTE_NAME.INDEX_PAGE].url}/${id}`} className={isFutureOptionPage ? styles.icon : styles.hidden}>
            <Icon name={ICON_NAME.REDIRECTION_ICON} />
          </Link>
        </span>
        {showFnoSearch && (
        <div className={indexStyles.indexSearch}>
          <Search onChange={onFnoSearchClick} pageName={name} defaultView className={styles.fnoSearch} />
        </div>
        )}
        {!hideBookmarkIcon && (
          <div role="presentation" onClick={handleExpansion.bind(null, id, false)} className={styles.bookmarkIcon}>
            {watchlistsOfSId.length
              ? <BookmarkedIcon size={4} /> : <NotbookmarkedIcon size={4} />}
          </div>
        )}
        {isAdvancedChartSelected && <ChartsSelector />}
      </div>
      <div className={isFutureOptionPage ? indexStyles.futureOptionWrapper : styles.wrapper}>
        <div className={styles.leftContainer}>
          <span className={styles.companyHoldings}>{formatPrice(ltp)}</span>
          <div className={styles.change}>
            <ChangeWithPercent
              value={change}
              withRupee={false}
              percent={percentageChange}
              className={styles.pl}
            />
            <ChangeIcon value={change} />
            <div className={styles.iconLayout}>
              <div className={styles.exchangeLayout}>
                <LiveIcon exchange={exchange} showExchange />
              </div>
            </div>
          </div>
        </div>
        <div className={styles.rightContainer}>
          <OptionScrip id={id} name={name} />
          <Link to={`${Routes[ROUTE_NAME.INDEX_PAGE].url}/${id}?toggleDetails=true`} className={isFutureOptionPage ? styles.chartIcon : styles.hidden}>
            <Icon name={ICON_NAME.CHART_ICON} />
          </Link>
          <div>
            <If test={isFutureOptionPage && currentTab === 1}>
              <div className={indexStyles.customDropdown}>
                <CustomDropdown
                  selectedValue={selectedOption}
                  options={optionsConfig || []}
                  displayValue={selectedOption?.label}
                  onChange={onChange}
                  containerClass={`${indexStyles.dropdown}`}
                  optionDefaultClass={indexStyles.optionClass}
                  dropdownClass={indexStyles.dropdownClass}
                />
              </div>
            </If>
            {!hideChartIcon && (
            <div className={cx([styles.rightContainer, styles.flexEnd])}>
              <button
                className={cx([styles.btn, styles.chartBtn, btnStyles.btn, btnStyles.btnSmall, styles.btnSwitch],
                  { [styles.disabled]: toggleDisabled })}
                onClick={debounceToggleExpand}
                disabled={toggleDisabled}
                aria-label="Toggle Chart"
              >
                <Icon name={toggleDetails ? ICON_NAME.CHART_LINE : ICON_NAME.CANDLE_CHART} size={8} />
              </button>
            </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

IndexHeader.propTypes = {
  exchange: PropTypes.string.isRequired,
  securityId: PropTypes.number.isRequired,
};

const LoadingComponent = () => <SkeletonLoader type={TYPE.WATCHLIST} className={indexStyles.headerLoader} />;

const IndexHeaderWithLoader = WithLoader({ WrappedComponent: IndexHeader, LoadingComponent });

export default IndexHeaderWithLoader;
