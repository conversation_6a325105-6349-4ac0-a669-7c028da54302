import React, { useContext, useEffect } from 'react';
import If from '@common/If';
import { useLocation } from 'react-router-dom';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { getUrlParameter, mobileBrowser } from '@utils';
import Charts from '../../../Company/partials/Charts';
import TradingViewCharts from '../../../Company/partials/TradingViewCharts';
import LightViewCharts from '../../../Company/partials/LightViewCharts';
import { ChartsProvider } from '../../../Company/partials/Charts/chartsContext';
import styles from './index.scss';
import PerformanceCard from './partials/PerformanceCard';
import SimilarIndices from './partials/SimilarIndices';
import IndexBar from './partials/IndexBar';
import { WatchlistContext } from '../../../../modules/WatchlistSidebar/watchlistContext';

const ChartSection = ({
  id, exchange, stockName, securityId, toggleDetails, instrumentType, chartsIsLoading, disableToggleCharts,
}) => (
  <ChartsProvider
    id={id}
    exchange={exchange}
    name={stockName}
    securityId={securityId}
    toggleDetails={toggleDetails}
    instrumentType={instrumentType}
    isLoading={chartsIsLoading}
    disableToggleCharts={disableToggleCharts}
  >
    <Charts />
  </ChartsProvider>
);

const TradingViewChartSection = ({
  id, exchange, stockName, securityId, toggleDetails, instrumentType, chartsIsLoading, disableToggleCharts,
}) => (
  <ChartsProvider
    id={id}
    exchange={exchange}
    name={stockName}
    securityId={securityId}
    toggleDetails={toggleDetails}
    instrumentType={instrumentType}
    isLoading={chartsIsLoading}
    disableToggleCharts={disableToggleCharts}
  >
    <TradingViewCharts />
  </ChartsProvider>
);

const LightViewChartSection = ({
  id, exchange, stockName, securityId, toggleDetails, instrumentType, chartsIsLoading, disableToggleCharts,
}) => (
  <ChartsProvider
    id={id}
    exchange={exchange}
    name={stockName}
    securityId={securityId}
    toggleDetails={toggleDetails}
    instrumentType={instrumentType}
    isLoading={chartsIsLoading}
    disableToggleCharts={disableToggleCharts}
  >
    <LightViewCharts />
  </ChartsProvider>
);

function Overview({
  id, toggleDetails, securityId, exchange, name, instrumentType, chartsIsLoading, inProgress, disableToggleCharts,
}) {
  const { search } = useLocation();
  const {
    searchInputRef,
  } = useContext(WatchlistContext);

  useEffect(() => {
    try {
      const element = document.getElementById('searchInput');
      const nat = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
      nat.call(element, getUrlParameter('showSearch', search));
      element.dispatchEvent(new Event('input', { bubbles: true }));
    } catch (e) {
    }
  }, [search, searchInputRef]);

  const { selectedChart } = useLoggedInContext();
  return (
    <>
      <If test={toggleDetails && selectedChart === SELECT_CHART_MODES[1].id}>
        <TradingViewChartSection
          id={id}
          exchange={exchange}
          stockName={name}
          securityId={securityId}
          toggleDetails={toggleDetails}
          instrumentType={instrumentType}
          chartsIsLoading={chartsIsLoading}
          inProgress={inProgress}
          disableToggleCharts={disableToggleCharts}
        />
      </If>
      <If test={!toggleDetails && selectedChart === SELECT_CHART_MODES[1].id}>
        <LightViewChartSection
          id={id}
          exchange={exchange}
          stockName={name}
          securityId={securityId}
          toggleDetails={toggleDetails}
          instrumentType={instrumentType}
          chartsIsLoading={chartsIsLoading}
          inProgress={inProgress}
          disableToggleCharts={disableToggleCharts}
        />
      </If>
      <If test={selectedChart === SELECT_CHART_MODES[0].id}>
        <ChartSection
          id={id}
          exchange={exchange}
          stockName={name}
          securityId={securityId}
          toggleDetails={toggleDetails}
          instrumentType={instrumentType}
          chartsIsLoading={chartsIsLoading}
          inProgress={inProgress}
          disableToggleCharts={disableToggleCharts}
        />
      </If>
      {!toggleDetails && (
        <>
          <div className={styles.cards}>
            <PerformanceCard securityId={securityId} inProgress={inProgress} />
            {!mobileBrowser() && <IndexBar id={id} inProgress={inProgress} />}
          </div>
          <div>
            <SimilarIndices indexId={id} inProgress={inProgress} />
          </div>
        </>
      )}
    </>
  );
}

export default Overview;
