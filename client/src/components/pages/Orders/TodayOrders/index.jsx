import React, {
  useState, useEffect,
  useContext,
} from 'react';
import { MODAL_TYPES } from '@common/Modal/enums';
import NumberedTablist from '@common/Tabs/NumberedTablist';
import OrderDataTable from '@pages/Orders/TodayOrders/OrderDataTable';
import { useModal } from '@layout/App/ModalContext';
import PlaceOrder from '@modules/OrderFlow/PlaceOrder';
import useOrders from '@modules/Orders';
import { classNames as cx } from '@utils/style';
import isEmpty from 'lodash/isEmpty';
import { ICON_NAME } from '@common/Icon/enums';
import { sendEvent, sendPageEvent } from '@service/AnalyticsService';
import { ORDERS_PAGE_GA } from '@constants/ga-events';
import { useLocation, useHistory } from 'react-router-dom';
import { ROUTE_NAME } from '@utils/enum';
import { DraggableModalContext } from '@common/usePlaceOrder';
import { mobileBrowser } from 'utils';
import CancelOrder from '../../../modules/OrderFlow/CancelOrder';
import ModifyOrder from '../../../modules/OrderFlow/ModifyOrder';
import styles from './index.scss';
import {
  CONSTMAP, INSTRUMENTS,
} from './constants';
import OrderDetails from './OrderDetails';
import { ORDER_ACTION_TYPES, STATUS_BUCKET, ORDER_STATUS } from './OrderDetails/enums';
import { isBracketOrCover } from '../utils';
import Routes from '../../../../routes';
import { getChartsRedirectionUrl } from './OrderDataTable/partials/RedirectToCharts/utils';

function Orders() {
  const {
    ordersData, inProgress: inProgressFromContext, failed, getData,
  } = useOrders();
  const location = useLocation();
  const history = useHistory();
  const [inProgress, setInProgress] = useState(inProgressFromContext);
  const [numberedTabList, setNumberedTabList] = useState([
    {
      id: ORDER_STATUS.PENDING,
      count: '',
      label: 'Pending',
    },
    {
      id: ORDER_STATUS.SUCCESSFUL,
      count: '',
      label: 'Successful',
    },
    {
      id: ORDER_STATUS.FAILED,
      count: '',
      label: 'Failed',
    },
    {
      id: ORDER_STATUS.CANCELLED,
      count: '',
      label: 'Cancelled',
    },
  ]);
  const [numberedTab, setNumberedTab] = useState(CONSTMAP.PENDING);
  const [tabData, setTabData] = useState({});
  const { openModal, closeModal } = useModal();
  const [openedMarketDepth, setOpenedMarketDepth] = useState(-1);
  const [showControl, setShowControl] = useState(-1);
  const { openDraggableModal } = useContext(DraggableModalContext);

  const changeNumberedTab = (tab) => {
    history.push(`${Routes[ROUTE_NAME.ORDERS].url}/${tab.toLowerCase()}`);
  };

  useEffect(() => {
    sendPageEvent({
      // openScreen Events || Pulse Events
      eventDetails: {
        screenName: '/stocks_orders_land',
        vertical_name: 'stocks',
        event_action: 'open_screen',
      },
    });
  }, []);

  useEffect(() => {
    setShowControl(-1);
  }, [openedMarketDepth]);

  useEffect(() => {
    const selectedTab = numberedTabList.find(({ id }) => location.pathname.split('/').pop() === id.toLowerCase());
    setNumberedTab(selectedTab ? selectedTab.id : CONSTMAP.PENDING);
  }, [location.pathname, numberedTabList]);

  const createOrdersData = (orders = []) => {
    const pendingOrders = orders.filter((order) => STATUS_BUCKET.PENDING.includes(order.status));
    const successfulOrders = orders.filter((order) => STATUS_BUCKET.SUCCESSFUL.includes(order.status));
    const failedOrders = orders.filter((order) => STATUS_BUCKET.FAILED.includes(order.status));
    const cancelledOrders = orders.filter((order) => STATUS_BUCKET.CANCELLED.includes(order.status));

    const pendingTab = {
      id: ORDER_STATUS.PENDING,
      count: pendingOrders.length,
      label: 'Pending',
    };

    const successTab = {
      id: ORDER_STATUS.SUCCESSFUL,
      count: successfulOrders.length,
      label: 'Successful',
    };

    const failedTab = {
      id: ORDER_STATUS.FAILED,
      count: failedOrders.length,
      label: 'Failed',
    };

    const cancelledTab = {
      id: ORDER_STATUS.CANCELLED,
      count: cancelledOrders.length,
      label: 'Cancelled',
    };
    const tabs = [pendingTab, successTab, failedTab, cancelledTab];
    const tabOrders = {
      [ORDER_STATUS.PENDING]: [...pendingOrders],
      [ORDER_STATUS.SUCCESSFUL]: [...successfulOrders],
      [ORDER_STATUS.FAILED]: [...failedOrders],
      [ORDER_STATUS.CANCELLED]: [...cancelledOrders],
    };
    setNumberedTabList(tabs);
    setTabData(tabOrders);
    setInProgress(false);
  };

  useEffect(() => {
    if (inProgressFromContext) setInProgress(true);
    else createOrdersData(ordersData);
  }, [inProgressFromContext, ordersData]);

  const openDetailModal = ({ data, orderAction }) => {
    let componentProps = {
      name: data.display_name,
      exchange: data.exchange,
      securityId: parseInt(data.security_id, 10),
      segment: data.segment,
      transactionType: data.txn_type,
      initialQuantity: data.quantity,
      productType: data.product,
      orderType: data.order_type,
      initialPrice: data.price || '',
      initialTriggerPrice: data.trigger_price || '',
      isin: data.isin,
      tickSize: data.tick_size / 100,
      lotSize: data.lot_size,
      instrumentType: data.instrument_type || data.intrumentType,
      instrument_type: INSTRUMENTS[data.instrument]
        ? INSTRUMENTS[data.instrument]
        : data.instrument_type || data.intrumentType,
    };

    switch (orderAction) {
      case ORDER_ACTION_TYPES.REPEAT:
      case ORDER_ACTION_TYPES.RETRY:
        if (isBracketOrCover(data.product)) {
          delete componentProps.orderType;
          delete componentProps.initialPrice;
          delete componentProps.initialTriggerPrice;
        }
        if (mobileBrowser()) {
          openModal({
            type: MODAL_TYPES.BOTTOM_SHEET,
            Component: PlaceOrder,
            componentProps,
          });
        } else {
          openDraggableModal({
            type: MODAL_TYPES.POPUP,
            Component: PlaceOrder,
            componentProps,
          });
        }
        break;

      case ORDER_ACTION_TYPES.MODIFY:
        componentProps = {
          ...componentProps,
          tradedQuantity: data.traded_qty,
          extraParams: {
            order_no: data.order_no,
            serial_no: data.serial_no,
            group_id: data.group_id,
          },
          algoOrderNo: data.algo_ord_no,
          legNo: data.leg_no,
          status: data.status,
          ext_info: data?.ext_info,
        };
        if (mobileBrowser()) {
          openModal({
            type: MODAL_TYPES.BOTTOM_SHEET,
            Component: ModifyOrder,
            componentProps,
          });
        } else {
          openDraggableModal({
            type: MODAL_TYPES.POPUP,
            Component: ModifyOrder,
            componentProps,
          });
        }
        break;

      case ORDER_ACTION_TYPES.CANCEL:
        componentProps = {
          ...componentProps,
          orderDetails: data,
          ext_info: data?.ext_info,
          switchModifyModal: () => openDetailModal({ data, orderAction: ORDER_ACTION_TYPES.MODIFY }),
        };
        openModal({
          type: mobileBrowser() ? MODAL_TYPES.BOTTOM_SHEET : MODAL_TYPES.POPUP,
          Component: CancelOrder,
          componentProps,
        });
        break;

      case ORDER_ACTION_TYPES.CHARTS: {
        const { security_id, exchange, segment } = data;
        getChartsRedirectionUrl(security_id, exchange, segment)
          .then((url) => history.push(url)).catch(() => {});
        break;
      }

      default:
        openModal({
          Component: OrderDetails,
          componentProps: {
            orderData: data, openDetailModal, tabStatus: numberedTab, closeModal,
          },
          closeIcon: numberedTab === 'Successful' ? ICON_NAME.CLOSE_LIGHT : ICON_NAME.CLOSE_POPUP,
        });
        break;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.contentWrapper}>
        <NumberedTablist
          options={numberedTabList}
          activeTab={numberedTab}
          activeClassName={styles.activeTab}
          setTab={(tab) => {
            setOpenedMarketDepth(-1);
            changeNumberedTab(tab);
            sendEvent({
              event_category: ORDERS_PAGE_GA.L2_CLICKED.EVENT_CATEGORY,
              event_action: ORDERS_PAGE_GA.L2_CLICKED.EVENT_ACTION,
              event_label: ORDERS_PAGE_GA.L2_CLICKED.EVENT_LABEL(
                CONSTMAP.TODAYS_ORDER.toLowerCase(), tab,
              ),
            });
          }}
          isTable
        />
        <div className={cx(styles.table, { [styles.errorContainer]: failed })}>
          <OrderDataTable
            tabStatus={numberedTab}
            orderData={tabData[numberedTab]}
            orderActionModal={openDetailModal}
            inProgress={inProgress}
            failed={failed}
            reqEmpty={isEmpty(tabData[numberedTab])}
            noOrders={isEmpty(ordersData)}
            refresh={getData}
            openedMarketDepth={openedMarketDepth}
            setOpenedMarketDepth={setOpenedMarketDepth}
            setShowControl={setShowControl}
            showControl={showControl}
          />
        </div>
      </div>
    </div>
  );
}

export default Orders;
