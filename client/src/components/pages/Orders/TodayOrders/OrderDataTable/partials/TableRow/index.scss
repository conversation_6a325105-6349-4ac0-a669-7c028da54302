@import '../../../../../../../styles/main';

.offMarket {
  background: $grey3;
  color: $default;
  border-radius: .2rem;
  min-width: 4.3rem;
  height: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

  @include typography(h8);
}

.name {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tableRow {
  cursor: pointer;

  > * {
    text-align: right;
  }

  > div:nth-child(1),
  div:nth-child(2),
  div:nth-child(3),
  div:last-child {
    text-align: left;
  }

  > div:nth-child(2) {
    width: 25%;
  }
}

.loaderTableRow {
  > * {
    min-width: 10rem;
    justify-content: flex-end;
  }

  > div:nth-child(1) {
    min-width: 7rem;
    justify-content: flex-start;
  }

  > div:nth-child(3) {
    justify-content: flex-start;
    min-width: 11rem;
  }

  > div:nth-last-child(3) {
    min-width: 11rem;
  }

  > div:last-child {
    min-width: 9rem;
    justify-content: flex-start;
    text-align: left;
  }
}

.tableRow:hover {
  .offMarket {
    visibility: hidden;
  }
}

.emptyContainer {
  margin-top: 2rem;
  min-height: calc(100vh - 19.5rem);
}

.dummyRow1 {
  height: 3.1rem;

  @include respond(phone) {
    height: auto;
  }
}

.dummyRow {
  height: 28rem;

  @include respond(phone) {
    height: 54rem;
  }
}

.tag {
  display: flex;
  color: $grey3;
  justify-content: center;
  align-items: center;
  height: 2rem;
  padding: 0 1rem;
  border-radius: .24rem;
  border: solid .1rem $grey3;
  margin-right: 1rem;

  @include typography(h8);

  @include respond(phone) {
    height: 1.8rem;
    padding: .3rem .6rem;
  }
}

.tags {
  display: flex;
  align-items: center;
}

.qtyFraction {
  color: $grey3;

  @include typography(h7);
}

.stockName {
  color: $grey0;

  @include typography(h7);

  @include respond(phone) {
    @include typography(h6, semibold);
  }
}

.noWrap {
  white-space: nowrap;
}

.rowDivider {
  > div > *:first-child {
    padding-bottom: .5rem;

    @include respond(phone) {
      padding-bottom: 0;
    }
  }
}

.rowControls {
  display: flex;
  justify-content: space-between;
  background-color: $grey5;
  padding: .5rem 2rem;
  position: absolute;
  width: 100%;
  height: 3rem;

  @include respond(phone) {
    position: static;
    height: auto;
    padding: 1rem 2rem;
  }

  > div {
    display: flex;
    align-items: baseline;
    white-space: nowrap;
    gap: .5rem;

    > div:last-child {
      color: $grey0;

      @include typography(h8);
    }
  }

  button {
    @include respond(phone) {
      /* stylelint-disable-next-line declaration-no-important */
      background-color: $grey5 !important;
    }
  }
}

.column {
  display: flex;
  flex-direction: column;
  gap: .5rem;
}

.mobileMarketDepthContainer {
  display: flex;
  justify-content: space-between;
  padding: 0 2.5rem 0 1.5rem;
  position: absolute;
  width: 100%;
  height: 27.5rem;
  background-color: $default;

  @include respond(phone) {
    padding: 0;
    margin-top: 4rem;
  }

  > div {
    position: static;
    height: auto;

    > div > div {
      width: auto;
    }
  }
}

.amo {
  color: $grey3;
  margin-right: 1rem;
}

.alignContent {
  justify-content: space-between;
}

.messageWrapper {
  padding: 1rem;

  @include typography(h7);

  .statusIcon {
    margin-right: 1rem;
    /* stylelint-disable-next-line declaration-no-important */
    height: 1.5rem !important;
  }
}
