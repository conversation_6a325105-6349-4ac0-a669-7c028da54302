import React from 'react';
import PropTypes from 'prop-types';
import { classNames as cx } from '@utils/style';
import { ICON_NAME } from '@common/Icon/enums';
import { ORDER_TYPES } from '@utils/enum';
import { isBracketOrCover, isSLorSLM } from '@pages/Orders/utils';
import Button from '@common/Table/Button';
import MarketDepthContainer from '@modules/Positions/common/MarketDepthContainer';
import DropdownOptions from '@modules/Positions/common/DropdownOptions';
import StackedRow from '@common/Table/StackedRow';
import If from '@common/If';
import { sendEvent } from '@service/AnalyticsService';
import {
  getTime, formatPrice, roundValue, mobileBrowser, isBondInstrument,
} from '@utils';
import { table } from '@commonStyles';
import styles from '../../styles';
import orderStyles from './orders-table-common.scss';
import {
  COLORMAP, CONSTMAP, MORE_OPTIONS, BONDS_MORE_OPTIONS,
} from '../../constants';
import {
  ORDER_ACTION_TYPES, ORDER_STATUS_BUCKETS, ORDER_STATUS,
} from '../../../OrderDetails/enums';
import RedirectToCharts from '../RedirectToCharts';

export function getOrderPrice(orderType, price, tabStatus, avgPrice) {
  if ((tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key)
    && ([ORDER_TYPES.MKT, ORDER_TYPES.SLM].indexOf(orderType) !== -1)) {
    return formatPrice(avgPrice);
  }
  if ([ORDER_TYPES.MKT, ORDER_TYPES.SLM].indexOf(orderType) === -1) return formatPrice(price);
  return 'MKT';
}

export function getTriggerPriceString(orderType, trigger_price) {
  if (isSLorSLM(orderType)) return `(Tgr:${formatPrice(trigger_price)})`;
  return null;
}

export function getPrice(orderType, price, status, avgPrice, trigger_price) {
  const bucketStatus = Object.values(ORDER_STATUS_BUCKETS)
    .find((data) => data.values.find((value) => value === status));

  return `${getOrderPrice(orderType, price, bucketStatus.key, avgPrice)} ${getTriggerPriceString(orderType, trigger_price) || ''}`;
}

export function getOrderValue({
  order_type, quantity, price, avg_traded_price,
}, ltp, tabStatus) {
  if (tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key) return quantity * avg_traded_price;
  if ([ORDER_TYPES.MKT, ORDER_TYPES.SLM].indexOf(order_type) === -1) {
    return quantity * price;
  }
  if (ltp) return roundValue(ltp) * quantity;
  return '';
}

export function getProductTypeStatus(productType) {
  if (isBracketOrCover(productType)) return 'Exit';
  return 'Cancel';
}

export function progressPercentage(triggerPrice, orderPrice, orderLtp, currentLtp) {
  if (!triggerPrice) {
    const percentage = ((orderLtp - currentLtp) / (orderLtp - orderPrice)) * 100;
    if (orderPrice === 0) return 100;
    if (percentage < 0) return 0;
    if (percentage > 100) return 100;
    return percentage;
  }
  const percentage = ((orderLtp - currentLtp) / (orderLtp - triggerPrice)) * 100;
  if (percentage < 0) return 0;
  if (percentage > 100) return 100;
  return percentage;
}

export const isAMOorder = (orderStatus) => [ORDER_STATUS.O_PENDING,
  ORDER_STATUS.O_MODIFIED, ORDER_STATUS.O_CANCELLED].indexOf(orderStatus) > -1;

function TableRow({
  ltp, item, openDetailModal, tabStatus, openedMarketDepth, setOpenedMarketDepth, index,
  showControl = -1, setShowControl,
}) {
  const isMarketDepthOpen = openedMarketDepth === index;
  const shouldShowControls = showControl === `${item.order_no}_${item.order_type}_${item.txn_type}`;
  const handleMobileRowClick = (e) => {
    e.stopPropagation();
    setShowControl(shouldShowControls ? -1 : `${item.order_no}_${item.order_type}_${item.txn_type}`);
  };

  if (mobileBrowser()) {
    const price = getPrice(item.order_type, item.price, item.display_status, item.avg_traded_price, item.trigger_price);
    const bottomStartSlots = {
      [ORDER_STATUS.PENDING]: () => (
        <div className={styles.tags}>
          <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
            {item?.txn_type}
          </span>
          <span className={styles.tag}>
            {item.display_product}
          </span>
          {
            isAMOorder(item.status) && (
              <span className={styles.amo}>
                AMO
              </span>
            )
          }
          <span className={styles.qtyFraction}>{`${item?.traded_qty}/${item?.quantity}`}</span>
        </div>
      ),
      [ORDER_STATUS.SUCCESSFUL]: () => (
        <div className={styles.tags}>
          <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
            {item?.txn_type}
          </span>
          <span className={styles.tag}>
            {item.display_product}
          </span>
        </div>
      ),
      [ORDER_STATUS.FAILED]: () => (
        <div className={styles.tags}>
          <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
            {item?.txn_type}
          </span>
          <span className={styles.tag}>
            {item.display_product}
          </span>
        </div>
      ),
      [ORDER_STATUS.CANCELLED]: () => (
        <div className={styles.tags}>
          <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
            {item?.txn_type}
          </span>
          <span className={styles.tag}>
            {item.display_product}
          </span>
        </div>
      ),
    };
    const topEndSlots = {
      [ORDER_STATUS.PENDING]: () => (
        <div className={cx([styles.noWrap, styles.stockName])}>
          { price.includes(ORDER_TYPES.MKT) ? price : `₹ ${price}`}
        </div>

      ),
      [ORDER_STATUS.SUCCESSFUL]: () => (
        <div className={styles.noWrap}>
          <span className={styles.qtyFraction}>Avg. </span>
          {formatPrice(item.avg_traded_price)}
        </div>
      ),
      [ORDER_STATUS.FAILED]: () => (
        <span className={`${orderStyles[COLORMAP[item.display_status]]}`}>
          {item.display_status}
        </span>
      ),
      [ORDER_STATUS.CANCELLED]: () => (
        <span className={`${orderStyles[COLORMAP[item.display_status]]}`}>
          {item.display_status}
        </span>
      ),
    };

    const bottomEndSlots = {
      [ORDER_STATUS.PENDING]: () => (
        <div className={cx([styles.noWrap, styles.qtyFraction])}>{`LIVE ${formatPrice(ltp, 2)}`}</div>

      ),
      [ORDER_STATUS.SUCCESSFUL]: () => (
        <span className={styles.qtyFraction}>{`${item?.traded_qty}/${item?.quantity}`}</span>
      ),
      [ORDER_STATUS.FAILED]: () => (
        <span className={styles.qtyFraction}>{`${item?.traded_qty}/${item?.quantity}`}</span>
      ),
      [ORDER_STATUS.CANCELLED]: () => (
        <span className={styles.qtyFraction}>{`${item?.traded_qty}/${item?.quantity}`}</span>
      ),
    };
    const BottomStartSlot = bottomStartSlots[tabStatus];
    const BottomEndSlot = bottomEndSlots[tabStatus];
    const TopEndSlot = topEndSlots[tabStatus];
    return (
      <>
        <div
          role="presentation"
          onClick={handleMobileRowClick}
          className={cx([styles.tableRow, table.plainTableRow, styles.rowDivider, styles.alignContent], {
            [styles.marketDepthOpen]: isMarketDepthOpen,
          })}
        >
          <StackedRow
            slotTopStart={(
              <div className={styles.stockName}>
                {item.display_name}
                <span className={orderStyles.exchange}>
                  {item?.exchange}
                </span>
              </div>
            )}
            slotTopEnd={<TopEndSlot />}
            slotBottomStart={<BottomStartSlot />}
            slotBottomEnd={<BottomEndSlot />}
          />

        </div>
        {(isMarketDepthOpen)
          && (
            <>
              <div className={styles.dummyRow} />
              <div
                style={{ top: `${6 + 5.75 * index}rem` }}
                className={styles.mobileMarketDepthContainer}
              >
                <MarketDepthContainer
                  {...item}
                  index={index}
                  setOpenedMarketDepth={setOpenedMarketDepth}
                />
              </div>
            </>
          )}
        {
          openDetailModal && shouldShowControls
          && (
            <>
              <div className={styles.dummyRow1} />
              <div className={styles.rowControls}>
                {
                  ([ORDER_STATUS_BUCKETS.FAILED.key, ORDER_STATUS_BUCKETS.CANCELLED.key].indexOf(tabStatus) > -1)
                  && (
                    <div>
                      <Button
                        onClick={(e) => {
                          if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                          e.stopPropagation();
                          sendEvent({
                            event_category: 'order',
                            event_action: 'order_entry_from_orderbook',
                            event_label: 'orders',
                            vertical_name: 'stocks',
                            screenName: '/stocks_orders',
                          });
                          sendEvent({
                            event_category: 'order',
                            event_action: 'order_entry_from_orderbook_retry',
                            event_label: 'orders',
                            vertical_name: 'stocks',
                            screenName: '/stocks_orders',
                          });
                          openDetailModal({
                            data: item,
                            orderAction: ORDER_ACTION_TYPES.REPEAT,
                          });
                        }}
                        icon={ICON_NAME.RETRY}
                      />
                      <div>Retry</div>
                    </div>
                  )
                }
                {
                  tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key
                  && (
                    <div>
                      <Button
                        onClick={(e) => {
                          if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                          e.stopPropagation();
                          sendEvent({
                            event_category: 'order',
                            event_action: 'order_entry_from_orderbook',
                            event_label: 'orders',
                            vertical_name: 'stocks',
                            screenName: '/stocks_orders',
                          });
                          sendEvent({
                            event_category: 'order',
                            event_action: 'order_entry_from_orderbook_repeat',
                            event_label: 'orders',
                            vertical_name: 'stocks',
                            screenName: '/stocks_orders',
                          });
                          openDetailModal({
                            data: item,
                            orderAction: ORDER_ACTION_TYPES.REPEAT,
                          });
                        }}
                        icon={ICON_NAME.REPEAT}
                      />
                      <div>Repeat</div>
                    </div>
                  )
                }
                {
                  tabStatus === ORDER_STATUS_BUCKETS.PENDING.key
                  && (
                    <>
                      <div>
                        <Button
                          message={getProductTypeStatus(item?.product)}
                          onClick={(e) => {
                            if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                            e.stopPropagation();
                            openDetailModal({
                              data: item,
                              orderAction: ORDER_ACTION_TYPES.CANCEL,
                            });
                          }}
                          icon={ICON_NAME.CANCEL}
                        />
                        <div>{getProductTypeStatus(item?.product)}</div>
                      </div>
                      <div>
                        <Button
                          onClick={(e) => {
                            if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                            e.stopPropagation();
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook_modify',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            openDetailModal({
                              data: item,
                              orderAction: ORDER_ACTION_TYPES.MODIFY,
                            });
                          }}
                          icon={ICON_NAME.EDIT}
                        />
                        <div>Modify</div>
                      </div>
                    </>
                  )
                }
                <div>
                  <Button
                    onClick={(e) => {
                      if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                      e.stopPropagation();
                      openDetailModal({
                        data: item,
                        orderAction: ORDER_ACTION_TYPES.DETAILS,
                      });
                    }}
                    icon={ICON_NAME.INFO}
                  />
                  <div>Details</div>
                </div>
                <div>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      setOpenedMarketDepth(isMarketDepthOpen ? -1 : index);
                    }}
                    icon={ICON_NAME.ORDERS_MARKET_DEPTH}
                  />
                  <div>Market Depth</div>
                </div>
              </div>
            </>
          )
        }
      </>
    );
  }

  const isBond = isBondInstrument(item.instrument_type);

  return (
    <>
      <div
        role="presentation"
        onClick={() => {
          if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
          openDetailModal({
            data: item,
            orderAction: ORDER_ACTION_TYPES.DETAILS,
          });
        }}
        className={cx([styles.tableRow, table.stripedFlatTableRow], {
          [styles.marketDepthOpen]: isMarketDepthOpen,
        })}
      >
        <div>
          {
            getTime({ dateTime: item.order_date_time, format: 'HH:mm' })
          }
        </div>
        <div className={`${table.rowControlsHolder}`}>
          <div className={styles.name}>
            <div className={orderStyles.displayName}>
              <span className={(item.txn_type === CONSTMAP.BUY) ? orderStyles.buy : orderStyles.sell}>
                {item?.txn_type}
              </span>
              <span className={styles.stockName}>
                {item.display_name}
                <span className={orderStyles.exchange}>
                  {item?.exchange}
                </span>
              </span>
              {
                openDetailModal
                && (
                  <div className={table.rowControls}>
                    {
                      ([ORDER_STATUS_BUCKETS.FAILED.key, ORDER_STATUS_BUCKETS.CANCELLED.key].indexOf(tabStatus) > -1)
                      && (
                        <Button
                          message="Retry"
                          onClick={(e) => {
                            if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                            e.stopPropagation();
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook_retry',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            openDetailModal({
                              data: item,
                              orderAction: ORDER_ACTION_TYPES.REPEAT,
                            });
                          }}
                          icon={ICON_NAME.RETRY}
                        />
                      )
                    }
                    {
                      tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key
                      && (
                        <Button
                          message="Repeat"
                          onClick={(e) => {
                            if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                            e.stopPropagation();
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            sendEvent({
                              event_category: 'order',
                              event_action: 'order_entry_from_orderbook_repeat',
                              event_label: 'orders',
                              vertical_name: 'stocks',
                              screenName: '/stocks_orders',
                            });
                            openDetailModal({
                              data: item,
                              orderAction: ORDER_ACTION_TYPES.REPEAT,
                            });
                          }}
                          icon={ICON_NAME.REPEAT}
                        />
                      )
                    }
                    {
                      tabStatus === ORDER_STATUS_BUCKETS.PENDING.key
                      && (
                        <>
                          <Button
                            message={getProductTypeStatus(item?.product)}
                            onClick={(e) => {
                              if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                              e.stopPropagation();
                              openDetailModal({
                                data: item,
                                orderAction: ORDER_ACTION_TYPES.CANCEL,
                              });
                            }}
                            icon={ICON_NAME.CANCEL}
                          />
                          <Button
                            message="Modify"
                            onClick={(e) => {
                              if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                              e.stopPropagation();
                              sendEvent({
                                event_category: 'order',
                                event_action: 'order_entry_from_orderbook',
                                event_label: 'orders',
                                vertical_name: 'stocks',
                                screenName: '/stocks_orders',
                              });
                              sendEvent({
                                event_category: 'order',
                                event_action: 'order_entry_from_orderbook_modify',
                                event_label: 'orders',
                                vertical_name: 'stocks',
                                screenName: '/stocks_orders',
                              });
                              openDetailModal({
                                data: item,
                                orderAction: ORDER_ACTION_TYPES.MODIFY,
                              });
                            }}
                            icon={ICON_NAME.EDIT}
                          />
                        </>
                      )
                    }
                    <Button
                      message="Details"
                      onClick={(e) => {
                        if (!isMarketDepthOpen) setOpenedMarketDepth(-1);
                        e.stopPropagation();
                        openDetailModal({
                          data: item,
                          orderAction: ORDER_ACTION_TYPES.DETAILS,
                        });
                      }}
                      icon={ICON_NAME.INFO}
                    />
                    <Button
                      message="Market Depth"
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenedMarketDepth(isMarketDepthOpen ? -1 : index);
                      }}
                      icon={ICON_NAME.ORDERS_MARKET_DEPTH}
                    />
                    <If test={!isBond}>
                      <RedirectToCharts scrip={item} />
                    </If>
                    {isBond ? (
                      <DropdownOptions
                        options={(tabStatus === ORDER_STATUS_BUCKETS.PENDING.key
                        || tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key)
                          ? BONDS_MORE_OPTIONS : BONDS_MORE_OPTIONS.slice(0, 2)}
                        {...item}
                      />
                    )
                      : (
                        <DropdownOptions
                          options={(tabStatus === ORDER_STATUS_BUCKETS.PENDING.key
                          || tabStatus === ORDER_STATUS_BUCKETS.SUCCESSFUL.key)
                            ? MORE_OPTIONS : MORE_OPTIONS.slice(0, 3)}
                          {...item}
                        />
                      )}
                  </div>
                )
              }
            </div>
            {
              isAMOorder(item.status) && (
                <div className={styles.offMarket}>
                  AMO
                </div>
              )
            }
          </div>
        </div>
        <div>
          {item.display_product}
        </div>
        {
          (tabStatus === ORDER_STATUS.PENDING || tabStatus === ORDER_STATUS.SUCCESSFUL)
          && (
            <div>
              {`₹ ${formatPrice(getOrderValue(item, ltp, tabStatus))}`}
            </div>
          )
        }
        <div>{`${item?.traded_qty} / ${item?.quantity}`}</div>
        {
          tabStatus === ORDER_STATUS.PENDING
          && (
            <div>{formatPrice(ltp)}</div>
          )
        }
        {
          tabStatus === ORDER_STATUS.SUCCESSFUL && (
            <div>{formatPrice(item.avg_traded_price)}</div>
          )
        }
        <div>
          {getPrice(item.order_type, item.price, item.display_status, item.avg_traded_price, item.trigger_price)}
        </div>
        {tabStatus === ORDER_STATUS.PENDING ? (
          <div>
            <div className={orderStyles.progressBar}>
              <div style={{ width: `${progressPercentage(item.trigger_price, item.price, item.ref_ltp, ltp, item.order_type)}%` }} />
            </div>
          </div>
        ) : (
          <div className={`${orderStyles[COLORMAP[item.display_status]]}`}>
            {item.display_status}
          </div>
        )}
      </div>
      {(isMarketDepthOpen)
        && (
          <>
            <div className={styles.dummyRow} />
            <MarketDepthContainer
              {...item}
              index={index}
              top={`${8 + 4.5 * index}rem`}
              setOpenedMarketDepth={setOpenedMarketDepth}
            />
          </>
        )}
    </>
  );
}

TableRow.propTypes = {
  item: PropTypes.shape({}).isRequired,
};

export default TableRow;
