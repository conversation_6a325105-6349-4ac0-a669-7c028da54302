import React, {
  useMemo, useContext, useCallback, useState, useEffect,
} from 'react';
import Table from '@common/Table';
import { DataFeedContext } from '@layout/App/DataFeedContext';
import { REQUEST_TYPES, RESPONSE_TYPES } from '@service/dataConfig';
import {
  EXCHANGE_CODE, ROUTE_NAME, SEGMENT_TYPES, ORDERS_SORT,
} from '@utils/enum';
import useSort from '@common/useSort';
import { TYPE } from '@common/SkeletonLoader/enums';
import SkeletonLoader from '@common/SkeletonLoader';
import WithLoader from '@common/WithLoader';
import { ICON_NAME, ICON_SVG } from '@common/Icon/enums';
import EmptyState from '@common/EmptyState';
import { useHistory } from 'react-router-dom';
import { COMMON_PAGE_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';
import LocalStorage from '@service/LocalStorage';
import If from '@common/If';
import SleekCard from '@common/SleekCard';
import { getDataTheme } from '@utils/style';
import { getCurrentTime } from '@layout/MarginPledge/api';
import { mobileBrowser, formatTimestampTo24Hours } from '@utils';
import styles from './styles';
import TableRow, { getOrderValue } from './partials/TableRow';
import {
  PENDING_TABS, SUCCESSFUL_TABS, FAILED_TABS, AWARENESS_CONFIG,
} from './constants';
import { EMPTY_STATES } from '../constants';
import { ORDER_STATUS } from '../OrderDetails/enums';
import Routes from '../../../../../routes';

const requiredFeedResponse = [RESPONSE_TYPES.TRADE];

function OrderDataTable({
  tabStatus, orderData = [], orderActionModal, openedMarketDepth, setOpenedMarketDepth,
  setShowControl, showControl,
}) {
  const { dataFeed } = useContext(DataFeedContext);

  const [isPendingWarnVisible, setPendingWarnVisible] = useState(false);

  const transformOrderData = useCallback((latestFeed, initialData) => {
    if (latestFeed?.lastTradePrice) {
      const { lastTradePrice } = latestFeed;
      return ({
        ...initialData,
        current_price: lastTradePrice,
        order_value: getOrderValue(initialData,
          lastTradePrice,
          tabStatus),
      });
    }
    return initialData;
  }, [tabStatus]);

  const requiredStreams = useMemo(() => ({ exchange, security_id, segment = SEGMENT_TYPES.CASH }) => dataFeed
    ?.getStream(REQUEST_TYPES.STOCK,
      [EXCHANGE_CODE[segment][exchange],
        parseInt(security_id, 10)],
      requiredFeedResponse)[0].feed, [dataFeed]);

  function getRowsOptions() {
    switch (tabStatus) {
      case ORDER_STATUS.SUCCESSFUL: return SUCCESSFUL_TABS;
      case ORDER_STATUS.FAILED:
      case ORDER_STATUS.CANCELLED: return FAILED_TABS;
      default: return PENDING_TABS;
    }
  }

  const {
    data, activeSort, handleSorting,
  } = useSort({
    rawData: orderData,
    requiredFeedFxn: requiredStreams,
    transformFxn: transformOrderData,
    defaultSort: LocalStorage.get(ORDERS_SORT, false),
  });

  const cardConfig = {
    ...AWARENESS_CONFIG,
    ...{
      preIconClass: styles.statusIcon,
      icon: ICON_SVG[getDataTheme()][ICON_NAME.PENDING],
    },
  };

  const isTimeWithinRange = (current) => {
    const { START_TIME, END_TIME } = AWARENESS_CONFIG.timeRange;
    const currentTime = formatTimestampTo24Hours(current);
    return currentTime >= START_TIME && currentTime < END_TIME;
  };

  useEffect(() => {
    (async () => {
      const { current } = await getCurrentTime();
      const value = tabStatus === ORDER_STATUS.PENDING && isTimeWithinRange(current);
      setPendingWarnVisible(value);
    })();
  }, [tabStatus]);

  return (
    <div className={styles.container}>
      <If test={isPendingWarnVisible}>
        <SleekCard
          cardConfig={cardConfig}
          setShowMessage={() => setPendingWarnVisible(false)}
          className={styles.messageWrapper}
        />
      </If>
      <Table
        options={getRowsOptions()}
        active={activeSort}
        onClick={(selectedTab) => {
          LocalStorage.set(ORDERS_SORT, selectedTab, '', false);
          handleSorting(selectedTab);
        }}
        className={styles.tableRow}
        showHeader={!mobileBrowser()}
      >
        {
          data.map((item, i) => (
            <TableRow
              ltp={item?.current_price}
              item={item}
              key={i}
              index={i}
              openDetailModal={orderActionModal}
              tabStatus={tabStatus}
              openedMarketDepth={openedMarketDepth}
              setOpenedMarketDepth={setOpenedMarketDepth}
              showControl={showControl}
              setShowControl={setShowControl}
            />
          ))
        }
      </Table>
    </div>
  );
}

function LoadingComponent() {
  return (
    <Table options={PENDING_TABS} className={styles.loaderTableRow} isNested showHeader={!mobileBrowser()}>
      <SkeletonLoader type={TYPE.TABLE} rows={5} columns={mobileBrowser() ? 2 : 8} className={styles.loaderTableRow} />
    </Table>
  );
}

function EmptyComponent({ noOrders, tabStatus }) {
  const history = useHistory();
  return (
    <EmptyState
      text={noOrders ? EMPTY_STATES.NO_ORDERS.heading : EMPTY_STATES[tabStatus].heading}
      subText={noOrders ? EMPTY_STATES.NO_ORDERS.message : EMPTY_STATES[tabStatus].message}
      ctaText="Place Order"
      iconName={ICON_NAME.EMPTY_ORDERS}
      isFullScreen={false}
      className={styles.emptyContainer}
      onCtaClick={() => {
        history.push(Routes[ROUTE_NAME.MOVERS].url);
        sendEvent({
          event_category: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_CATEGORY,
          event_action: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_ACTION,
          event_label: COMMON_PAGE_GA.PLACE_ORDER_CLICKED.EVENT_LABEL('orders'),
        });
      }}
    />
  );
}

const OrderDataTableHOC = WithLoader({
  WrappedComponent: OrderDataTable,
  LoadingComponent,
  EmptyComponent,
});

export default OrderDataTableHOC;
