import {
  useState, useEffect, useMemo, useContext, useCallback, useRef,
} from 'react';
import { useCallbackForEvents } from '@utils/react';
import isEmpty from 'lodash/isEmpty';
import { useParams, useHistory, useLocation } from 'react-router-dom';
import useGetApi from '@common/UseApi/useGetApi';
import { getUrlParameter } from '@utils/index';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { UserEventContext, RECENT_TYPE } from '@layout/App/UserEventContext';
import usePlaceOrder from '@common/usePlaceOrder';
import { useModal } from '@layout/App/ModalContext';
import { UserContext, useIrData } from '@layout/App/UserContext';
import { TRANSACTION_TYPES, INSTRUMENTS } from '@utils/enum';
import { userAllowedStatus } from '@layout/App/api';
import { getInvestcareNudge } from '@modules/OrderFlow/PlaceOrder/InvestCare/api';
import GttAddEditModal from '@modules/GTT/GttAddEditModal';
import { getExpiryDate } from '@modules/GTT/api';
import useSipModal from '@pages/StockInstructions/StockSips/partials/useSipModal';
import AddPriceAlert from '@modules/PriceAlerts/partials/AddAlert';
import { SURVEILLANCE_INDICATOR_BLOG } from '@modules/OrderFlow/PlaceOrder/InvestCare/enums';
import { sendEvent } from '@service/AnalyticsService';
import { getCompanyData } from './api';

export const DEEP_LINKS = {
  ORDER: 'place-order',
  GTT: 'place-gtt',
  SIP: 'place-sip',
  PRICE_ALERT: 'add-price-alert',
};

function useSecurity() {
  const { userId, showBasketOrder } = useContext(UserContext);
  const [showInfoData, setShowInfoData] = useState({});
  const [investcareData, setInvestcareData] = useState(null);
  const { makeRequest, inProgress, failed } = useGetApi();
  const { makeRequest: nudgeMakeRequest } = useGetApi();
  const priceInputRef = useRef(null);
  const { openModal } = useModal();
  const history = useHistory();
  const { search } = history.location;
  const id = useParams()?.id || getUrlParameter('id', search);
  const [data, setData] = useState(null);
  const [expiryDate, setExpiryDate] = useState();
  const location = useLocation();
  const { addRecentSecurity } = useContext(UserEventContext);
  const { irStatus } = useIrData();
  const [isAllowedUser, setAllowedUser] = useState(false);
  const { equityCardInfo } = useLoggedInContext();

  const fullScreen = useMemo(() => getUrlParameter('fullScreen', history.location.search) === 'true', [history.location.search]);

  const getInProgressStatus = () => {
    if (fullScreen && data) {
      return false;
    } return !!inProgress;
  };

  useEffect(() => {
    userAllowedStatus(userId).then(({ isUserAllowed }) => {
      setAllowedUser(isUserAllowed);
    }).catch(() => {});
  }, [makeRequest, userId]);

  const getPmlData = useCallbackForEvents((pmlId) => {
    if (pmlId) {
      makeRequest(getCompanyData(pmlId)).then(({ data: { results } }) => {
        setData({ ...results[0] });
        addRecentSecurity(RECENT_TYPE.VIEWED, results[0]);
        addRecentSecurity(RECENT_TYPE.SEARCHED, results[0]);
      }).catch();
    }
  }, [addRecentSecurity, makeRequest, id]);

  useEffect(() => {
    getPmlData(id);
  }, [getPmlData, id]);

  useEffect(() => {
    if (data && data.segment) {
      try {
        nudgeMakeRequest(
          getInvestcareNudge(data.security_id, data.segment, data.exchange, data.isin),
        ).then((res) => {
          setShowInfoData(res.data);
        });
      } catch (err) {}
    }
  }, [data, nudgeMakeRequest]);

  useEffect(() => {
    if (showInfoData) {
      const {
        is_t2t_scrip, is_re_scrip, is_surveillance_indicator, surveillance_desc, is_ban,
      } = showInfoData;
      const investcareCard = [];
      if (!isEmpty(equityCardInfo)) {
        if (is_re_scrip) {
          investcareCard.push(equityCardInfo.company_T2T);
          investcareCard.push(equityCardInfo.company_RE);
        } else if (is_t2t_scrip) {
          investcareCard.push(equityCardInfo.company_T2T);
        }
        if (is_ban) {
          investcareCard.push({
            cta_link: SURVEILLANCE_INDICATOR_BLOG,
            message: 'This security is in "Ban for trade".',
          });
        }
        if (is_surveillance_indicator) {
          investcareCard.push({
            cta_link: SURVEILLANCE_INDICATOR_BLOG,
            message: `Security is under surveillance measure ${surveillance_desc}`,
          });
        }
      }
      setInvestcareData(investcareCard.length ? investcareCard : null);
    }
  }, [equityCardInfo, showInfoData]);

  const {
    name, exchange, security_id: securityId, tick_size, lot_size: lotSize,
    isin, instrument_type: instrumentType, segment, exch_symbol, bse_listed_flag,
  } = data || {};

  const { openAddNewSipModal } = useSipModal({
    stockId: id,
    companyName: name,
    exchange,
    securityId,
    isin,
  });

  const productType = getUrlParameter('product', history.location.search);
  const { buy, sell } = usePlaceOrder(name, exchange, securityId, {
    isin, lotSize, segment, tickSize: tick_size / 100, instrumentType, productType, exch_symbol,
  });
  const getProps = useCallback(
    (trigger_type, transactionType) => ({
      exchange,
      id,
      pml_id: id,
      lot_size: lotSize,
      tick: tick_size / 100,
      instrument_type: instrumentType,
      name,
      segment,
      isin,
      security_id: securityId,
      expiry_date: expiryDate,
      priceInputRef,
      trigger_type,
      transaction_type: transactionType,
    }),
    [exchange, expiryDate, id, instrumentType, isin, lotSize, name, securityId, segment, tick_size],
  );

  useEffect(() => {
    const action = getUrlParameter('action', search);
    if (action === DEEP_LINKS.GTT) {
      getExpiryDate(id).then(
        ({ data: { expiry_date } = {} }) => setExpiryDate(expiry_date),
      ).catch(() => {});
    }
  }, [id, search]);

  useEffect(() => {
    const action = getUrlParameter('action', search);
    const transactionType = getUrlParameter('txn_type', search);
    const trigger_type = getUrlParameter('trigger_type', search).toUpperCase();

    if (action === DEEP_LINKS.ORDER && !inProgress && irStatus) {
      const orderType = getUrlParameter('order_type', search);
      const initialQuantity = getUrlParameter('quantity', search);
      const initialPrice = getUrlParameter('price', search);
      const initialTriggerPrice = getUrlParameter('trigger_price', search);
      const initialStoplossValue = getUrlParameter('stoploss_value', search);
      const initialTargetValue = getUrlParameter('profit_value', search);
      const channel = getUrlParameter('channel', search) || null;

      if (transactionType === TRANSACTION_TYPES.SELL) {
        sell({
          initialQuantity,
          orderType,
          initialPrice,
          initialTriggerPrice,
          initialTargetValue,
          initialStoplossValue,
          channel,
        });
      } else {
        buy({
          initialQuantity,
          orderType,
          initialPrice,
          initialTriggerPrice,
          initialTargetValue,
          initialStoplossValue,
          channel,
        });
      }
    } else if (action === DEEP_LINKS.GTT && !inProgress && irStatus
      && !([INSTRUMENTS.OPTIDX, INSTRUMENTS.OPTSTK, INSTRUMENTS.FUTIDX,
        INSTRUMENTS.FUTSTK].indexOf(instrumentType) > -1 && bse_listed_flag)) {
      sendEvent({
        event_category: 'gtt',
        event_action: 'gtt_ordersview_postorderplacement',
        event_label: 'gtt',
        vertical_name: 'stocks',
        screenName: '/gtt',
      });
      openModal({
        Component: GttAddEditModal,
        componentProps: getProps(trigger_type, transactionType),
      });
    } else if (action === DEEP_LINKS.SIP && !inProgress && irStatus) {
      openAddNewSipModal();
      history.replace(location.pathname);
    } else if (action === DEEP_LINKS.PRICE_ALERT && !inProgress && irStatus) {
      openModal({
        Component: AddPriceAlert,
        componentProps: getProps(),
        onOpenAnimationEnd: () => priceInputRef.current && priceInputRef.current.focus(),
      });
      history.replace(location.pathname);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, inProgress, sell, buy, irStatus, openModal, getProps]);

  return {
    data,
    inProgress: getInProgressStatus(),
    failed,
    chartsIsLoading: inProgress,
    isAllowedUser,
    showBasketOrder,
    investcareData,
  };
}

export default useSecurity;
