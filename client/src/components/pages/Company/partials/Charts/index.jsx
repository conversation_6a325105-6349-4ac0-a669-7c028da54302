import React from 'react';

import { classNames as cx } from '@utils/style';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { STATICS } from '@pages/Company/partials/Charts/ENUMS';
import If from '@common/If';
import { Percent } from '@common/Prices';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import ErrorView from '@common/WithLoader/ErrorView';
import CONFIG from '../../../../../config';
import RangeControls from './partials/RangeControls';
import FooterControls from './partials/FooterControls';
import ToolsBar from './partials/ToolsBar';
import SideBar from './partials/SideBar';
import ScripActions from './partials/ScripActions';
import styles from './index.scss';

function Charts({
  showScripActions = false,
  chartIndices = false,
  hideShadow = false,
  hideRange = false,
  corporateActions = false,
  containerClass,
}) {
  const { theme, selectedChart } = useLoggedInContext();
  const ctx = useCharts();
  const {
    toggleDetails,
    activeRange,
    isError,
    handleRange,
    chartsRef,
    iframeRef,
    fullScreen,
    rangeValue,
    inProgress,
  } = ctx;

  const renderControls = () => {
    if (toggleDetails) {
      return <ToolsBar />;
    }

    return (
      <div
        className={cx(styles.rangeControls, {
          [styles.scripActions]: !chartIndices,
          [styles.chartIndices]: chartIndices,
        })}
      >
        {!chartIndices && showScripActions && <ScripActions />}
        {!chartIndices && !showScripActions && (
          <span className={styles.rangeContainer}>
            <span>{STATICS.Returns}</span>
            <span>
              {rangeValue && !inProgress && <Percent value={rangeValue} className={styles.returnsValue} withSign />}
            </span>
          </span>
        )}
        <RangeControls setChartRange={handleRange} activeRange={activeRange} />
      </div>
    );
  };

  if (isError) {
    return <ErrorView />;
  }

  const iframeSrc = `${CONFIG.IFRAME_CHARTS}/equity-charts/?theme=${theme}`;

  return (
    <div
      ref={chartsRef}
      className={cx(styles.container, {
        [containerClass]: containerClass,
        [styles.shadow]: !hideShadow && !toggleDetails,
        [styles.toggleContainer]: toggleDetails,
        [styles.fullScreenContainer]: fullScreen,
        [styles.corporateActions]: corporateActions,
      })}
    >
      {!hideRange && renderControls()}
      <div
        className={cx(styles.mainView, {
          [styles.mainViewFullScreen]: fullScreen,
          [styles.homeScreenChart]: chartIndices,
        })}
      >
        <div
          className={cx(styles.iframeContainer, {
            [styles.iframeContainerToggled]: toggleDetails,
          })}
        >
          {inProgress && (
            <div className={styles.loader}>
              <SkeletonLoader
                type={TYPE.CHARTS}
                showScripActions={showScripActions}
              />
            </div>
          )}
          {fullScreen && <ScripActions />}
          <iframe
            className={styles.iframe}
            ref={iframeRef}
            id="chart"
            src={iframeSrc}
            title="Charts"
          />
          <If test={toggleDetails && selectedChart === SELECT_CHART_MODES[0].id}>
            <FooterControls />
          </If>
        </div>
        {fullScreen && <SideBar />}
      </div>
    </div>
  );
}

export default Charts;
