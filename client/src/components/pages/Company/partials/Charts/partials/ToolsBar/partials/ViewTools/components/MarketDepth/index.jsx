import React, { useContext } from 'react';
import Popper from '@common/Popper';
import { DraggableModalContext } from '@common/usePlaceOrder';
import PerformanceData from '@pages/Company/partials/Overview/partials/Performance/PerformaceData';
import MarketDepthLayout from '@modules/WatchlistSidebar/partials/MarketDepthLayout';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { VIEW_TOOLS_TITLES, STATICS } from '@pages/Company/partials/Charts/ENUMS';
import { ICON_NAME } from '@common/Icon/enums';
import usePerformanceData, { getFormattedDataForCard } from '@pages/Company/partials/Overview/partials/Performance/usePerformanceData';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';
import styles from './index.scss';
import commonStyles from '../../../../index.scss';

function MarketDepth() {
  const { openDraggableModal } = useContext(DraggableModalContext);
  const {
    securityId, exchange, segment, stockName, isin, tick_size, lot_size, expiry_display_date, instrumentType,
    exch_symbol,
  } = useCharts();

  const performanceData = usePerformanceData({ exchange, securityId, segment });

  return (
    <div>
      <Popper
        title={VIEW_TOOLS_TITLES.MARKET_DEPTH}
        className={styles.popper}
        iconName={ICON_NAME.MARKET_DEPTH}
        iconSize={4}
        useCapture={false}
        titleClassName={commonStyles.title}
        onToggle={(isOpen) => {
          if (isOpen) {
            sendEvent({
              event_category: ADV_CHARTS_GA.MARKET_DEPTH_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.MARKET_DEPTH_CLICKED.EVENT_ACTION,
            });
          }
        }}
        iconStyles={styles.titleIconStyle}
      >
        <div className={styles.wrapper}>
          <MarketDepthLayout
            securityId={securityId}
            exchange={exchange}
            segment={segment}
            name={stockName}
            isin={isin}
            tickSize={tick_size}
            lotSize={lot_size}
            instrumentType={instrumentType}
            exch_symbol={exch_symbol}
            openDraggableModal={openDraggableModal}
          />
          <p className={styles.performanceHeader}>{STATICS.PERFORMANCE}</p>
          <PerformanceData data={getFormattedDataForCard({
            ...performanceData,
            expiryDate: expiry_display_date,
          }, segment)}
          />
        </div>
      </Popper>
    </div>
  );
}

export default MarketDepth;
