import React from 'react';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';

const RefreshCharts = () => {
  const { handleRefreshCharts } = useCharts();
  return (
    <Icon
      onIconClick={handleRefreshCharts}
      name={ICON_NAME.REFRESH_CHARTS}
      size={4}
    />
  );
};

export default RefreshCharts;
