@import '../../../../../../../../../../../styles/main';

.popper {
  width: 18rem;
  background-color: $default;
  left: -12%;
  top: 2.8rem;
}

.wrapper {
  padding: 1rem 0;

  @include dropDownArrow;

  &::before {
    left: 10%;
  }
}

.charts {
  display: flex;
  padding: 1rem 1.5rem;

  > *:first-child {
    margin-right: 1rem;
  }
}

.header {
  color: $grey1;
  padding: 1.5rem 1rem .5rem;
  display: block;

  @include typography(h7, bold);
}

.advancedCharts {
  display: flex;
  padding: 1rem;
  justify-content: space-between;
  color: $grey2;

  @include typography(h7);

  &:hover {
    background-color: $grey4;
  }

  > *:nth-child(2) {
    cursor: pointer;
  }
}

.active > div {
  color: $secondary-color;

  path[fill^='#'],
  circle[fill^='#'],
  rect { // scss-lint:disable QualifyingElement
    fill: $secondary-color;
  }

  circle[stroke^='#'],
  rect,
  polyline,
  path { // scss-lint:disable QualifyingElement
    stroke: $secondary-color;
  }
}

.dropDownIcon > div > *:first-child {
  border-color: $secondary-color;
}
