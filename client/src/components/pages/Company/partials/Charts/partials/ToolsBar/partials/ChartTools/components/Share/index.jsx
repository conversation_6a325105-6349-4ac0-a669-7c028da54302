import React, { useEffect } from 'react';

import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { MODAL_TYPES } from '@common/Modal/enums';
import { useModal } from '@layout/App/ModalContext';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import ShareModal from '@common/ShareModal';

const Share = () => {
  const { handleShare, shareChartId, setShareChartId } = useCharts();
  const { openModal, closeModal } = useModal();

  useEffect(() => {
    if (shareChartId) {
      let shareUrl = null;
      if (window.location.href.includes('fullScreen=')) {
        shareUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}?toggleDetails=true&fullScreen=true&shareChart=${shareChartId}`);
      } else {
        shareUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}?toggleDetails=true&shareChart=${shareChartId}`);
      }
      const shareChartSubject = 'Paytm Money Share Charts';
      openModal({
        Component: ShareModal,
        componentProps: { shareUrl, shareChartSubject, closeModal },
        type: MODAL_TYPES.LARGE_POPUP,
        disableClose: true,
      });
      setShareChartId(null);
    }
  }, [openModal, closeModal, shareChartId, setShareChartId]);

  return (
    <div onClick={handleShare} role="presentation">
      <Icon
        name={ICON_NAME.SHARE}
        size={4}
      />
    </div>
  );
};

export default Share;
