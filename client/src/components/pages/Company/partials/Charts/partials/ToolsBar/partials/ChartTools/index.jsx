import React, { useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';

import Icon from '@common/Icon';
import If from '@common/If';
import { ICON_NAME } from '@common/Icon/enums';
import { MODAL_TYPES } from '@common/Modal/enums';
import { useModal } from '@layout/App/ModalContext';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { sendEvent } from '@service/AnalyticsService';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import Popper from '@common/Popper';
import { TOOLS_TYPE_ID } from '@pages/Company/partials/Charts/ENUMS';

import TimeInterval from './components/TimeInterval';
import Indicator from './components/Indicator';
import Graphs from './components/Graphs';
import Settings from './components/Settings';
import Share from './components/Share';
import TFCIntro from './components/TFCIntro';
import SearchBox from './components/SearchBox';
import styles from './index.scss';
import Layouts from './components/Layouts';
import RefreshCharts from './components/Refresh';
import { TOOLS_DROPDPOWN_CONFIG } from './config';

function ChartTools({ requestFullscreen, fullScreen, hideShare = false }) {
  const {
    handleDrawingTools, isDrawingToolbarActive, displayTFCIntro,
    setTFCIntro, updateToolTypeOpen, toolTypeOpen, handleShare, handleRefreshCharts,
  } = useCharts();
  const { selectedChart } = useLoggedInContext();
  const { openModal, closeModal } = useModal();

  const openInNewTab = () => {
    const link = window.location.href.split('/stocks');
    const fullScreenPage = window.location.href.includes('fullScreen=true');
    const isToggled = window.location.href.includes('toggleDetails=true');
    if (fullScreenPage) {
      window.open(window.location.href, '_blank');
    } else if (isToggled) {
      window.open(`${window.origin}/stocks${link[1]}&fullScreen=true`, '_blank');
    } else {
      window.open(`${window.origin}/stocks${link[1]}?fullScreen=true`, '_blank');
    }
  };

  const proceedToPlace = useCallback(() => {
    closeModal();
    setTFCIntro(false);
  }, [closeModal, setTFCIntro]);

  const handleToolSelection = (toolType) => {
    // Directly invoke bridgeInterface call for share chart and refresh chart
    switch (toolType) {
      case TOOLS_TYPE_ID.SHARE_CHART:
        handleShare();
        break;
      case TOOLS_TYPE_ID.REFRESH_CHART:
        handleRefreshCharts();
        break;
      case TOOLS_TYPE_ID.SETTINGS:
      case TOOLS_TYPE_ID.SAVE_VIEW:
        if (toolTypeOpen === toolType) {
          // Setting to null because if they click on the same tool again, it should close
          updateToolTypeOpen(null);
        } else {
          updateToolTypeOpen(toolType);
        }
        break;
      default:
    }
  };

  useEffect(() => {
    if (displayTFCIntro) {
      openModal({
        type: MODAL_TYPES.SIDEPANE,
        Component: TFCIntro,
        componentProps: { proceedToPlace },
        onOpenAnimationEnd: () => setTFCIntro(false),
      });
    }
  }, [displayTFCIntro, openModal, proceedToPlace, setTFCIntro]);

  return (
    <div className={styles.container}>
      <If test={selectedChart === SELECT_CHART_MODES[0].id}>
        <TimeInterval />
        <Graphs />
        <Indicator />
        <Layouts />
        <div
          onClick={() => {
            handleDrawingTools(!isDrawingToolbarActive);
            sendEvent({
              event_category: ADV_CHARTS_GA.EDIT_CHART_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.EDIT_CHART_CLICKED.EVENT_ACTION,
            });
          }}
          role="presentation"
          className={styles.drawingTools}
        >
          <Icon
            name={ICON_NAME.DRAW}
            size={4}
          />
        </div>
        <If test={!fullScreen}>
          <Popper
            className={styles.popper}
            showDropDownIcon={false}
            iconName={ICON_NAME.MORE_OPTION}
            activeIconName={ICON_NAME.MORE_OPTION}
            iconSize={4.2}
            iconStyles={styles.popperIcon}
            useCapture={false}
            closeOnPopperClick={false}
            handleClose={() => updateToolTypeOpen(null)}
          >
            <div className={styles.mainMenu}>
              {TOOLS_DROPDPOWN_CONFIG.map(({ component, title, toolType }, index) => (
                <div className={styles.menuOptions} key={index} onClick={() => handleToolSelection(toolType)} role="presentation">
                  {component}
                  <div className={styles.menuOptionTitle}>{title}</div>
                </div>
              ))}
            </div>
          </Popper>
        </If>
        <If test={fullScreen}>
          <Layouts isSaveLayout />
          <Settings />
          <If test={!hideShare}><Share /></If>
          <RefreshCharts />
        </If>
      </If>
      <div onClick={openInNewTab} role="presentation" className={styles.fullScreen}>
        <Icon
          name={ICON_NAME.REDIRECTION_ICON}
          size={4}
        />
      </div>
      <div onClick={requestFullscreen} role="presentation" className={styles.fullScreen}>
        <Icon
          name={fullScreen ? ICON_NAME.COLLAPSE : ICON_NAME.EXPAND}
          size={4}
        />
      </div>
      {fullScreen && <SearchBox />}
    </div>
  );
}

ChartTools.propTypes = {
  requestFullscreen: PropTypes.func.isRequired,
  fullScreen: PropTypes.bool.isRequired,
};

export default ChartTools;
