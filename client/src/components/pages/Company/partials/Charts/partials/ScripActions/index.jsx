import React from 'react';

import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import usePlaceOrder from '@common/usePlaceOrder';

import { classNames as cx } from '@utils/style';

import { ChangeIcon, ChangeWithPercent } from '@common/Prices';
import LiveIcon from '@common/LiveIcon';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { INSTRUMENTS } from '@utils/enum';
import { formatPrice } from '@utils/index';

import { button } from '@commonStyles/index';
import styles from './index.scss';

function ScripActions() {
  const ctx = useCharts();
  const { selectedChart } = useLoggedInContext();
  const {
    ltp, change, percentageChange, stockName, securityId, isChartInit,
    segment, exchange, instrumentType, tick_size, isin, lot_size, setTFCView, isAllowedUser,
    exch_symbol,
  } = ctx;
  const { buy, sell } = usePlaceOrder(stockName, exchange, securityId, {
    isin, segment, tickSize: tick_size / 100, lotSize: lot_size, instrumentType, exch_symbol,
  });
  return (
    <div className={styles.container}>
      <div>
        <div className={styles.name}>
          {stockName}
        </div>
        <div className={styles.values}>
          <div className={styles.ltp}>{formatPrice(ltp)}</div>
          <ChangeWithPercent
            value={change}
            percent={percentageChange}
            className={styles.change}
          />
          <ChangeIcon value={percentageChange} />
          <LiveIcon exchange={exchange} segment={segment} showExchange />
        </div>
      </div>
      {
        instrumentType !== INSTRUMENTS.INDEX && (
          <div className={styles.actions}>
            <button
              className={cx([button.btn, button.greenBtnFill, button.btnSmall, styles.btn])}
              onClick={() => buy && buy()}
            >
              Buy
            </button>
            <button
              className={cx([button.btn, button.redBtnFill, button.btnSmall, styles.btn])}
              onClick={() => sell && sell()}
            >
              Sell
            </button>
            {
              isAllowedUser && selectedChart === SELECT_CHART_MODES[0].id
                ? (
                  <button
                    className={cx([button.btn, button.btnSmall, button.secondaryBtnFill, styles.btn, styles.btnTFC],
                      { [styles.disabled]: !isChartInit })}
                    onClick={setTFCView}
                    disabled={!isChartInit}
                  >
                    <Icon name={ICON_NAME.TFC_ICON} size={4} />
                    <span className={styles.tfcButtonLabel}>Trade from Charts</span>
                  </button>
                )
                : ''
}
          </div>
        )
      }
    </div>
  );
}

export default ScripActions;
