@import '../../../../../../../../../styles/main';

.container {
  display: flex;
  align-items: center;

  > * {
    padding: 0 1.2rem;
    color: $grey2;
    cursor: pointer;

    @include typography(h8);
  }

  > *:first-child {
    padding-left: 0;
  }

  > :nth-child(-n+3) {
    border-right: $company-charts-view-separator;
  }
}

.fullScreen {
  cursor: pointer;
  user-select: none;
}

.tab {
  color: $grey2;
  padding: 1rem;
  cursor: pointer;

  @include typography(h7);

  &:hover {
    background-color: $grey4;
  }
}

.active {
  background-color: $grey4;
  color: $grey1;
  cursor: default;

  &:hover {
    background-color: $grey4;
  }
}

.input {
  height: 3rem;
  width: 16rem;
  margin: .5rem 0 1rem;
}

.drawingTools {
  cursor: pointer;
  user-select: none;
}

.mainMenu {
  position: absolute;
  background-color: $default;
  border-radius: .4rem;
  box-shadow: 0 .3rem 1rem 0 $grey3;
  user-select: none;
  width: 11rem;

  @include dropDownArrow();

  .menuOptions {
    color: $grey2;
    display: flex;
    align-items: center;
    padding: 2rem 1rem;
    outline: none;
    height: 2.7rem;
    border-bottom: solid 1px $grey4;

    @include typography(h8);

    .menuOptionTitle {
      margin-left: .5rem;
    }
  }

  &::before {
    left: 35%;
  }
}

.popper {
  > div:first-child {
    top: 1.7rem;
    z-index: 25;
    left: -3.3rem;
  }
}
