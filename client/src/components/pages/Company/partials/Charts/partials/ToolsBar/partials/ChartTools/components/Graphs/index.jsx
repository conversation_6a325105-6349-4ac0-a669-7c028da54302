import React from 'react';

import Popper from '@common/Popper';
import { classNames as cx } from '@utils/style';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { CHART_TYPE, CHARTS_TYPE_ICONS, STATICS } from '@pages/Company/partials/Charts/ENUMS';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';

import commonStyles from '../../../../index.scss';
import commonTabStyles from '../../index.scss';
import styles from './index.scss';

function Graphs() {
  const {
    fullScreen, activeChart, handleChartType, handleAdvancedChartAggregation,
  } = useCharts();
  const { chartConstants } = useLoggedInContext();
  const CHARTS_TYPE_LIST = chartConstants.data?.common?.chartType || [];
  const ADVANCED_CHARTS_TYPE_LIST = chartConstants.data?.common?.advancedChartType || [];

  const handleChartsClick = (chartsObj) => {
    if (chartsObj.value !== activeChart.value) {
      handleChartType(chartsObj);
    }
  };

  const handleAdvancedChartsSettings = (chartsObj) => {
    handleAdvancedChartAggregation(chartsObj);
  };

  return (
    <div>
      <Popper
        title={fullScreen && activeChart.label}
        iconName={CHARTS_TYPE_ICONS[activeChart.value]}
        iconSize={4}
        className={styles.popper}
        titleClassName={commonStyles.title}
        onToggle={(isOpen) => {
          if (isOpen) {
            sendEvent({
              event_category: ADV_CHARTS_GA.CHART_TYPE_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.CHART_TYPE_CLICKED.EVENT_ACTION,
            });
          }
        }}
        message={!fullScreen && activeChart.label}
        containerStyles={!fullScreen && activeChart.label ? styles.active : ''}
        dropDownIconStyles={!fullScreen && activeChart.label ? styles.dropDownIcon : ''}
      >
        <div className={styles.wrapper}>
          {CHARTS_TYPE_LIST.map((chartsObj) => {
            if (chartsObj.value === CHART_TYPE.MOUNTAIN) {
              return null;
            }
            return (
              <div
                role="presentation"
                key={chartsObj.value}
                onClick={() => {
                  handleChartsClick(chartsObj);
                  sendEvent({
                    event_category: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_CATEGORY,
                    event_action: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_ACTION,
                    event_label: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_LABEL(
                      chartsObj.label.toLowerCase(),
                    ),
                  });
                }}
                className={cx([styles.charts, commonTabStyles.tab], {
                  [commonTabStyles.active]: activeChart.value === chartsObj.value,
                })}
              >
                <Icon size={4} name={CHARTS_TYPE_ICONS[chartsObj.value]} />
                <span>{chartsObj.label}</span>
              </div>
            );
          })}
          <div>
            <span className={styles.header}>{STATICS.ADVANCED}</span>
            <div>
              {ADVANCED_CHARTS_TYPE_LIST.map((advancedChartsType) => (
                <div
                  role="presentation"
                  key={advancedChartsType.label}
                  className={cx(styles.advancedCharts, {
                    [commonTabStyles.active]: activeChart.value === advancedChartsType.value,
                  })}
                  onClick={() => {
                    handleChartsClick(advancedChartsType);
                    sendEvent({
                      event_category: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_CATEGORY,
                      event_action: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_ACTION,
                      event_label: ADV_CHARTS_GA.CHART_TYPE_OPTION_CLICKED.EVENT_LABEL(
                        advancedChartsType.label.toLowerCase(),
                      ),
                    });
                  }}
                >
                  <span>{advancedChartsType.label}</span>
                  {
                    advancedChartsType.showSettingsIcon && (
                      <Icon
                        size={2}
                        name={ICON_NAME.SETTINGS}
                        onIconClick={() => handleAdvancedChartsSettings(advancedChartsType)}
                      />
                    )
                  }
                </div>
              ))}
            </div>
          </div>
        </div>
      </Popper>
    </div>
  );
}

export default Graphs;
