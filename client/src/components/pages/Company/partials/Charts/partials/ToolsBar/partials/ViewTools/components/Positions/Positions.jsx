import React from 'react';
import Popper from '@common/Popper';
import With<PERSON>oader from '@common/WithLoader';
import {
  STATICS,
  VIEW_TOOLS_TITLES,
} from '@pages/Company/partials/Charts/ENUMS';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { ICON_NAME } from '@common/Icon/enums';

import { ROUTE_NAME } from '@utils/enum';
import { sendEvent } from '@service/AnalyticsService';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import Routes from '@/routes';
import Position from './partials/Position';
import ViewAll from '../common/ViewAll';
import styles from './positions.scss';
import commonStyles from '../../../../index.scss';

function Positions({ data: { All } }) {
  const { stockName } = useCharts();
  return (
    <Popper
      title={VIEW_TOOLS_TITLES.POSITIONS}
      count={All.length}
      className={styles.popper}
      iconSize={4}
      iconName={ICON_NAME.VIEW_POSITIONS}
      titleClassName={commonStyles.title}
      onToggle={(isOpen) => {
        if (isOpen) {
          sendEvent({
            event_category: ADV_CHARTS_GA.POSITIONS_CLICKED.EVENT_CATEGORY,
            event_action: ADV_CHARTS_GA.POSITIONS_CLICKED.EVENT_ACTION,
          });
        }
      }}
      iconStyles={styles.titleIconStyle}
    >
      <div className={styles.wrapper}>
        <div className={styles.positions}>
          {All.length ? (
            All.map((position) => (
              <Position
                data={position}
                key={`${position.displayName}_${position.display_product}`}
              />
            ))
          ) : (
            <div className={styles.noPositionText}>
              {STATICS.NO_POSITIONS}
              <span>{stockName}</span>
            </div>
          )}
        </div>
        <ViewAll
          redirectURL={Routes[ROUTE_NAME.POSITIONS].url}
          handleOnClick={() => {
            sendEvent({
              event_category: ADV_CHARTS_GA.POSITIONS_VIEW_ALL_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.POSITIONS_VIEW_ALL_CLICKED.EVENT_ACTION,
            });
          }}
        />
      </div>
    </Popper>
  );
}

const PositionsHOC = WithLoader({ WrappedComponent: Positions });

export { PositionsHOC as default, Positions };
