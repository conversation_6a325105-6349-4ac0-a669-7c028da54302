import React, { useRef, useState, useEffect } from 'react';
import Popper from '@common/Popper';
import { ICON_NAME } from '@common/Icon/enums';
import { classNames as cx } from '@utils/style';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { TOOLS_TYPE_ID } from '@pages/Company/partials/Charts/ENUMS';

import Icon from '@common/Icon';
import styles from './index.scss';
import commonStyles from '../../../../index.scss';

function Layouts({
  isSaveLayout = false,
}) {
  const popperRef = useRef();
  const {
    fullScreen, layouts, handleAddLayout, handleLayoutAction, toolTypeOpen,
  } = useCharts();
  const [showSave, setShowSave] = useState(false);
  const inputRef = useRef(null);
  const [inputLabel, setInputLabel] = useState('');

  const handleLayoutClick = (layout) => {
    const { id, label } = layout;
    handleLayoutAction(id, label, isSaveLayout ? 'update' : 'apply');
  };

  const saveLayout = () => {
    if (inputLabel.length) {
      handleAddLayout(inputLabel);
      setInputLabel('');
      setShowSave(!showSave);
    } else {
      inputRef.current.focus();
    }
  };

  const deleteLayout = (layout) => {
    const { id, label } = layout;
    handleLayoutAction(id, label, 'delete');
  };

  const iconDecider = () => {
    if (isSaveLayout) return ICON_NAME.CHART_SAVE;
    if (!fullScreen && layouts.some((layout) => layout.isSelected)) return ICON_NAME.CHART_VIEWS_ACTIVE;
    return ICON_NAME.CHART_VIEWS;
  };

  useEffect(() => {
    if (toolTypeOpen === TOOLS_TYPE_ID.SAVE_VIEW && isSaveLayout) {
      // Calling openPopper if the selected tool is save views
      // Using isSaveLayout to avoid opening of view layouts in minimized screen
      if (popperRef?.current?.openPopper) {
        // eslint-disable-next-line no-unused-expressions
        popperRef?.current?.openPopper();
      }
    }
  }, [toolTypeOpen, isSaveLayout]);

  return (
    <div>
      <Popper
        ref={popperRef}
        title={!fullScreen || isSaveLayout ? '' : 'Views'}
        iconName={iconDecider()}
        iconSize={4}
        showDropDownIcon={!isSaveLayout}
        className={cx(styles.popper, {
          [styles.savePopper]: isSaveLayout,
        })}
        onToggle={() => {
          setInputLabel('');
          setShowSave(false);
        }}
        titleClassName={commonStyles.title}
        activeIconName={isSaveLayout ? ICON_NAME.CHART_SAVE_ACTIVE : ICON_NAME.CHART_VIEWS_ACTIVE}
        message={!fullScreen && 'Views'}
        dropDownIconStyles={!fullScreen && layouts.some((layout) => layout.isSelected) ? styles.dropDownIcon : ''}
      >
        <div className={styles.wrapper}>
          <span className={styles.header}>SAVED VIEWS</span>
          <div className={styles.layoutWrapper}>

            {layouts.length ? layouts.map((layout, key) => (
              <div
                role="presentation"
                key={key}
                className={cx(styles.layouts, {
                  [styles.activeLayout]: layout.isSelected,
                })}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!layout.isSelected || isSaveLayout) {
                    handleLayoutClick(layout);
                  }
                }}
              >
                {layout.label}
                <div
                  className={styles.deleteIcon}
                  role="presentation"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteLayout(layout);
                  }}
                >
                  <Icon
                    name={ICON_NAME.ADD_DARK}
                    size={2}
                  />
                </div>
              </div>
            )) : <div className={styles.noLayouts}>No saved layouts</div>}
          </div>
          {
            isSaveLayout
              ? (
                <div>
                  {
                    !showSave
                      ? (
                        <div
                          className={styles.saveLayout}
                          role="presentation"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowSave(!showSave);
                            if (inputRef) {
                              setTimeout(() => {
                                inputRef.current.focus();
                              });
                            }
                          }}
                        >
                          <Icon
                            name={ICON_NAME.ADD_DARK}
                            size={1.6}
                            className={styles.addIcon}
                          />
                          Save View
                        </div>
                      )
                      : (
                        <div
                          className={styles.saveLayout}
                        >
                          <input
                            ref={inputRef}
                            onClick={(e) => e.stopPropagation()}
                            onChange={(e) => setInputLabel(e.target.value)}
                            className={styles.nameInput}
                          />
                          <div
                            className={styles.tickIcon}
                            role="presentation"
                            onClick={(e) => {
                              e.stopPropagation();
                              saveLayout();
                            }}
                          >
                            <Icon
                              name={ICON_NAME.TICK_BOX}
                              size={3.6}
                            />
                          </div>
                        </div>
                      )
                  }
                </div>
              )
              : null
          }
        </div>
      </Popper>
    </div>
  );
}

export default Layouts;
