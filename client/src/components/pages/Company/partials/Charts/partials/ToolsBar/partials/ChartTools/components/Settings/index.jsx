import React, { useMemo, useRef, useEffect } from 'react';

import Popper from '@common/Popper';
import { ICON_NAME } from '@common/Icon/enums';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SETTINGS, TOOLS_TYPE_ID } from '@pages/Company/partials/Charts/ENUMS';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';
import { classNames as cx } from '@utils/style';

import styles from './index.scss';
import RadioGroup from './partials/RadioGroup';
import ToggleGroup from './partials/ToggleGroup';

function Settings({ isDropdownItem = false }) {
  const popperRef = useRef();
  const ctx = useCharts();
  const {
    selectedSettingOptions,
    handleSettings,
    toolTypeOpen,
  } = ctx;
  const { chartConstants } = useLoggedInContext();
  const radioGroupData = useMemo(() => {
    const options = chartConstants.data?.common?.yAxisScale || [];
    return {
      ...SETTINGS.RADIO_BUTTONS,
      options,
    };
  }, [chartConstants.data]);

  useEffect(() => {
    if (toolTypeOpen === TOOLS_TYPE_ID.SETTINGS) {
      // Calling openPopper if the selected tool is settings
      if (popperRef?.current?.openPopper) {
        // eslint-disable-next-line no-unused-expressions
        popperRef?.current?.openPopper();
      }
    }
  }, [toolTypeOpen]);

  return (
    <Popper
      ref={popperRef}
      iconName={ICON_NAME.SETTINGS_OUTLINE}
      showDropDownIcon={false}
      className={cx(styles.popper, {
        [styles.popperDropdownItem]: isDropdownItem,
      })}
      iconSize={4}
      onToggle={(isOpen) => {
        if (isOpen) {
          sendEvent({
            event_category: ADV_CHARTS_GA.SETTINGS_CLICKED.EVENT_CATEGORY,
            event_action: ADV_CHARTS_GA.SETTINGS_CLICKED.EVENT_ACTION,
          });
        }
      }}
    >
      <div
        className={styles.container}
        onClick={(e) => e.stopPropagation()}
        role="presentation"
      >
        <ToggleGroup
          data={SETTINGS.TOGGLE_BUTTONS}
          onSelect={(settings) => {
            handleSettings(settings);
            const key = Object.keys(settings)[0];
            const option = SETTINGS.TOGGLE_BUTTONS.find(({ id }) => id === key);
            sendEvent({
              event_category: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_ACTION,
              event_label: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_LABEL(option.displayName.toLowerCase()),
            });
          }}
          selectedItem={selectedSettingOptions}
        />
        <RadioGroup
          data={radioGroupData}
          onSelect={(settings) => {
            handleSettings(settings);
            const key = Object.values(settings)[0];
            const option = radioGroupData.options.find(({ value }) => value === key);
            sendEvent({
              event_category: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_ACTION,
              event_label: ADV_CHARTS_GA.SETTINGS_OPTION_CLICKED.EVENT_LABEL(option.label.toLowerCase()),
            });
          }}
          selectedItem={selectedSettingOptions[radioGroupData.id]}
        />
      </div>
    </Popper>
  );
}

export default Settings;
