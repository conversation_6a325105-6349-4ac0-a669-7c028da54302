import React from 'react';

import Popper from '@common/Popper';
import { ICON_NAME } from '@common/Icon/enums';
import { classNames as cx } from '@utils/style';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { STATICS } from '@pages/Company/partials/Charts/ENUMS';
import { ADV_CHARTS_GA } from '@constants/ga-events';
import { sendEvent } from '@service/AnalyticsService';

import styles from './index.scss';
import commonStyles from '../../../../index.scss';

function TimeInterval() {
  const { chartConstants } = useLoggedInContext();
  const { activeInterval, handleInterval } = useCharts();
  const TIME_INTERVALS = chartConstants.data?.common?.timeInterval || [];

  const handleTimeInterval = (interval) => {
    if (interval.value !== activeInterval.value) {
      handleInterval(interval.value);
    }
  };

  return (
    <div>
      <Popper
        title={activeInterval.label}
        iconName={ICON_NAME.CALENDAR_CHARTS}
        iconSize={4}
        className={styles.popper}
        titleClassName={commonStyles.title}
        onToggle={(isOpen) => {
          if (isOpen) {
            sendEvent({
              event_category: ADV_CHARTS_GA.TIME_INTERVAL_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.TIME_INTERVAL_CLICKED.EVENT_ACTION,
            });
          }
        }}
        iconStyles={styles.titleIconStyle}
      >
        <div className={styles.wrapper}>
          <span className={styles.header}>{STATICS.SELECT_INTERVAL}</span>
          {TIME_INTERVALS.map((interval) => (
            <div
              role="presentation"
              key={interval.value}
              className={cx(styles.interval, {
                [styles.active]: activeInterval.value === interval.value,
              })}
              onClick={() => {
                handleTimeInterval(interval);
                sendEvent({
                  event_category: ADV_CHARTS_GA.TIME_INTERVAL_OPTION_CLICKED.EVENT_CATEGORY,
                  event_action: ADV_CHARTS_GA.TIME_INTERVAL_OPTION_CLICKED.EVENT_ACTION,
                  event_label: ADV_CHARTS_GA.TIME_INTERVAL_OPTION_CLICKED.EVENT_LABEL,
                  vertical_name: ADV_CHARTS_GA.TIME_INTERVAL_OPTION_CLICKED.VERTICAL_NAME,
                  event_label3: interval.value,
                  screenName: ADV_CHARTS_GA.TIME_INTERVAL_OPTION_CLICKED.SCREEN_NAME,
                });
              }}
            >
              {interval.label}
            </div>
          ))}
        </div>
      </Popper>
    </div>
  );
}

export default TimeInterval;
