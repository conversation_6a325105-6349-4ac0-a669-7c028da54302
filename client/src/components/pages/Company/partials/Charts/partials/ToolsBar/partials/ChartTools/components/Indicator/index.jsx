import React, { useState } from 'react';

import Popper from '@common/Popper';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import {
  STATICS,
} from '@pages/Company/partials/Charts/ENUMS';
import { classNames as cx } from '@utils/style';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { sendEvent } from '@service/AnalyticsService';
import { ADV_CHARTS_GA } from '@constants/ga-events';

import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import commonTabStyles from '../../index.scss';
import commonStyles from '../../../../index.scss';
import styles from './index.scss';

function Indicator() {
  const ctx = useCharts();
  const {
    fullScreen,
    handleIndicators,
    activeIndicators,
    handleRemoveIndicator,
    handleClearAllIndicators,
    handleIndicatorsSettings,
  } = ctx;
  const { chartConstants } = useLoggedInContext();
  const indicators = chartConstants.data?.common?.indicators || [];
  const [filteredList, setFilteredList] = useState([...indicators]);
  const displaySegregator = Boolean(activeIndicators.length);

  const filterIndicators = (e) => {
    const { value } = e.target;
    const filteredIndicators = indicators.filter(({ label }) => label
      .toLowerCase()
      .includes(value.toLowerCase()));
    setFilteredList(filteredIndicators);
  };

  const resetIndicators = () => {
    if (filteredList.length !== indicators.length) {
      setFilteredList([...indicators]);
    }
  };

  const removeIndicator = (e, indicator) => {
    e.stopPropagation();
    handleRemoveIndicator(indicator);
  };

  const handleClearAll = (e) => {
    e.stopPropagation();
    handleClearAllIndicators();
  };

  const handleSettings = (e, indicator) => {
    e.stopPropagation();
    handleIndicatorsSettings(indicator);
  };

  const renderActiveIndicators = () => activeIndicators.map((indicator) => (
    <span
      key={indicator.type}
      className={cx([
        commonTabStyles.tab,
        styles.indicator,
        commonTabStyles.active,
      ])}
      onClick={() => handleIndicators(indicator.type)}
      role="presentation"
    >
      <span>{indicator.name}</span>
      <span
        role="presentation"
        onClick={(e) => handleSettings(e, indicator)}
        className={styles.closeButton}
      >
        <Icon name={ICON_NAME.CHART_SETTINGS} size={2} />
      </span>
      <span
        role="presentation"
        onClick={(e) => removeIndicator(e, indicator)}
        className={styles.closeButton}
      >
        <Icon name={ICON_NAME.CLOSE_DARK} size={1.8} />
      </span>
    </span>
  ));

  const renderIndicators = () => filteredList.map(({ value, label }) => (
    <span
      key={value}
      className={cx([commonTabStyles.tab, styles.indicator])}
      onClick={() => {
        handleIndicators(value);
        sendEvent({
          event_category:
              ADV_CHARTS_GA.INDICATOR_OPTION_CLICKED.EVENT_CATEGORY,
          event_action: ADV_CHARTS_GA.INDICATOR_OPTION_CLICKED.EVENT_ACTION,
          event_label: ADV_CHARTS_GA.INDICATOR_OPTION_CLICKED.EVENT_LABEL,
          vertical_name: ADV_CHARTS_GA.INDICATOR_OPTION_CLICKED.VERTICAL_NAME,
          screenName: ADV_CHARTS_GA.INDICATOR_OPTION_CLICKED.SCREEN_NAME,
          event_label3: value,
        });
      }}
      role="presentation"
    >
      {label}
    </span>
  ));

  return (
    <div>
      <Popper
        title={fullScreen && STATICS.INDICATOR}
        iconName={ICON_NAME.INDICATORS}
        iconSize={4}
        className={styles.popper}
        handleClose={resetIndicators}
        titleClassName={commonStyles.title}
        onToggle={(isOpen) => {
          if (isOpen) {
            sendEvent({
              event_category: ADV_CHARTS_GA.INDICATOR_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.INDICATOR_CLICKED.EVENT_ACTION,
              event_label: ADV_CHARTS_GA.INDICATOR_CLICKED.EVENT_LABEL,
              vertical_name: ADV_CHARTS_GA.INDICATOR_CLICKED.VERTICAL_NAME,
              screenName: ADV_CHARTS_GA.INDICATOR_CLICKED.SCREEN_NAME,
            });
          }
        }}
        message={!fullScreen && STATICS.INDICATOR}
        containerStyles={!fullScreen && activeIndicators.length ? styles.active : ''}
        dropDownIconStyles={!fullScreen && activeIndicators.length ? styles.dropDownIcon : ''}
      >
        <div className={styles.wrapper}>
          <div className={styles.input}>
            <input
              onClick={(e) => e.stopPropagation()}
              placeholder={STATICS.INPUT_PLACEHOLDER}
              onChange={filterIndicators}
            />
          </div>
          <div className={styles.indicatorsContainer}>
            {displaySegregator ? (
              <span className={styles.segregrator}>
                <span className={styles.header}>{STATICS.SELECTED}</span>
                <span
                  onClick={handleClearAll}
                  role="presentation"
                  className={styles.clearAll}
                >
                  {STATICS.CLEAR_ALL}
                </span>
              </span>
            ) : (
              ''
            )}
            {renderActiveIndicators()}
            {displaySegregator ? (
              <span className={cx([styles.segregrator, styles.header])}>
                {STATICS.ALL_INDICATORS}
              </span>
            ) : (
              ''
            )}
            {renderIndicators()}
          </div>
        </div>
      </Popper>
    </div>
  );
}

export default Indicator;
