import React from 'react';

import { classNames as cx } from '@utils/style';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { INSTRUMENTS } from '@utils/enum';

import styles from './index.scss';
import ViewTools from './partials/ViewTools';
import ChartTools from './partials/ChartTools';

function ToolsBar(hideShare = false) {
  const {
    fullScreen, setFullScreen, instrumentType, segment,
  } = useCharts();
  return (
    <div className={cx(styles.container, {
      [styles.index]: INSTRUMENTS.INDEX === instrumentType,
      [styles.fullScreen]: fullScreen,
    })}
    >
      { INSTRUMENTS.INDEX !== instrumentType && <ViewTools segment={segment} />}
      <ChartTools
        requestFullscreen={setFullScreen}
        fullScreen={fullScreen}
        hideShare={hideShare}
      />
    </div>
  );
}

export default ToolsBar;
