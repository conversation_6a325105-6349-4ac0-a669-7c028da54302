import React from 'react';

import Popper from '@common/Popper';
import WithLoader from '@common/WithLoader';
import {
  STATICS,
  VIEW_TOOLS_TITLES,
} from '@pages/Company/partials/Charts/ENUMS';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { ICON_NAME } from '@common/Icon/enums';
import { ADV_CHARTS_GA } from '@constants/ga-events';

import { ROUTE_NAME } from '@utils/enum';
import { sendEvent } from '@service/AnalyticsService';
import Routes from '@/routes';
import styles from './orders.scss';
import ViewAll from '../common/ViewAll';
import Order from './partials/Order';
import commonStyles from '../../../../index.scss';

function Orders({ data }) {
  const { stockName, exchange } = useCharts();
  const ordersData = data.filter((item) => item.display_status === 'Pending');
  return (
    <Popper
      title={VIEW_TOOLS_TITLES.ORDERS}
      count={ordersData.length}
      className={styles.popper}
      iconName={ICON_NAME.ORDERS}
      iconSize={4}
      titleClassName={commonStyles.title}
      onToggle={(isOpen) => {
        if (isOpen) {
          sendEvent({
            event_category: ADV_CHARTS_GA.ORDERS_CLICKED.EVENT_CATEGORY,
            event_action: ADV_CHARTS_GA.ORDERS_CLICKED.EVENT_ACTION,
          });
        }
      }}
      iconStyles={styles.titleIconStyle}
    >
      <div className={styles.wrapper}>
        <div className={styles.orders}>
          {ordersData.length ? (
            ordersData.map((order) => (
              <Order
                data={order}
                key={`${order.order_date_time}_${order.display_name}`}
              />
            ))
          ) : (
            <div className={styles.noOrderPlacedText}>
              {STATICS.NO_ORDERS}
              <span>{` ${stockName} ${exchange}`}</span>
            </div>
          )}
        </div>
        <ViewAll
          redirectURL={Routes[ROUTE_NAME.ORDERS].url}
          handleOnClick={() => {
            sendEvent({
              event_category: ADV_CHARTS_GA.ORDERS_VIEW_ALL_CLICKED.EVENT_CATEGORY,
              event_action: ADV_CHARTS_GA.ORDERS_VIEW_ALL_CLICKED.EVENT_ACTION,
            });
          }}
        />
      </div>
    </Popper>
  );
}

const OrdersHOC = WithLoader({ WrappedComponent: Orders });

export { OrdersHOC as default, Orders };
