@import '../../../../../../../../../../../styles/main';

.popper {
  background-color: $default;
  width: 28rem;
  right: 0;
  top: 2.8rem;
}

.wrapper {
  padding: 2.4rem 0 1.6rem;

  @include dropDownArrow;

  &::before {
    right: 3rem;
    left: initial;
  }
}

.input {
  padding: 0 2.6rem 0 2.3rem;
  margin-bottom: .5rem;

  input {
    width: 100%;
    height: 3rem;
    outline: none;
    border: 1px solid $grey3;
    border-radius: .2rem;
    color: $grey2;
    padding: .5rem 0 .5rem 1rem;

    @include typography(h8);
  }
}

.indicatorsContainer {
  max-height: 30rem;
  overflow: auto;
  flex-direction: column;

  > *:not(:last-child) {
    margin-bottom: .1rem;
  }
}

.indicator {
  padding: 1rem 2.6rem 1rem 2.3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  > span:first-child {
    flex-basis: 80%;
  }
}

.closeButton {
  cursor: pointer;
}

.segregrator {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 1rem 2.6rem 1rem 2.3rem;
  cursor: default;

  @include typography(h8);

  > span {
    cursor: pointer;
  }
}

.clearAll {
  color: $secondary-color;
}

.header {
  color: $grey1;

  @include typography(h7, bold);
}

.active > div {
  color: $secondary-color;

  path[fill^='#'],
  circle[fill^='#'],
  rect {
    fill: $secondary-color;
  }

  circle[stroke^='#'],
  rect,
  polyline,
  path {
    stroke: $secondary-color;
  }
}

.dropDownIcon > div > *:first-child {
  border-color: $secondary-color;
}
