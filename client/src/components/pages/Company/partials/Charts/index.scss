@import '../../../../../styles/main';
/* stylelint-disable declaration-no-important */
.container {
  border-radius: .4rem;
  position: relative;
  background-color: $default;
  height: 40rem;
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;

  @include box-shadow(0, 2px, 6px);

  @include respond(phone) {
    height: 25rem;
  }
}

.corporateActions {
  height: 50rem !important;
}

.shadow {
  padding-bottom: .7rem;

  @include box-shadow(0, 10px, 20px, 0);
}

.loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $default;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: .4rem;
}

.toggleContainer {
  box-shadow: none;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.fullScreenContainer {
  box-shadow: none;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  position: absolute;
  z-index: 25;
}

.mainView {
  display: flex;
  flex: 1 0 0;

  @include respond(phone) {
    max-width: 100%;
    margin-left: 1rem;
  }
}

.mainViewFullScreen {
  height: calc(100% - 4rem);
}

.iframeContainer {
  flex: 1 0 0;
  position: relative;

  @include respond(phone) {
    display: block;
  }

  iframe {
    height: 100%;

    @include respond(phone) {
      height: auto;
    }
  }
}

.iframeContainerToggled {
  display: flex;
  flex-direction: column;
  flex: 1 0 0;
}

.iframe {
  width: 100%;
  border: 0;
  border-radius: .4rem;
}

.rangeControls {
  display: flex;

  > div:first-child {
    position: relative;
  }
}

.rangeContainer {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 1.5rem 1.5rem 0;

  @include respond(phone) {
    display: flex;
    justify-content: center;
    flex-direction: row;
    padding: 1.5rem 1.5rem 0;
  }

  .returnsValue {
    @include respond(phone) {
      @include typography(h8);
    }
  }

  > *:first-child {

    color: $grey3;
    margin-right: .5rem;

    @include typography(h7);

    @include respond(phone) {
      @include typography(h8);
    }
  }

  > *:nth-child(2) {
    margin-top: .4rem;

    @include typography(h7);

    @include respond(phone) {
      margin-top: 0;

      @include typography(h8);
    }
  }
}

.chartIndices {
  justify-content: flex-end;
}

.scripActions {
  justify-content: space-between;
}

.homeScreenChart {
  margin-top: 2.4rem;
}
