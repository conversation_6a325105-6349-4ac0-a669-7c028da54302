import React, {
  createContext, useCallback, useContext, useEffect, useRef, useState, useMemo,
} from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import isEqual from 'lodash/isEqual';
import useObservable from '@common/useObservable';
import { openTpinWindow } from '@modules/OrderFlow/CDSLAuthorisation';
import useOrders from '@modules/Orders';
import usePerformanceData from '@pages/Company/partials/Overview/partials/Performance/usePerformanceData';
import useGetApi from '@common/UseApi/useGetApi';
import { getCompanyData } from '@pages/Company/api';
import { useCallbackForEvents, useDidUpdateEffect } from '@utils/react';
import { handle419Errors } from '@service/ApiService';
import {
  COOKIES, INSTRUMENTS, EXCHANGE_CODE, SEGMENT_TYPES, EXCHANGE, LOCATION_DETAILS, IR_STATUS, IR_STATUS_REVOKE_SUBTYPE,
} from '@utils/enum';
import { getDataTheme } from '@utils/style';
import LocalStorage from '@service/LocalStorage';
import { UserContext } from '@layout/App/UserContext';
import { DataFeedContext } from '@layout/App/DataFeedContext';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { updateStatus } from '@layout/TPINAuth/tpinApi';
import { useMarketStatus } from '@modules/MarketStatus';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { combineLatest, empty } from 'rxjs';
import { REQUEST_TYPES, RESPONSE_TYPES } from '@service/dataConfig';
import { first, map } from 'rxjs/operators';
import moment from 'moment';
import { useToast } from '@common/Toast';
import { APPEARANCE_TYPES } from '@common/Toast/enums';
import useNonIrModal from '@common/useNonIrModal';
import { sendEvent } from '@service/AnalyticsService';
import {
  mapQueryString, getCombinedFeed, getLtpChangeFromTradeClose,
  setQueryParam, getUrlParameter, getCookieValue, getInstrumentLink,
} from '@utils';

import {
  CHART_TYPE, SETTINGS, SEGMENT_TYPE, LOCAL_STORAGE_KEYS, STATUS,
} from './ENUMS';
import { constructBroadcastPacket } from './utils';

const indexResponseTypes = [RESPONSE_TYPES.INDEX];
const stockResponseTypes = [RESPONSE_TYPES.OHLC, RESPONSE_TYPES.TRADE];

export function useOHLCData() {
  const { dataFeed } = useContext(DataFeedContext);
  return useCallback(({
    security_id, exchange, segment, instrument_type, id,
  }) => {
    if (!dataFeed || !security_id) return empty();
    switch (instrument_type) {
      case INSTRUMENTS.INDEX: {
        const feeds = dataFeed
          ?.getStream(REQUEST_TYPES.INDEX,
            [parseInt(security_id, 10)],
            indexResponseTypes)[0].feed;
        return feeds.pipe(map((feed) => ({
          ...feed,
          id,
        })));
      }
      default: {
        const feeds = dataFeed
          ?.getStream(REQUEST_TYPES.STOCK,
            [EXCHANGE_CODE[segment][exchange],
              parseInt(security_id, 10)],
            stockResponseTypes);
        return getCombinedFeed(feeds).pipe(
          map(([tradeData, ohlcData]) => ({
            ...tradeData,
            ...ohlcData,
            id,
          })),
        );
      }
    }
  }, [dataFeed]);
}

const requiredCompareFeedResponse = [RESPONSE_TYPES.TRADE, RESPONSE_TYPES.P_CLOSE];
const requiredCompareFeedResponseForIndex = [RESPONSE_TYPES.INDEX];

function useStockFeed() {
  const { dataFeed } = useContext(DataFeedContext);
  return useCallback(({
    security_id, exchange, instrument_type, segment = SEGMENT_TYPES.CASH, id,
  }) => {
    if (!dataFeed || !security_id) return empty();
    switch (instrument_type) {
      case INSTRUMENTS.INDEX: {
        const feedsIndex = dataFeed
          ?.getStream(REQUEST_TYPES.INDEX,
            [parseInt(security_id, 10)],
            requiredCompareFeedResponseForIndex)[0].feed;
        return feedsIndex.pipe(
          map(({
            pClose: prev, lastTradePrice: ltp, lastUpdatedTime,
          }) => ({
            ltp,
            change: ltp - prev,
            percentageChange: ((ltp - prev) / prev) * 100,
            id,
            lastUpdatedTime,
          })),
        );
      }
      default: {
        const feeds = dataFeed
          ?.getStream(REQUEST_TYPES.STOCK,
            [EXCHANGE_CODE[segment][exchange],
              parseInt(security_id, 10)],
            requiredCompareFeedResponse);
        return getCombinedFeed(feeds).pipe(
          map(([tradeData, closeData]) => {
            const { tradeVolume, openInterest, lastTradeTime } = tradeData;
            const { percentageChange, change, ltp } = getLtpChangeFromTradeClose(tradeData, closeData);
            return {
              ltp,
              change,
              percentageChange,
              tradeVolume,
              id,
              openInterest,
              lastTradeTime,
            };
          }),
        );
      }
    }
  }, [dataFeed]);
}

const ChartsContext = createContext(null);

function ChartsProvider({
  id,
  name: stockName,
  exchange,
  securityId,
  toggleDetails,
  disableToggleCharts,
  isTFCEnabled,
  resetTFC,
  setChartInitSuccess,
  children,
  isLoading,
  instrumentType,
  isin,
  tick_size,
  lot_size,
  segment = SEGMENT_TYPES.CASH,
  rangeParam = false,
  ...props
}) {
  const { combinedIrData } = useContext(UserContext);

  const hideChartsMarker = useMemo(() => (combinedIrData?.EQUITY[0].irStatus === IR_STATUS.REVOKED
    && (combinedIrData?.EQUITY[0].revokeSubType === IR_STATUS_REVOKE_SUBTYPE.KRA_VALIDATION_REVOKED
    || combinedIrData?.EQUITY[0].revokeSubType === IR_STATUS_REVOKE_SUBTYPE.AADHAAR_PAN_CONNECTION
    || combinedIrData?.EQUITY[0].revokeSubType === IR_STATUS_REVOKE_SUBTYPE.FREEZE)),
  [combinedIrData]);

  const twoFAtoken = getCookieValue(COOKIES.TWOFA_TOKEN);

  const headers = useMemo(() => ({
    'content-type': 'application/json',
    'x-pmngx-key': 'paytmmoney',
    [COOKIES.SSO_TOKEN]: getCookieValue(COOKIES.SSO_TOKEN),
    [COOKIES.USER_AGENT]: getCookieValue(COOKIES.USER_AGENT),
    ...(twoFAtoken ? { [COOKIES.TWOFA_TOKEN]: twoFAtoken } : {}),
    'location-data': LocalStorage.get(LOCATION_DETAILS, true),
    hideChartsMarker: hideChartsMarker || false,
  }), [hideChartsMarker, twoFAtoken]);

  const reqStreams = useStockFeed();
  const activeTheme = getDataTheme();

  const {
    id: ltpId, ltp, change, percentageChange, tradeVolume, openInterest, lastTradeTime, lastUpdatedTime,
  } = useObservable(
    () => (securityId ? reqStreams({
      security_id: securityId,
      exchange,
      instrument_type: instrumentType,
      segment,
      id,
    }) : empty()),
    [securityId, exchange, instrumentType, segment, reqStreams],
  ) || {};

  const reqOHLCStreams = useOHLCData();
  const ohlc = useObservable(
    () => (securityId ? reqOHLCStreams({
      security_id: securityId,
      exchange,
      segment,
      instrument_type: instrumentType,
      id,
    }).pipe(first()) : empty()),
    [securityId, exchange, instrumentType, segment, reqOHLCStreams],
  ) || null;

  const history = useHistory();
  const { search, pathname } = useLocation();

  const { chartConstants } = useLoggedInContext();
  const CHART_LAYOUT_TYPE = chartConstants.data?.common?.chartLayoutType || {};

  const fullScreen = useMemo(() => getUrlParameter('fullScreen', history.location.search) === 'true', [history.location.search]);

  const {
    isMultiGrid,
    setMultiGrid,
    setActiveMultiGridChartId,
    activeMultiGridCharts,
    setActiveMultiGridCharts,
    multiGridInitChartsData,
    setIsAdvancedChartSelected,
  } = useContext(WatchlistContext);

  const [activeRange, setRange] = useState('');
  const [activeInterval, setInterval] = useState({});
  const [activeChart, setChartType] = useState({});
  const [activeIndicators, setActiveIndicators] = useState([]);
  const [isChartInit, setChartInitStatus] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isIframeInit, setIframeInit] = useState(false);
  const [isDrawingToolbarActive, setDrawingToolbar] = useState(false);
  const [firstValueInRange, setFirstValueInRange] = useState(null);
  const [isChartsLoading, setIsChartsLoading] = useState(isLoading);
  const [layouts, setLayouts] = useState([]);
  const [selectedSettingOptions, setSelectedSettingsOptions] = useState({});
  const [isCompareChartInstance, setIsCompareChartInstance] = useState(false);
  const [compareChartPmlIds, setCompareChartPmlIds] = useState([]);
  const [compareCompanyData, setCompareCompanyData] = useState([]);
  const [shareChartId, setShareChartId] = useState('');
  const [isTFC, setTFC] = useState(false);
  const [displayTFCIntro, setTFCIntro] = useState(false);
  const [multiGridUniqueIds, setMultiGridUniqueIds] = useState([]);
  const [multiGridUniqueCompanyData, setMultiGridUniqueCompanyData] = useState([]);
  const { handleIrAction } = useNonIrModal();
  const [toolTypeOpen, setToolTypeOpen] = useState(null); // This will be used to show the tool selected from "MORE" dropdown
  const { ordersData } = useOrders();
  const { limitData } = usePerformanceData({ exchange, securityId, segment });
  const lastMultigridAddedId = useRef(null);
  const isReload = useRef(true);

  const marketStatusCtx = useMarketStatus();
  const mkt_status = marketStatusCtx?.marketStatus?.[segment][exchange || EXCHANGE.NSE].status;
  const amo = marketStatusCtx?.marketStatus?.[segment][exchange || EXCHANGE.NSE].amo;

  const rangeValue = useMemo(() => {
    // Logic to calculate Range value for charts
    if (firstValueInRange && ltp) {
      return ((ltp - firstValueInRange) / Math.abs(firstValueInRange)) * 100;
    } return null;
  }, [ltp, firstValueInRange]);

  const iframeRef = useRef(null);
  const chartsRef = useRef(null);

  const requiredStreams = useCallback(({ // eslint-disable-next-line
    id, instrument_type, security_id, exchange, segment,
  }) => reqStreams({
    security_id,
    exchange,
    instrument_type,
    segment,
    id,
  }), [reqStreams]);

  const requiredOHLCStreams = useCallback(({ // eslint-disable-next-line
    id, instrument_type, security_id, exchange, segment,
  }) => reqOHLCStreams({
    security_id,
    exchange,
    instrument_type,
    segment,
    id,
  }), [reqOHLCStreams]);

  const compareChartLtpData = useObservable(() => (
    compareCompanyData.length
      ? combineLatest(compareCompanyData.map(requiredStreams)) : empty()
  ), [compareCompanyData, requiredStreams]);

  const compareChartOHLCData = useObservable(() => (
    compareCompanyData.length
      ? combineLatest(compareCompanyData.map(requiredOHLCStreams)).pipe(first()) : empty()
  ), [compareCompanyData, requiredOHLCStreams]);

  const { makeRequest } = useGetApi();
  const maxCompareStocksCacheCount = 10;
  useEffect(() => {
    if (isCompareChartInstance && compareChartPmlIds.length) {
      const updatedCompareCompanyData = compareCompanyData
        .filter((company) => compareChartPmlIds.includes(company.id)) || [];
      let compareStocksIds = [];
      const compareStocks = LocalStorage.get(LOCAL_STORAGE_KEYS.COMPARE_STOCKS) || [];
      if (compareStocks && compareStocks.length) {
        compareStocksIds = compareStocks.map((compareStock) => compareStock.id);
      }
      if (compareCompanyData.length > compareChartPmlIds.length) {
        setCompareCompanyData(updatedCompareCompanyData);
      } else {
        compareChartPmlIds.forEach((compareChartPmlId) => {
          if (compareStocksIds.includes(compareChartPmlId)) {
            compareStocks.unshift(compareStocks.splice(compareStocks.map((stock) => stock.id)
              .indexOf(compareChartPmlId), 1)[0]);
            LocalStorage.set(LOCAL_STORAGE_KEYS.COMPARE_STOCKS, compareStocks);
            let companyDataExists = false;
            updatedCompareCompanyData.forEach((compareCompany) => {
              if (compareCompany.id === compareChartPmlId) {
                companyDataExists = true;
              }
            });
            if (!companyDataExists) {
              setCompareCompanyData([compareStocks[0], ...updatedCompareCompanyData]);
            }
          } else {
            makeRequest(getCompanyData(compareChartPmlId)).then(({ data: { results } }) => {
              setCompareCompanyData([results[0], ...updatedCompareCompanyData]);
              if (compareStocksIds.length < maxCompareStocksCacheCount) {
                LocalStorage.set(LOCAL_STORAGE_KEYS.COMPARE_STOCKS, [results[0], ...compareStocks]);
              } else {
                LocalStorage.set(LOCAL_STORAGE_KEYS.COMPARE_STOCKS,
                  [results[0], ...compareStocks.slice(0, maxCompareStocksCacheCount - 1)]);
              }
            }).catch();
          }
        });
      }
    } else if (compareChartPmlIds.length === 0 && compareCompanyData.length) {
      setCompareCompanyData([]);
    }
  }, [makeRequest, compareChartPmlIds, compareCompanyData, isCompareChartInstance]);

  useEffect(() => {
    if (multiGridUniqueIds && multiGridUniqueIds.length) {
      makeRequest(getCompanyData(multiGridUniqueIds.toString())).then(({ data: { results } }) => {
        setMultiGridUniqueCompanyData(results);
      });
    }
  }, [makeRequest, multiGridUniqueIds]);

  const multiGridChartLtpData = useObservable(() => (
    multiGridUniqueCompanyData.length
      ? combineLatest(multiGridUniqueCompanyData.map(requiredStreams)) : empty()
  ), [multiGridUniqueCompanyData, requiredStreams]);

  const multiGridChartOHLCData = useObservable(() => (
    multiGridUniqueCompanyData.length
      ? combineLatest(multiGridUniqueCompanyData.map(requiredOHLCStreams)).pipe(first()) : empty()
  ), [multiGridUniqueCompanyData, requiredOHLCStreams]);

  useEffect(() => {
    setShareChartId(null);
  }, [toggleDetails]);

  useEffect(() => {
    setIsAdvancedChartSelected(toggleDetails);

    return () => setIsAdvancedChartSelected(false);
  }, [toggleDetails, setIsAdvancedChartSelected]);

  useEffect(() => {
    if (chartConstants.failed) {
      setIsError(true);
      return;
    }
    const initialInterval = chartConstants.data?.common?.timeInterval?.[2];
    setInterval(initialInterval || {});
    const initialSettingOptions = {};
    SETTINGS.TOGGLE_BUTTONS.forEach((item) => {
      initialSettingOptions[item.id] = item.value;
    });
    initialSettingOptions.setChartScale = chartConstants.data?.common?.yAxisScale?.[0]?.value;
    setSelectedSettingsOptions(initialSettingOptions);
  }, [chartConstants]);

  useEffect(() => {
    if (rangeParam) {
      setRange(rangeParam);
      return;
    }
    const range = LocalStorage.get(LOCAL_STORAGE_KEYS.CHART_RANGE, false);
    if (range) {
      setRange(range);
      return;
    }
    const initialRange = chartConstants.data?.equity?.range?.[3]?.value;
    LocalStorage.set(LOCAL_STORAGE_KEYS.CHART_RANGE, initialRange, '', false);
    setRange(initialRange);
  }, [chartConstants, rangeParam]);

  useEffect(() => {
    if (isMultiGrid && activeMultiGridCharts) {
      const requiredMultiGridChartIds = activeMultiGridCharts.filter((multiGridChartId) => multiGridChartId !== id);
      if (requiredMultiGridChartIds && requiredMultiGridChartIds.length) {
        setMultiGridUniqueIds(requiredMultiGridChartIds);
      }
    }
  }, [isMultiGrid, activeMultiGridCharts, id]);

  const bridgeInterface = useCallbackForEvents(({ action }) => {
    // this method is for the interaction between iFrame(Charts) and UI
    if (isChartInit) {
      iframeRef.current.contentWindow.postMessage({
        actionType: 'bridgeInterface',
        chartConfig: action,
      }, '*');
    }
  }, [isChartInit]);

  const setFullScreen = useCallback(() => {
    const queryString = search.substring(1);
    const queryParams = mapQueryString(queryString);
    setQueryParam({
      query: { ...queryParams, fullScreen: !fullScreen },
      pathname: history.location.pathname,
      replace: history.replace,
    });
    // Show water mark on fullScreen
    if (!fullScreen) {
      bridgeInterface({ action: { showWatermark: { value: true } } });
    } else {
      bridgeInterface({ action: { showWatermark: { value: false } } });
      if (isTFC) {
        bridgeInterface({ action: { disableTFC: { value: true } } });
        setTFC(false);
      }
    }
  }, [bridgeInterface, fullScreen, history.location.pathname, history.replace, isTFC, search]);

  const setTFCView = useCallback(() => {
    if (!isTFC) {
      const handleIrActionCbValue = handleIrAction(() => null, segment)();
      if (handleIrActionCbValue !== null) {
        // If the revoke condition is satisfied, then modal handler will be returned from handleIrAction,
        // in that case no need to call enableTFC bridge call, the execution of this func will be stopped
        return;
      }

      if (!fullScreen) {
        setFullScreen();
      }
      bridgeInterface({ action: { enableTFC: { value: true } } });
      setTFC(true);
    } else {
      bridgeInterface({ action: { disableTFC: { value: true } } });
      setTFC(false);
    }
  }, [isTFC, handleIrAction, fullScreen, bridgeInterface, setFullScreen, segment]);

  useEffect(() => {
    const getTxnIdTFC = () => {
      const reqId = LocalStorage.get(LOCAL_STORAGE_KEYS.REQ_ID, false);

      if (reqId && isTFC && window.location.href.includes('fullScreen=true')) {
        LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.REQ_ID); // Deleting reqId from local storage to avoid unnecessary API calls
        makeRequest(updateStatus(reqId))
          .then((response) => {
            const { isin_list } = response?.data;
            let is_txn_success = STATUS.FAILED;
            let countIsinFalseStatus = 0;
            isin_list.forEach((isinVal) => {
              if (isinVal?.status === false) {
                countIsinFalseStatus += 1;
              }
            });
            if (countIsinFalseStatus !== isin_list.length) {
              is_txn_success = STATUS.SUCCESS;
            }
            const { displayMessage } = response?.meta;
            bridgeInterface({ action: { verifyCDSLStatus: { status: is_txn_success, message: displayMessage } } });
          })
          .catch((err) => {
            bridgeInterface({
              action: {
                verifyCDSLStatus: {
                  status: STATUS.FAILED, message: err.meta.displayMessage,
                },
              },
            });
          });
      }
    };
    window.addEventListener('storage', getTxnIdTFC);
    return () => window.removeEventListener('storage', getTxnIdTFC);
  }, [bridgeInterface, makeRequest, isTFC]);

  useEffect(() => {
    LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.REQ_ID);
  }, [securityId]);

  useEffect(() => {
    if (isTFCEnabled) {
      setTFCView();
      resetTFC(false);
    } else {
      LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.REQ_ID);
    }
  }, [isTFCEnabled, resetTFC, setTFCView]);

  useEffect(() => {
    // handle fullScreen
    const keyDown = ({ keyCode }) => {
      if (keyCode === 27 && fullScreen) {
        setFullScreen();
      }
    };
    window.addEventListener('keydown', keyDown);
    return () => {
      window.removeEventListener('keydown', keyDown);
    };
  }, [fullScreen, setFullScreen]);

  const { addToast } = useToast();

  useEffect(() => {
    // handle callback from charts
    const { current } = iframeRef;
    function handleMessages(event) {
      if (event?.data?.dataRxCb) {
        const { dataRxCb: data } = event.data;
        const dataRxCb = { ...data, ...(data.state || {}) };
        if (dataRxCb?.status) {
          setChartInitStatus(dataRxCb.status === '200');
          if (segment === SEGMENT_TYPES.DERIVATIVES) {
            setChartInitSuccess(dataRxCb.status === '200');
          }
          setIsChartsLoading(dataRxCb.status !== '200');
          setIsError(dataRxCb.status === '500');
        } if (dataRxCb.setChartType && !chartConstants.failed) {
          const CHARTS_TYPE_LIST = chartConstants.data?.common?.chartType || [];
          let chartType = CHARTS_TYPE_LIST.find((chart) => chart.value === dataRxCb.setChartType);
          if (!chartType) {
            const ADVANCED_CHARTS_TYPE_LIST = chartConstants.data?.common?.advancedChartType || [];
            chartType = ADVANCED_CHARTS_TYPE_LIST
              .find((chart) => chart.value === dataRxCb.setChartType);
          }
          setChartType(chartType);
        } if (dataRxCb.setTimeInterval) {
          const intervalObj = (chartConstants.data?.common?.timeInterval || [])
            .find((interval) => interval.value === dataRxCb.setTimeInterval);
          setInterval(intervalObj || {});
        } if (dataRxCb.setRange) {
          // Save customers chart range selection
          if (!rangeParam) LocalStorage.set(LOCAL_STORAGE_KEYS.CHART_RANGE, dataRxCb.setRange, '', false);
          setRange(dataRxCb.setRange);
        } if (dataRxCb.setIndicators) {
          setActiveIndicators(dataRxCb.setIndicators);
        } if (dataRxCb.clearAllIndicators) {
          setActiveIndicators([]);
        } if (dataRxCb.layoutData) {
          setLayouts(dataRxCb.layoutData);
          if (dataRxCb.layoutInfo) {
            const {
              status, meta, operation,
            } = dataRxCb.layoutInfo;
            if (operation !== 'get' && meta.displayMessage) {
              if (status === 200) {
                addToast(meta.displayMessage, APPEARANCE_TYPES.SUCCESS);
              } else {
                addToast(meta.displayMessage, APPEARANCE_TYPES.FAIL);
              }
            }
          }
        } if (dataRxCb?.messageData) {
          switch (dataRxCb.messageData?.msgType) {
            case APPEARANCE_TYPES.SUCCESS: {
              addToast(dataRxCb.messageData.message, APPEARANCE_TYPES.SUCCESS);
              break;
            }
            case APPEARANCE_TYPES.FAIL: {
              addToast(dataRxCb.messageData?.message, APPEARANCE_TYPES.FAIL);
              break;
            }
            case APPEARANCE_TYPES.INFO: {
              addToast(dataRxCb.messageData?.message, APPEARANCE_TYPES.INFO);
              break;
            }
            default: {
              addToast(
                dataRxCb.messageData?.message,
                APPEARANCE_TYPES.INFO,
                null,
                null,
                null,
                true,
                false,
                null,
                true,
                false,
              );
              break;
            }
          }
        } if (dataRxCb?.compareStocks) {
          const compareStockIds = dataRxCb.compareStocks.reduce((acc, compareStock) => {
            acc.push(compareStock.id);
            return acc;
          }, []);
          setCompareChartPmlIds(compareStockIds);
        } if (dataRxCb?.isCompareChartInstance) {
          setIsCompareChartInstance(dataRxCb.isCompareChartInstance);
        } if (dataRxCb?.shareChart) {
          if (dataRxCb?.shareChart?.data?.id) {
            setShareChartId(dataRxCb.shareChart.data.id);
          } else if (dataRxCb?.shareChart?.info?.status && dataRxCb?.shareChart?.info?.message) {
            if (dataRxCb?.shareChart?.info?.status === 200) {
              addToast(dataRxCb?.shareChart?.info?.message, APPEARANCE_TYPES.SUCCESS);
            } else {
              addToast(dataRxCb?.shareChart?.info?.message, APPEARANCE_TYPES.FAIL);
              setShareChartId(null);
            }
          } else {
            const SHARE_FAIL_TOAST_MESSAGE = 'Share failed, please try again later';
            addToast(SHARE_FAIL_TOAST_MESSAGE, APPEARANCE_TYPES.FAIL);
            setShareChartId(null);
          }
        } if (dataRxCb?.isTFCenabled) {
          setTFC(dataRxCb.isTFCenabled);
        }
        setSelectedSettingsOptions((prevState) => ({
          ...prevState,
          setChartScale: dataRxCb.setChartScale,
          toggleCrosshair: dataRxCb.toggleCrosshair,
          setHeadsUpDisplay: dataRxCb.setHeadsUpDisplay,
        }));
        setDrawingToolbar(dataRxCb.displayDrawingToolbar);
        if (dataRxCb?.firstValueInRange) {
          setFirstValueInRange(dataRxCb.firstValueInRange);
        }
        if (dataRxCb?.selectedStock) {
          const { stockId, segment: segmentType, instrument_type } = event.data.dataRxCb.selectedStock;
          if (!pathname.includes(stockId)) {
            if (segmentType === SEGMENT_TYPES.DERIVATIVES) {
              if (search.includes('fullScreen')) {
                history.replace(`${getInstrumentLink(instrument_type)}/${stockId}?fullScreen=true`);
              } else {
                history.replace(`${getInstrumentLink(instrument_type)}/${stockId}`);
              }
            } else if (!search.includes('toggleDetails')) {
              if (search.includes('fullScreen')) {
                history.replace(`${getInstrumentLink(instrument_type)}/${stockId}?toggleDetails=true&fullScreen=true`);
              } else {
                history.replace(`${getInstrumentLink(instrument_type)}/${stockId}?toggleDetails=true`);
              }
            } else {
              history.replace(`${getInstrumentLink(instrument_type)}/${stockId}${search}`);
            }
          }
        }
        if (dataRxCb?.multiGrid && dataRxCb?.activeStocks) {
          setMultiGrid(true);
          setActiveMultiGridChartId(dataRxCb?.activeChartID);
          if (!isEqual(dataRxCb?.activeStocks, activeMultiGridCharts)) {
            // Send the OHLC of last added multigrid ID, on reload assigning it to null as
            // we need to trigger OHLC bridge interface for all the multigrid IDs
            const lastAddedId = dataRxCb.activeStocks.filter((stock) => !activeMultiGridCharts.includes(stock));
            lastMultigridAddedId.current = lastAddedId.length === 1 ? lastAddedId[0] : null;
            setActiveMultiGridCharts(dataRxCb.activeStocks);
          }
        } else if (dataRxCb?.multiGrid === false || !dataRxCb?.multiGrid) {
          setMultiGrid(false);
          setActiveMultiGridChartId(dataRxCb?.activeChartID);
          setActiveMultiGridCharts(dataRxCb.activeStocks);
        }
        if (dataRxCb?.failed_2FA) {
          handle419Errors();
        }
      }
      if (event?.data?.orderCb) {
        const { orderCb } = event.data;
        if (typeof orderCb.isTFCenabled !== 'undefined' && orderCb.isTFCenabled !== null) {
          if (!LocalStorage.get(LOCAL_STORAGE_KEYS.SHOW_TFC_INTRO, false)) {
            LocalStorage.set(LOCAL_STORAGE_KEYS.SHOW_TFC_INTRO, true, '', false);
            setTFCIntro(true);
          }
          setTFC(orderCb.isTFCenabled);
          const queryString = search.substring(1);
          const queryParams = mapQueryString(queryString);
          setQueryParam({
            query: { ...queryParams, isTFCOrder: orderCb.isTFCenabled },
            pathname: history.location.pathname,
            replace: history.replace,
          });
        }
        if (orderCb?.validateTpinTFC) {
          LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.REQ_ID);
          const { isin: isinTFC, qty: quantityTFC } = orderCb.validateTpinTFC;
          openTpinWindow({ isin: isinTFC, quantity: quantityTFC });
        }
        if (orderCb?.pushToFullScreen && !fullScreen) {
          setFullScreen();
        }
        if (orderCb?.showTFCInfoModal) {
          setTFCIntro(true);
        }
      }
    }
    if (current) {
      window.addEventListener('message', handleMessages);
    }
    return () => {
      if (current) {
        window.removeEventListener('message', handleMessages);
      }
    };
  }, [activeMultiGridCharts, addToast, chartConstants.data, chartConstants.failed, fullScreen,
    history, pathname, rangeParam, search, segment, setActiveMultiGridChartId, setActiveMultiGridCharts,
    setChartInitSuccess, setFullScreen, setMultiGrid]);

  useEffect(() => {
    // iframeDOM init
    const iframeDOM = iframeRef.current;
    if (iframeDOM) {
      iframeDOM.onload = () => {
        setIframeInit(true);
      };
    }
  }, []);

  // updateCharts method is called when clicked on different Scrip
  const updateCharts = useCallbackForEvents(({
    exchangeArg,
    securityIdArg,
    idArg,
    stockNameArg,
    segmentTypeArg,
    segmentArg,
  }) => {
    bridgeInterface({ action: { clearStreamingInfo: { value: true } } });
    if (iframeRef.current) {
      const iframeDOM = iframeRef.current;
      const RANGES_FOR_DERIVATIVES = chartConstants.data?.fno?.range || [];
      const chartConfig = {
        setRange: {
          value: (segmentArg === SEGMENT_TYPES.DERIVATIVES && RANGES_FOR_DERIVATIVES.indexOf(activeRange) === -1)
            ? RANGES_FOR_DERIVATIVES[0]?.value : activeRange,
        },
        setChartMode: { value: activeTheme },
        setChartLayoutType: { value: toggleDetails ? CHART_LAYOUT_TYPE.ADVANCED : CHART_LAYOUT_TYPE.BASIC },
      };
      const stockDetails = {
        stockName: stockNameArg,
        stockId: idArg,
        stockExchangeType: exchangeArg,
        securityId: securityIdArg,
        segmentType: segmentTypeArg,
      };
      let shareId = null;
      if (window.location.href.includes('shareChart=')) {
        let shareChartParams = window.location.href.slice(window.location.href.indexOf('shareChart='));
        if (shareChartParams.includes('&') && shareChartParams[shareChartParams.length - 1] !== '&') {
          shareChartParams = shareChartParams.slice(0, shareChartParams.lastIndexOf('&'));
        }
        shareId = shareChartParams.slice(shareChartParams.indexOf('=') + 1);
      }
      iframeDOM.contentWindow.postMessage({
        actionType: 'initChart',
        chartConfig,
        stockDetails,
        headers,
        shareId,
      }, '*');
    }
  }, [activeChart, toggleDetails, activeRange, chartConstants.data]);

  useEffect(() => {
    // update charts
    if (isIframeInit && !isLoading && id) {
      setFirstValueInRange(null);
      updateCharts({
        exchangeArg: instrumentType === INSTRUMENTS.INDEX ? 'IDX' : exchange,
        securityIdArg: securityId,
        idArg: id,
        stockNameArg: stockName,
        segmentTypeArg: SEGMENT_TYPE[instrumentType],
        segmentArg: segment,
      });
    }
  }, [exchange, isIframeInit, isLoading, securityId, id, stockName, updateCharts, instrumentType, segment]);

  useEffect(() => {
    if (multiGridInitChartsData) {
      const {
        name: stockNameArg, id: idArg, exchange: exchangeArg, security_id: securityIdArg,
        instrument_type: instrumentTypeArg, segment: segmentArg,
      } = multiGridInitChartsData;
      updateCharts({
        exchangeArg: instrumentTypeArg === INSTRUMENTS.INDEX ? 'IDX' : exchangeArg,
        securityIdArg,
        idArg,
        stockNameArg,
        segmentTypeArg: SEGMENT_TYPE[instrumentTypeArg],
        segmentArg,
      });
    }
  }, [multiGridInitChartsData, updateCharts]);

  useEffect(() => {
    if (isLoading) setIsChartsLoading(true);
  }, [isLoading]);

  useEffect(() => {
    if (ohlc && isChartInit) {
      bridgeInterface({
        action: {
          setOhlcPacket: {
            value: [moment().format('DD-MM-YYYY hh:mm'),
              ohlc.Open, ohlc.High, ohlc.Low, ohlc.pClose, ohlc.tradeVolume],
          },
        },
      });
    }
  }, [bridgeInterface, isChartInit, ohlc]);

  useEffect(() => {
    if (isCompareChartInstance && compareChartPmlIds.length
      && compareChartOHLCData && compareChartPmlIds.length === compareChartOHLCData.length && isChartInit) {
      // send compare ohlc
      compareChartOHLCData.forEach((compareChartOHLC) => {
        bridgeInterface({
          action: {
            setOhlcPacket: {
              value: [moment().format('DD-MM-YYYY hh:mm'), compareChartOHLC.Open,
                compareChartOHLC.High, compareChartOHLC.Low, compareChartOHLC.pClose, compareChartOHLC.tradeVolume],
              pmlId: compareChartOHLC.id,
            },
          },
        });
      });
    }
  }, [bridgeInterface, isCompareChartInstance, compareChartPmlIds, compareChartOHLCData, isChartInit]);

  useEffect(() => {
    if (isMultiGrid && multiGridUniqueIds.length
      && multiGridChartOHLCData && multiGridUniqueIds.length === multiGridChartOHLCData.length
      && (lastMultigridAddedId.current || isReload.current)) {
      isReload.current = false;
      // send multigrid ohlc
      let filteredOHLCdata = multiGridChartOHLCData;
      if (lastMultigridAddedId.current) {
        // Filter out the last added multigrid ID to trigger OHLC bridge interface call
        filteredOHLCdata = multiGridChartOHLCData.filter(
          (ohlcPacket) => ohlcPacket.id === lastMultigridAddedId.current,
        );
      }
      filteredOHLCdata.forEach((multiGridChartOHLC) => {
        bridgeInterface({
          action: {
            setOhlcPacket: {
              value: [moment().format('DD-MM-YYYY hh:mm'), multiGridChartOHLC.Open, multiGridChartOHLC.High,
                multiGridChartOHLC.Low, multiGridChartOHLC.pClose, multiGridChartOHLC.tradeVolume],
              multiGridPmlId: multiGridChartOHLC.id,
            },
          },
        });
      });
    }
  }, [bridgeInterface, isMultiGrid, multiGridUniqueIds, multiGridChartOHLCData]);

  useEffect(() => {
    // send ltp
    if (ohlc && ltpId && ohlc?.id === ltpId) {
      bridgeInterface({
        action: {
          ltp: {
            value: {
              ltp,
              volume: tradeVolume || 0,
              status: mkt_status,
              amo,
            },
            broadcast_packet: constructBroadcastPacket({ lastUpdatedTime, lastTradeTime, openInterest }),
          },
        },
      });
    }
  }, [bridgeInterface, ltp, mkt_status, amo, tradeVolume, ohlc,
    ltpId, isChartInit, openInterest, lastTradeTime, lastUpdatedTime]);

  useEffect(() => {
    if (isCompareChartInstance && compareChartPmlIds.length && compareChartLtpData && isChartInit) {
      // send compare ltp
      compareChartLtpData.forEach((compareChartLtp) => {
        bridgeInterface({
          action: {
            ltp: {
              value: {
                ltp: compareChartLtp.ltp,
                volume: compareChartLtp.tradeVolume || 0,
              },
              pmlId: compareChartLtp.id,
              broadcast_packet: constructBroadcastPacket(compareChartLtp),
            },
          },
        });
      });
    }
  }, [bridgeInterface, isCompareChartInstance, compareChartPmlIds, compareChartLtpData, isChartInit]);

  useEffect(() => {
    if (isMultiGrid && multiGridUniqueIds.length && multiGridChartLtpData) {
      // send multigrid ltp
      multiGridChartLtpData.forEach((compareChartLtp) => {
        bridgeInterface({
          action: {
            ltp: {
              value: {
                ltp: compareChartLtp.ltp,
                volume: compareChartLtp.tradeVolume || 0,
              },
              multiGridPmlId: compareChartLtp.id,
              broadcast_packet: constructBroadcastPacket(compareChartLtp),
            },
          },
        });
      });
    }
  }, [bridgeInterface, isMultiGrid, multiGridUniqueIds, multiGridChartLtpData]);

  useEffect(() => {
    // send updated orders list from order feed
    if (isChartInit && ordersData) {
      bridgeInterface({ action: { updateOrdersBroadcast: { value: ordersData } } });
    }
  }, [bridgeInterface, isChartInit, ordersData]);

  useEffect(() => {
    // send updated orders list from order feed
    if (isChartInit && limitData) {
      bridgeInterface({ action: { updateCircuitLimit: { value: limitData } } });
    }
  }, [bridgeInterface, isChartInit, limitData]);

  useDidUpdateEffect(() => {
    // toggle charts from Basic <-> Advanced
    if (toggleDetails) {
      bridgeInterface({ action: { setChartLayoutType: { value: CHART_LAYOUT_TYPE.ADVANCED } } });
    } else {
      bridgeInterface({ action: { setChartLayoutType: { value: CHART_LAYOUT_TYPE.BASIC } } });
    }
  }, [bridgeInterface, toggleDetails]);

  const onChartChange = useCallbackForEvents((activeChartType) => {
    // remove all indicators in switched to mountain charts.
    if (activeChartType === CHART_TYPE.MOUNTAIN) {
      bridgeInterface({ action: { clearAllIndicators: { value: true } } });
    }
  }, [activeIndicators, instrumentType, isChartInit]);

  useEffect(() => {
    onChartChange(activeChart.value);
  }, [onChartChange, activeChart]);

  const handleRange = (range) => {
    sendEvent({
      event_category: 'charts_basic',
      event_action: 'range_changed',
      event_label: 'charts',
      vertical_name: 'stocks',
      event_label3: range,
      screenName: '/company_page',
    });
    bridgeInterface({ action: { setRange: { value: range } } });
  };

  const handleRemoveIndicator = (indicator) => {
    bridgeInterface({ action: { removeIndicator: { value: indicator } } });
  };

  const handleInterval = (interval) => {
    bridgeInterface({ action: { setTimeInterval: { value: interval } } });
  };

  const handleIndicators = (indicator) => {
    bridgeInterface({ action: { setIndicators: { value: indicator } } });
  };

  const handleChartType = (chartObj) => {
    bridgeInterface({ action: { setChartType: { value: chartObj.value } } });
  };

  const handleSettings = (settings) => {
    const [key, value] = Object.entries(settings)[0];
    bridgeInterface({ action: { [key]: { value } } });
  };

  const handleDrawingTools = (showDrawingToolBar) => {
    bridgeInterface({ action: { displayDrawingToolbar: { value: showDrawingToolBar } } });
  };

  const handleClearAllIndicators = () => {
    bridgeInterface({ action: { clearAllIndicators: { value: true } } });
  };

  const handleIndicatorsSettings = (indicator) => {
    bridgeInterface({ action: { updateIndicator: { value: { type: indicator.type, name: indicator.name } } } });
  };

  const handleAdvancedChartAggregation = (chartObj) => {
    bridgeInterface({ action: { editAdvancedChartAggregation: { value: chartObj.value } } });
  };

  const handleAddLayout = (label) => {
    bridgeInterface({ action: { layoutActions: { operation: 'add', value: { label } } } });
  };

  // action may be delete/apply/update
  const handleLayoutAction = (layoutId, layoutLabel, action) => {
    bridgeInterface({
      action: {
        layoutActions: {
          operation: action,
          value: { id: layoutId, label: layoutLabel },
        },
      },
    });
  };

  const handleShare = useCallback(() => {
    if (isChartInit) {
      bridgeInterface({ action: { createShareChart: { pmlId: ltpId } } });
    }
  }, [bridgeInterface, isChartInit, ltpId]);

  const handleRefreshCharts = useCallback(() => {
    bridgeInterface({ action: { resetToDefaultConfig: { value: true } } });
  }, [bridgeInterface]);

  const updateToolTypeOpen = (toolId) => {
    setToolTypeOpen(toolId);
  };

  if (disableToggleCharts) {
    disableToggleCharts(!isChartInit);
  }

  return (
    // eslint-disable-next-line react/jsx-no-constructed-context-values
    <ChartsContext.Provider value={{
      isChartInit,
      ltp,
      change,
      percentageChange,
      id,
      isin,
      stockName,
      toggleDetails,
      securityId,
      exchange,
      segment,
      iframeRef,
      chartsRef,
      activeRange,
      fullScreen,
      setFullScreen,
      setTFCView,
      setRange,
      handleRange,
      activeInterval,
      handleInterval,
      activeChart,
      handleChartType,
      handleIndicators,
      activeIndicators,
      selectedSettingOptions,
      handleSettings,
      handleRemoveIndicator,
      handleDrawingTools,
      handleClearAllIndicators,
      handleIndicatorsSettings,
      handleShare,
      handleRefreshCharts,
      updateToolTypeOpen,
      toolTypeOpen,
      shareChartId,
      isDrawingToolbarActive,
      instrumentType,
      rangeValue,
      inProgress: chartConstants.loading || isLoading || isChartsLoading || !isChartInit,
      tick_size,
      lot_size,
      handleAdvancedChartAggregation,
      isError,
      layouts,
      handleAddLayout,
      handleLayoutAction,
      displayTFCIntro,
      setTFCIntro,
      setShareChartId,
      ...props,
    }}
    >
      {children}
    </ChartsContext.Provider>
  );
}

const useCharts = () => {
  const ctx = useContext(ChartsContext);
  return ctx;
};

export { ChartsContext, ChartsProvider, useCharts };
