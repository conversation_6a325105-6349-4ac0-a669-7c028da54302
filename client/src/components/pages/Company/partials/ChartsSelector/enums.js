export const CHART_MODES = [{
  id: 'tradingView',
  label: 'Trading View Chart',
}, {
  id: 'chartIq',
  label: 'ChartIQ',
}];

export const TRADING_VIEW_MODAL_DATA = [
  {
    title: 'Important Update',
    description: ['We have successfully set the',
      '"Trading View"',
      'charting library as your default charting tool. Enjoy exploring its enhanced features and advanced capabilities. Remember, you have the flexibility to change this preference in your profile/chart settings at any time.'],
    okButton: 'OK',
  },
  {
    title: 'Important Update',
    description: ['Dear User, We now offer the new and enhanced ', '"Trading View"',
      'charting tool, packed with numerous features. To optimize your experience, we can set it as your default charting library. Your preferences of Chart IQ will remain unchanged; you can switch to it from the charts settings (top right dropdown).',
      ' Click "OK" to continue or "Cancel" to stick with Chart IQ for this session.'],
    okButton: 'OK',
    cancelButton: 'Cancel',
  },
];
