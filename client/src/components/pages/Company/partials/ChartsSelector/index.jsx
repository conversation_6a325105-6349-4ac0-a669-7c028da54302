import { useGet<PERSON><PERSON> } from '@common/UseApi';
import { useIrData } from '@layout/App/UserContext';
import React, { useState, useEffect, useCallback } from 'react';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import CustomDropdown from '@common/CustomDropdown';

import { ICON_NAME } from '@common/Icon/enums';
import Icon from '@common/Icon';
import { getEquityStorefront } from '@pages/Home/partials/PromoBanners/api';
import { useModal } from '@layout/App/ModalContext';
import { LOCAL_STORAGE_KEYS } from '@layout/LoggedInLayout/enums';
import LocalStorage from '@service/LocalStorage';
import { MODAL_TYPES } from '@common/Modal/enums';
import { classNames as cx } from '@utils/style';
import { button } from '@commonStyles';
import { sendEvent } from '@service/AnalyticsService';
import styles from './index.scss';
import { CHART_MODES, TRADING_VIEW_MODAL_DATA } from './enums';
import CONFIG from '../../../../../config';

const TradingViewModal = ({
  selectedChart, handleChange, closeModal, setRecentlyShown,
}) => {
  if (selectedChart === CHART_MODES[0].id) {
    return (
      <div>
        <div className={styles.tvModalTitle}>
          {TRADING_VIEW_MODAL_DATA[0].title}
        </div>
        <div className={styles.tvModalDescription}>
          <div>{TRADING_VIEW_MODAL_DATA[0].description[0]}</div>
          <span>{TRADING_VIEW_MODAL_DATA[0].description[1]}</span>
          <span>
            {TRADING_VIEW_MODAL_DATA[0].description[2]}
          </span>
        </div>
        <div className={`${styles.tvModalGreetings}`}>
          <button
            type="button"
            className={cx([button.btn, styles.btnView, styles.okBtn], {
            })}
            onClick={closeModal}
          >
            {TRADING_VIEW_MODAL_DATA[0].okButton}
          </button>
        </div>
      </div>
    );
  }
  return (
    <div>
      <div className={styles.tvModalTitle}>
        {TRADING_VIEW_MODAL_DATA[1].title}
      </div>
      <div className={`${styles.tvModalDescription} ${styles.chartIqDescription}`}>
        <span>{TRADING_VIEW_MODAL_DATA[1].description[0]}</span>
        <span>
          {' '}
          {TRADING_VIEW_MODAL_DATA[1].description[1]}
        </span>
        <span>
          {' '}
          {TRADING_VIEW_MODAL_DATA[1].description[2]}
        </span>
        <div>
          {TRADING_VIEW_MODAL_DATA[1].description[3]}
        </div>
      </div>
      <div className={styles.tvModalButtons}>
        <button
          type="button"
          className={cx([button.btn, styles.btnView, styles.okBtn], {
          })}
          onClick={() => {
            closeModal();
            handleChange(CHART_MODES[0]);
            setRecentlyShown(true);
          }}
        >
          {TRADING_VIEW_MODAL_DATA[1].okButton}
        </button>
        <button
          type="button"
          className={cx([button.btn, styles.btnView], {
          })}
          onClick={closeModal}
        >
          {TRADING_VIEW_MODAL_DATA[1].cancelButton}
        </button>
      </div>
    </div>
  );
};

function ChartsSelector() {
  const [TvData, setTvData] = useState({});
  const { userId } = useIrData();
  const { makeRequest } = useGetApi();
  const [chartTypes, setChartTypes] = useState(CHART_MODES);
  const { selectedChart, setSelectedChartType } = useLoggedInContext();
  const [recentlyShown, setRecentlyShown] = useState(false);
  const [label, setLabel] = useState(CHART_MODES.find((chartId) => chartId.id === selectedChart).label);
  const { openModal, closeModal } = useModal();

  useEffect(() => {
    const getStorefrontData = async () => {
      try {
        const { page } = await makeRequest(getEquityStorefront(userId, { user_id: userId }));
        page.forEach((item) => {
          if (item.id === CONFIG.TRADING_VIEW_ID) {
            setTvData(item.views[0]?.items[0] || {});
          }
        });
      } catch (err) { }
    };
    getStorefrontData();
  }, [makeRequest, userId]);

  useEffect(() => {
    if (TvData?.isTvPanelEnabled === 'true') {
      const TvPanel = {
        id: TvData.name,
        label: TvData.title,
        url: TvData.url,
      };
      setChartTypes((oldTypes) => [...oldTypes, TvPanel]);
    }
  }, [TvData]);

  const handleChange = useCallback((mode) => {
    sendEvent({
      event_category: 'charts_advanced',
      event_action: 'chart_selected',
      event_label: 'charts',
      vertical_name: 'stocks',
      event_label3: mode.label,
      screenName: '/charts',
    });
    if (CHART_MODES.includes(mode)) {
      setSelectedChartType(mode.id);
      setLabel(mode.label);
    }
    const tvSelected = {
      count: 3,
      lastShown: new Date().toLocaleDateString(),
    };
    LocalStorage.set(LOCAL_STORAGE_KEYS.TRADING_VIEW_SELECTED, JSON.stringify(tvSelected), '', false);
  }, [setSelectedChartType]);

  useEffect(() => {
    let tvSelected = JSON.parse(LocalStorage.get(LOCAL_STORAGE_KEYS.TRADING_VIEW_SELECTED, false) || null);
    if (!tvSelected || tvSelected.count < 3) {
      if (LocalStorage.get(LOCAL_STORAGE_KEYS.CHART_SELECTED, false) === null) {
        tvSelected = {
          lastShown: new Date().toLocaleDateString(),
          count: 3,
        };
        LocalStorage.set(LOCAL_STORAGE_KEYS.TRADING_VIEW_SELECTED, JSON.stringify(tvSelected));
      } else if (!recentlyShown && LocalStorage.get(LOCAL_STORAGE_KEYS.CHART_SELECTED, false) === CHART_MODES[0].id) {
        openModal({
          Component: TradingViewModal,
          componentProps: { selectedChart: CHART_MODES[0].id, closeModal },
          type: MODAL_TYPES.POPUP,
          closeIcon: ICON_NAME.CLOSE_POPUP,
        });
        tvSelected = {
          lastShown: new Date().toLocaleDateString(),
          count: 3,
        };
        LocalStorage.set(LOCAL_STORAGE_KEYS.TRADING_VIEW_SELECTED, JSON.stringify(tvSelected));
      } else if (LocalStorage.get(LOCAL_STORAGE_KEYS.CHART_SELECTED, false) === CHART_MODES[1].id
      && (new Date().toLocaleDateString() > tvSelected?.lastShown || !tvSelected)) {
        tvSelected = {
          count: (tvSelected?.count || 0) + 1,
          lastShown: new Date().toLocaleDateString(),
        };
        openModal({
          Component: TradingViewModal,
          componentProps: {
            selectedChart: CHART_MODES[1].id, handleChange, closeModal, setRecentlyShown,
          },
          type: MODAL_TYPES.POPUP,
          closeIcon: ICON_NAME.CLOSE_POPUP,
        });
        LocalStorage.set(LOCAL_STORAGE_KEYS.TRADING_VIEW_SELECTED, JSON.stringify(tvSelected), '', false);
      }
    }
  }, [TvData, selectedChart, openModal, handleChange, closeModal, recentlyShown]);

  const customComponents = [
    {
      id: TvData.name,
      component:
  <div className={styles.tradingView} role="presentation" onClick={() => window.open(TvData?.url)}>
    {TvData.isBeta === 'true' ? <div className={styles.beta}> BETA</div> : null}
    <div className={styles.tradingViewTitle}>
      <div>{TvData.title}</div>
      <Icon name={ICON_NAME.REDIRECTION_ICON} />
    </div>
  </div>,
    },
  ];

  const onToggle = (isOpen) => {
    if (isOpen) {
      sendEvent({
        event_category: 'charts_advanced',
        event_action: 'chart_selection_cta_clicked',
        event_label: 'charts',
        vertical_name: 'stocks',
        screeName: '/charts',
      });
    }
  };

  return (
    <div className={styles.chartType}>
      <CustomDropdown
        selectedValue={selectedChart}
        options={chartTypes}
        displayValue={label}
        onChange={handleChange}
        containerClass={styles.containerClass}
        iconClass={styles.iconClass}
        optionDefaultClass={styles.chartsOption}
        dropdownClass={styles.chartDropdownClass}
        optionSelectedClass={styles.selectedOption}
        tick
        customComponents={customComponents}
        onToggle={onToggle}
      />
    </div>
  );
}
export default ChartsSelector;
