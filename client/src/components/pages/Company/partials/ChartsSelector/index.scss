@import '@/styles/main';

$beta-background-color: rgba(238, 156, 22, .1);
$dropDownBorder: #a7abb680;

.chartType {
  margin-left: auto;
  margin-right: 1.4rem;
}

.containerClass {
  width: 23rem;
  height: 3.2rem;
  color: $secondary-color;
  display: flex;
  border-radius: .4rem;
  border: solid 1px $dropDownBorder;

  @include typography(h7, semibold);
}

.iconClass > div {
  border-color: $secondary-color;
}

.chartsOption {
  text-align: center;
  padding: 0 1rem;
  height: 3.8rem;
  /* stylelint-disable-next-line declaration-no-important */
  border: none !important;

  &:hover {
    background: $grey5;

    @include typography(h7, semibold);
  }
}

.chartDropdownClass {
  padding: 1.5rem;
  margin-top: .2rem;

  @include typography(h7, default);
}

.tradingView {
  height: 3.8rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.beta {
  align-self: flex-start;
  background-color: $beta-background-color;
  color: $yellow;
  line-height: 1;

  @include typography(h9);
}

.tradingViewTitle {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-right: 1.2rem;
}

.selectedOption {
  color: $secondary-color;
}

.tvModalTitle {
  margin-top: 3rem;
  text-align: center;

  @include typography(h5, bold);
}

.tvModalDescription {
  padding: .6rem 3.8rem 1rem;
  text-align: center;

  @include typography(h7);

  > span:nth-child(2) {
    @include typography(h7, bold);
  }

  > div {
    margin-top: 2rem;
  }
}

.chartIqDescription {
  /* stylelint-disable-next-line declaration-no-important */
  padding: .6rem 4.8rem 2rem !important;
}

.tvModalGreetings {
  text-align: center;
  margin: 1rem 0 3rem;
  display: flex;
  justify-content: center;

  @include typography(h7, bold);

  > button {
    margin: 0;
  }
}

.tvModalButtons {
  display: flex;
  justify-content: center;
}

.btnView {
  color: $secondary-color;
  border: .1rem solid $secondary-color;
  border-radius: .3rem;
  width: 13rem;
  height: 3rem;
  margin: 0 0 3rem 1rem;

  @include typography(h7, semibold);
}

.okBtn {
  color: $default;
  background-color: $secondary-color;
}
