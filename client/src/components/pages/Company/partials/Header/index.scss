@import '@/styles/main';

$color: #eaeaea;

.headerLoader {
  padding-top: 1.5rem;
  padding-bottom: 1rem;
  margin-bottom: 1rem;

  > div > div:first-child {
    margin-bottom: 1rem;
  }
}

.wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2.5rem;

  @include respond(phone) {
    margin-bottom: 0;
  }

  > div {
    display: flex;
  }
}

.btn {
  color: $forever-white;
  width: 7.3rem;
  height: 4rem;
  border-radius: .25rem;
  margin-left: 1rem;

  @include typography(h6);
}

.btnTfc {
  width: 8.6rem;
}

.tfcButtonLabel {
  text-align: left;
  padding-left: .3rem;

  @include typography(h9, semibold);
}

.chartBtn {
  width: auto;
  padding: 0;
}

.rightContainer {
  display: flex;
  justify-content: space-between;
}

.flexEnd {
  justify-content: flex-end;
}

.iconLayout {
  display: flex;
}

.btnSwitch {
  background-color: $grey4;
}

.companyHeader {
  @include respond(phone) {
    padding: 1.5rem 1rem 1rem;
  }
}

.companyName {
  display: flex;
  align-items: center;
  margin-bottom: .5rem;
  color: $grey1;

  @include typography(h3, semibold);

  @include respond(phone) {
    color: $grey0;
    margin: 0 1rem .5rem 0;

    @include typography(h5, semibold);
  }
}

.bookmarkIcon {
  margin-left: 1.5rem;
  cursor: pointer;

  @include respond(phone) {
    margin-left: .8rem;
  }
}

.companyHoldingsContainer {
  display: flex;
}

.companyHoldings {
  line-height: 3rem;
  color: $grey1;

  @include typography(h1, semibold);

  @include respond(phone) {
    color: $grey0;
    margin-right: .3rem;

    @include typography(h2, bold);
  }
}

.pl {
  margin-left: 1rem;
  position: relative;

  @include respond(phone) {
    margin-left: .3rem;

    > div,
    > span {
      @include typography(h6);

      @include respond(phone) {
        @include typography(h7);
      }
    }
  }

  > div,
  > span {
    @include typography(h5);

    @include respond(phone) {
      @include typography(h7);
    }
  }
}

.leftContainer {
  display: flex;
  align-items: flex-end;

  @include respond(phone) {
    align-items: normal;
  }
}

.subHeader {
  color: $grey3;
  display: flex;

  @include typography(h6, 'semibold');

  .subHeaderValues {
    color: $grey0;
    margin-left: .35rem;
  }
}

.seriesWrapper {
  display: flex;
}

.isin {
  margin-right: .3rem;
}

.series {
  margin-left: 2.5rem;
}

.exchangeName {
  color: $grey2;
  align-self: center;
  margin-left: .5rem;

  @include typography(h8);
}

.exchangeLayout {
  display: flex;
  margin-left: 1rem;

  @include respond(phone) {
    white-space: nowrap;
  }
}

.hasSibling {
  cursor: pointer;
}

.change {
  display: flex;
  align-items: center;

  > :first-child {
    margin-right: .5rem;
  }
}

.disabled {
  pointer-events: none;
  opacity: .5;
}

.dropdownWrapper {
  padding-left: 1rem;
  display: flex;
  align-items: center;

  .menuItem {
    height: 4.5rem;
    margin-left: -1rem;

    > span {
      padding-left: 1rem;

      @include typography(h7);
    }
  }
}

.popper {
  z-index: 30;
  position: absolute;
  min-width: 11rem;
  background-color: $default;
  right: 0;
  top: 2.8rem;

  > div:first-child {
    z-index: 9999;
    min-width: 12.5rem;
    top: 3rem;
    left: -1rem;

    > div {
      /* stylelint-disable-next-line declaration-no-important */
      background-color: $default !important;
      border-bottom: solid 1px $grey4;
    }

    > div:first-child {
      left: 8.6rem;
      border-bottom: 0;
    }

    > div:last-child {
      /* stylelint-disable-next-line declaration-no-important */
      background-color: $bg-color !important;
    }
  }
}

.backArrowIcon {
  display: none;

  @include respond(phone) {
    display: inline-block;
    margin-right: 1rem;
  }
}

.indexSearch {
  > div {
    > div:first-child {
      border-radius: 20px;
    }
  }
}

.exchange {
  margin-left: 1rem;
  color: $grey2;
  background-color: $grey4;
  padding: .5rem 1rem;
  display: flex;
  align-items: center;
  border-radius: 20px;

  @include typography(h7, default);
}

.hidden {
  display: none;
}

.indexCompanyName {
  display: flex;
  align-items: start;
  justify-content: space-between;
  margin-bottom: .5rem;
  color: $grey1;
  width: 100%;

  @include typography(h2, semibold);

  > span {
    display: flex;
  }
}

.customDropdown {
  > div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: -1rem;
    /* stylelint-disable-next-line declaration-no-important */
    height: 0 !important;

    > div {
      background-color: $default;
      border: 1px solid $grey4;
      margin-left: 1rem;
      border-radius: 20px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }
}

.optionClass {
  height: 3rem;
}

.dropdownClass {
  max-height: 19rem;
  overflow: auto;

  @include typography(h7, default);
}

.dropdown {
  width: 10rem;
  height: 2rem;
  padding: .2rem 1rem .4rem .8rem;
  border-radius: 2px;
  background-color: $grey4;
  color: $grey1;

  > div:first-child {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @include typography(h7, default);
  }

  > div:nth-child(2) {
    > div {
      // scss-lint:disable NestingDepth
      > div {
        border-color: $primary-color;
      }
    }
  }
}

.futureOptionWrapper {
  display: flex;
  flex-direction: row;
  align-items: end;
  margin-bottom: 2.5rem;
}

.icon {
  margin-left: 1rem;
  padding: .5rem 1rem;
  display: flex;
  align-items: center;
}

.chartIcon {
  margin-left: 1rem;
  display: flex;
  align-items: center;
}

.expiryContainer {
  margin-left: .5rem;
  padding-left: .5rem;
  border-left: 1px solid $grey4;
  color: $grey2;

  .expiryDate {
    margin-left: .5rem;
  }
}

.container {
  border-radius: .4rem;
  position: relative;
  padding: .5rem 1rem;
  cursor: default;
  color: $default;
  background-color: $grey2;
  z-index: 4;

  @include box-shadow(0, 3px, 10px);

  &::before {
    left: 87%;
    top: -.5rem;
    border-bottom: .6rem solid $grey2;
    border-left: .6rem solid transparent;
    border-right: .6rem solid transparent;
    content: '';
    position: absolute;
  }
}

.iconStyles {
  padding: .4rem .7rem;
  cursor: pointer;
}

.customPopperStyle {
  top: 3rem;
  left: -9rem;
}
