import { debounce } from 'lodash';
import Popper from '@common/Popper';
import React, {
  useContext, useMemo, useCallback, useEffect,
} from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { map } from 'rxjs/operators';
import useObservable from '@common/useObservable';
import LiveIcon from '@common/LiveIcon';
import useFeed from '@common/useFeed';
import { INSTRUMENTS, ROUTE_NAME, SEGMENT_TYPES } from '@utils/enum';
import WithLoader from '@common/WithLoader';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import { ChangeWithPercent, ChangeIcon } from '@common/Prices';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { sendEvent } from '@service/AnalyticsService';
import { classNames as cx } from '@utils/style';
import DropdownOptions from '@modules/Positions/common/DropdownOptions';
import { RESPONSE_TYPES, REQUEST_TYPES } from '@service/dataConfig';
import usePlaceOrder from '@common/usePlaceOrder';
import Icon from '@common/Icon';
import { ICON_NAME, staticImagePath } from '@common/Icon/enums';
import { WatchlistContext } from '@modules/WatchlistSidebar/watchlistContext';
import { BookmarkedIcon, NotbookmarkedIcon } from '@modules/WatchlistSidebar/partials/Searchbox/Suggestion';
import GAElement from '@common/GAElement';
import { CHARTS_GA } from '@constants/ga-events';
import Search from '@modules/FnoOptionChain/partials/Search';
import { URL_PARAM } from '@modules/FnoOptionChain/enums';
import If from '@common/If';
import CustomDropdown from '@common/CustomDropdown';
import {
  getLtpChangeFromTradeClose, getCombinedFeed, formatPrice,
  mobileBrowser, isBondInstrument, emptyFn,
} from '@utils';
import { button as btnStyles } from '@commonStyles';
import Routes from '@/routes';
import styles from './styles';
import OptionScrip from '../OptionScrip';
import { ALL_TYPES, MORE_OPTIONS, MORE_OPTIONS_WITH_BASKET_ORDERS } from './enums';
import ChartsSelector from '../ChartsSelector';
import PinScripOptions from '../../../../modules/WatchlistSidebar/partials/PinScripOptions';

const requiredFeedResponse = [RESPONSE_TYPES.TRADE, RESPONSE_TYPES.P_CLOSE];

function CompanyHeader(props) {
  const {
    id,
    name,
    exchange,
    securityId,
    segment,
    exch_symbol,
    toggleExpand,
    toggleDetails,
    siblingId,
    tick_size,
    lot_size,
    isin,
    instrument_type,
    toggleDisabled,
    hideBuySellButtons,
    hideBookmarkIcon,
    isListingToday,
    showFnoSearch,
    expiry_display_date,
    requestTFC,
    initChartSuccess,
    isAllowedUser,
    series,
    currentTab,
    onChange,
    optionsConfig,
    selectedOption,
    showBasketOrder,
    hideMoreOptions = false,
    expiry_display_date: expiryDate,
    handleSelectOption,
    currentLtp = null,
    setCurrentLtp = emptyFn,
  } = props;
  const { selectedChart } = useLoggedInContext();
  const { isAdvancedChartSelected } = useContext(WatchlistContext);
  const isBond = isBondInstrument(instrument_type);
  const history = useHistory();
  const eventAction = 'buy_sell_swipe_action_completed';
  const eventCategory = 'fno_option_chain';
  const eventLabel = `fno_dashboard|${name}|${expiry_display_date}`;
  const { buy, sell } = usePlaceOrder(name, exchange, securityId, {
    isin,
    segment,
    tickSize: tick_size / 100,
    lotSize: lot_size,
    instrumentType: instrument_type,
    eventAction,
    eventCategory,
    eventLabel,
    instrument_Id: id,
    exch_symbol,
  });

  const isFutureOptionPage = history.location.pathname.includes(URL_PARAM.OPTION_PARAM)
  || history.location.pathname.includes(URL_PARAM.FUTURE_PARAM);

  const { handleExpansion, stocksToWatchlistMap } = useContext(WatchlistContext);
  const watchlistsOfSId = stocksToWatchlistMap[id] || [];
  const hideBuySell = hideBuySellButtons || mobileBrowser();
  const stockFeed = useFeed(
    REQUEST_TYPES.STOCK,
    requiredFeedResponse,
    { exchange, securityId, segment },
  );

  const computedDetails = useObservable(
    () => getCombinedFeed(stockFeed).pipe(
      map(([trade, close]) => getLtpChangeFromTradeClose(trade, close)),
    ),
    [stockFeed],
  );

  const ltp = computedDetails?.ltp;

  useEffect(() => {
    if (currentLtp === null && ltp) {
      setCurrentLtp(ltp);
    }
  }, [currentLtp, ltp, setCurrentLtp]);

  let change = computedDetails?.change;
  if (computedDetails?.percentageChange === Infinity && ltp !== 0) {
    change = 0;
  }

  const { pathname } = useLocation();
  const arr = pathname.split('/');
  arr.splice(-1);
  const switchExchange = () => {
    if (siblingId != null) {
      history.push(`${arr.join('/')}/${siblingId}`);
    }
  };
  const onFnoSearchClick = (suggestion) => {
    if (history.location.pathname.includes(URL_PARAM.OPTION_PARAM)) {
      history.push(`${Routes[ROUTE_NAME.FNO_OPTION_CHAIN].url}/${suggestion.id}/${suggestion.name}/${suggestion.exchange}`);
    }
    if (history.location.pathname.includes(URL_PARAM.FUTURE_PARAM)) {
      history.push(`${Routes[ROUTE_NAME.FUTURE_CONTRACT].url}/${suggestion.id}/${suggestion.name}/${suggestion.exchange}`);
    }
  };

  const filterOptions = (e) => (!((e.name === ALL_TYPES.SIP || e.name === ALL_TYPES.MARGIN_PLEDGE)
                                && segment !== SEGMENT_TYPES.CASH));

  const debounceToggleExpand = useMemo(() => (toggleExpand ? debounce(toggleExpand, 300) : null), [toggleExpand]);
  const isFno = history.location.pathname.indexOf('fno') > -1;
  const onBackClick = useCallback(() => history.goBack(), [history]);

  const isFNOScrip = [INSTRUMENTS.FUTIDX, INSTRUMENTS.FUTSTK,
    INSTRUMENTS.OPTIDX, INSTRUMENTS.OPTSTK].includes(instrument_type);

  return (
    <div className={styles.companyHeader}>
      <div className={styles.companyName}>
        <div role="presentation" onClick={onBackClick} className={styles.backArrowIcon}>
          <img src={`${staticImagePath}/common/back-arrow.svg`} alt="Back Icon" />
        </div>
        <div className={isFutureOptionPage ? styles.indexCompanyName : ''}>
          <span>
            {name}
            <span className={isFutureOptionPage ? styles.exchange : styles.hidden}>{exchange}</span>
            <Link to={`${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${id}`} className={isFutureOptionPage ? styles.icon : styles.hidden}>
              <Icon name={ICON_NAME.REDIRECTION_ICON} />
            </Link>
          </span>
          {showFnoSearch && (
          <div className={styles.indexSearch}>
            <Search onChange={onFnoSearchClick} defaultView pageName={props?.name} />
          </div>
          )}
        </div>
        {!hideBookmarkIcon && (
        <div
          role="presentation"
          onClick={handleExpansion.bind(null, id, mobileBrowser())}
          className={styles.bookmarkIcon}
        >
          {watchlistsOfSId.length
            ? <BookmarkedIcon size={4} /> : <NotbookmarkedIcon size={4} />}
        </div>
        )}

        { isAdvancedChartSelected && <ChartsSelector />}
      </div>
      <div className={isFutureOptionPage ? styles.futureOptionWrapper : styles.wrapper}>
        <div className={styles.leftContainer}>
          <span className={styles.companyHoldings}>{formatPrice(ltp)}</span>
          <div className={styles.change}>
            <ChangeWithPercent
              value={change}
              withRupee={false}
              percent={computedDetails?.percentageChange}
              className={styles.pl}
            />
            <ChangeIcon value={change} />
            <div className={styles.iconLayout}>
              <div
                className={cx([styles.exchangeLayout], {
                  [styles.hasSibling]: siblingId,
                })}
                role="presentation"
                onClick={switchExchange}
              >
                <LiveIcon exchange={exchange} segment={segment} showExchange showClosed={isListingToday} />
                <If test={isFNOScrip}>
                  <span className={styles.expiryContainer}>
                    Expiry:
                    <span className={styles.expiryDate}>{expiryDate}</span>
                  </span>
                </If>
              </div>
            </div>
          </div>
        </div>
        <Link to={`${Routes[ROUTE_NAME.COMPANY_PAGE].url}/${id}?toggleDetails=true`} className={isFutureOptionPage ? styles.chartIcon : styles.hidden}>
          <Icon name={ICON_NAME.CHART_ICON} />
        </Link>
        <If test={isFutureOptionPage && currentTab === 1}>
          <div className={styles.customDropdown}>
            <CustomDropdown
              selectedValue={selectedOption}
              options={optionsConfig || []}
              displayValue={selectedOption?.label}
              onChange={onChange}
              containerClass={`${styles.dropdown}`}
              optionDefaultClass={styles.optionClass}
              dropdownClass={styles.dropdownClass}
            />
          </div>
        </If>
        {!hideBuySell && (
        <div className={styles.rightContainer}>
          <button
            className={cx([styles.btn, btnStyles.btn, btnStyles.btnSmall, btnStyles.greenBtnFill])}
            onClick={() => {
              buy();
              sendEvent({
                event_category: 'order',
                event_action: 'order_entry_from_company_page',
                event_label: 'orders',
                vertical_name: 'stocks',
                screenName: '/stocks_orders',
              });
              if (!isBond && isFno) {
                sendEvent({
                  event_category: 'fno_option_chain',
                  event_action: 'buy_sell_clicked',
                  event_label: `fno_dashboard|${name}|${expiry_display_date}|buy`,
                  vertical_name: 'derivatives',
                });
              }
            }}
          >
            Buy
          </button>
          <button
            className={cx([styles.btn, btnStyles.btn, btnStyles.btnSmall, btnStyles.redBtnFill])}
            onClick={() => {
              sell();
              sendEvent({
                event_category: 'order',
                event_action: 'order_entry_from_company_page',
                event_label: 'orders',
                vertical_name: 'stocks',
                screenName: '/stocks_orders',
              });
              if (!isBond && isFno) {
                sendEvent({
                  event_category: 'fno_option_chain',
                  event_action: 'buy_sell_clicked',
                  event_label: `fno_dashboard|${name}|${expiry_display_date}|sell`,
                  vertical_name: 'derivatives',
                });
              }
            }}
          >
            Sell
          </button>
          <OptionScrip id={id} name={name} />
          {(toggleDetails || segment === SEGMENT_TYPES.DERIVATIVES) && isAllowedUser
            && selectedChart === SELECT_CHART_MODES[0].id
            ? (
              <button
                className={cx([styles.btn, styles.btnTfc, btnStyles.btn,
                  btnStyles.btnSmall, btnStyles.secondaryBtnFill],
                { [styles.disabled]: segment === SEGMENT_TYPES.DERIVATIVES ? !initChartSuccess : toggleDisabled })}
                onClick={() => requestTFC(true)}
                disabled={segment === SEGMENT_TYPES.DERIVATIVES ? !initChartSuccess : toggleDisabled}
              >
                <Icon name={ICON_NAME.TFC_ICON} size={4} />
                <span className={styles.tfcButtonLabel}>Trade from Charts</span>
              </button>
            )
            : ''}
          {segment !== SEGMENT_TYPES.DERIVATIVES && !isBond && (
          <GAElement
            gaEventCategory={CHARTS_GA.CHART_CLICKED.EVENT_CATEGORY}
            gaEventAction={CHARTS_GA.CHART_CLICKED.EVENT_ACTION}
          >
            <button
              className={cx([styles.btn, styles.chartBtn, btnStyles.btn, btnStyles.btnSmall, styles.btnSwitch],
                { [styles.disabled]: toggleDisabled || isListingToday })}
              onClick={debounceToggleExpand}
              disabled={toggleDisabled || isListingToday}
              aria-label="chart button"
            >
              <Icon name={toggleDetails ? ICON_NAME.CHART_LINE : ICON_NAME.CANDLE_CHART} size={8} />
            </button>
          </GAElement>
          )}
          <If test={hideMoreOptions}>
            <Popper
              iconName={ICON_NAME.STOCK_CONTROL_PIN}
              activeIconName={ICON_NAME.STOCK_CONTROL_PIN}
              showDropDownIcon={false}
              className={styles.customPopperStyle}
              iconSize={5}
              iconStyles={styles.iconStyles}
            >
              <div
                className={styles.container}
                onClick={(e) => e.stopPropagation()}
                role="presentation"
              >
                <PinScripOptions
                  securityId={securityId}
                  name={name}
                  id={id}
                  exchange={exchange}
                  instrumentType={instrument_type}
                  segment={segment}
                />
              </div>
            </Popper>
          </If>
          <If test={!hideMoreOptions}>
            {!isBond
          && (
          <div className={styles.dropdownWrapper}>
            <DropdownOptions
              options={showBasketOrder
                ? MORE_OPTIONS_WITH_BASKET_ORDERS.filter(filterOptions)
                : MORE_OPTIONS.filter(filterOptions)}
              pmlId={id}
              iconSize={7}
              instrumentType={instrument_type}
              popperClassName={styles.popper}
              menuClassName={styles.menuItem}
              exchange={exchange}
              segment={segment}
              lot_size={lot_size}
              isin={isin}
              security_id={securityId}
              tick_size={tick_size}
              displayName={name}
              handleSelectOption={handleSelectOption}
            />
          </div>
          )}
          </If>

        </div>
        )}
      </div>
      <div className={styles.subHeader}>
        {isBond ? (
          <div className={styles.seriesWrapper}>
            <div className={styles.isin}>
              ISIN:
              <span className={styles.subHeaderValues}>{`${isin}`}</span>
            </div>
            <div className={styles.series}>
              Series:
              <span className={styles.subHeaderValues}>{`${series}`}</span>
            </div>
          </div>
        ) : null}

      </div>
    </div>
  );
}

CompanyHeader.propTypes = {
  toggleExpand: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  exchange: PropTypes.string.isRequired,
  securityId: PropTypes.number.isRequired,
};

const LoadingComponent = () => <SkeletonLoader type={TYPE.WATCHLIST} className={styles.headerLoader} />;

const CompanyHeaderWithLoader = WithLoader({ WrappedComponent: CompanyHeader, LoadingComponent });

export default CompanyHeaderWithLoader;
