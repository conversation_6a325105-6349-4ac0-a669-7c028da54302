import { ICON_NAME } from '@common/Icon/enums';

export const ALL_TYPES = {
  SIP: 'SIP',
  MARGIN_PLEDGE: 'Margin Pledge',
  GTT: 'GTT',
  PRICE_ALERTS: 'Price Alerts',
  BASKETS: 'Baskets',
};

export const MORE_OPTIONS = [{
  id: 'createGtt',
  name: ALL_TYPES.GTT,
  iconName: ICON_NAME.GTT_DARK,
  iconSize: 3.5,
}, {
  id: 'marginPledge',
  name: ALL_TYPES.MARGIN_PLEDGE,
  iconName: ICON_NAME.MARGIN_PLEDGE_OPTION,
  iconSize: 3.5,
}, {
  id: 'createSip',
  name: ALL_TYPES.SIP,
  iconName: ICON_NAME.STOCK_SIPS,
  iconSize: 3.5,
}, {
  id: 'createPriceAlert',
  name: ALL_TYPES.PRICE_ALERTS,
  iconName: ICON_NAME.PRICE_ALERT,
  iconSize: 3.5,
}];

export const MORE_OPTIONS_WITH_BASKET_ORDERS = [{
  id: 'createBasket',
  name: ALL_TYPES.BASKETS,
  iconName: ICON_NAME.SMALL_BASKET,
  iconSize: 3.5,
}, {
  id: 'createGtt',
  name: ALL_TYPES.GTT,
  iconName: ICON_NAME.GTT_DARK,
  iconSize: 3.5,
}, {
  id: 'marginPledge',
  name: ALL_TYPES.MARGIN_PLEDGE,
  iconName: ICON_NAME.MARGIN_PLEDGE_OPTION,
  iconSize: 3.5,
}, {
  id: 'createSip',
  name: ALL_TYPES.SIP,
  iconName: ICON_NAME.STOCK_SIPS,
  iconSize: 3.5,
}, {
  id: 'createPriceAlert',
  name: ALL_TYPES.PRICE_ALERTS,
  iconName: ICON_NAME.PRICE_ALERT,
  iconSize: 3.5,
}];
