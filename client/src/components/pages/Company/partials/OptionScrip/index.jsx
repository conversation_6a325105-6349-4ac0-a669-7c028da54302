import React, { useState, useEffect } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { sendEvent } from '@service/AnalyticsService';
import { ROUTE_NAME } from '@utils/enum';
import { classNames as cx } from '@utils/style';
import Icon from '@common/Icon';
import { ICON_NAME } from '@common/Icon/enums';
import { button as btnStyles } from '@commonStyles';
import Routes from '@/routes';
import CONFIG from '@/config';
import styles from './index.scss';

const OptionScrip = ({ id, name }) => {
  const [optionScrip, setOptionScrip] = useState(null);
  const history = useHistory();

  useEffect(() => {
    const fnoScripData = async () => {
      try {
        const res = await fetch(CONFIG.FNO_SEARCH_V2);
        const { data } = await res.json();
        const isMatch = data.filter((scrip) => (scrip.id === id))[0];
        setOptionScrip(isMatch);
      } catch (error) { }
    };
    fnoScripData();
  }, [id]);
  const isFno = history.location.pathname.indexOf('fno') > -1;
  return (optionScrip && !isFno ? (
    <Link
      to={`${Routes[ROUTE_NAME.FNO_OPTION_CHAIN].url}/${optionScrip.id}/${optionScrip.symbol}/${optionScrip.exchange}`}
      onClick={() => {
        sendEvent({
          event_category: 'fno_option_chain',
          event_action: 'option_chain_widget_clicked',
          event_label: `${optionScrip.symbol} Company Page|${name}`,
          vertical_name: 'derivatives',
        });
      }}
    >
      <button className={cx([styles.btn, btnStyles.btn, btnStyles.secondaryBtnFill, styles.optionChainCta])}>
        <Icon name={ICON_NAME.OPTION_CHAIN} size={4} />
        Option Chain
      </button>
    </Link>
  ) : null
  );
};

export default OptionScrip;
