import React from 'react';
import { classNames as cx } from '@utils/style';
import { SEGMENT_TYPES } from '@utils/enum';
import If from '@common/If';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import { mobileBrowser } from 'utils';
import Charts from '../Charts';
import TradingViewCharts from '../TradingViewCharts';
import LightViewCharts from '../LightViewCharts';
import { ChartsProvider } from '../Charts/chartsContext';
import styles from './index.scss';

const ChartSection = ({
  corporateActions,
  ...props
}) => (
  <ChartsProvider
    {...props}
  >
    <Charts corporateActions={corporateActions} />
  </ChartsProvider>
);

const TradingViewChartSection = ({
  hideShare,
  corporateActions,
  ...props
}) => (
  <ChartsProvider
    {...props}
  >
    <TradingViewCharts hideShare={hideShare} corporateActions={corporateActions} />
  </ChartsProvider>
);

const LightViewChartSection = ({
  ...props
}) => (
  <ChartsProvider
    {...props}
  >
    <LightViewCharts />
  </ChartsProvider>
);

function Overview({
  id, toggleDetails, segment, hideShare, ...props
}) {
  const { selectedChart } = useLoggedInContext();
  return (
    <div
      className={cx([], {
        [styles.restrictedContainer]: !toggleDetails && (segment !== SEGMENT_TYPES.DERIVATIVES),
        [styles.container]: toggleDetails,
        [styles.derivatives]: !toggleDetails && (segment === SEGMENT_TYPES.DERIVATIVES),
      })}
    >
      <If test={(toggleDetails || segment === SEGMENT_TYPES.DERIVATIVES) && selectedChart === SELECT_CHART_MODES[1].id}>
        <TradingViewChartSection
          id={id}
          hideShare={hideShare}
          toggleDetails={(segment === SEGMENT_TYPES.DERIVATIVES && !mobileBrowser()) || toggleDetails}
          segment={segment}
          {...props}
        />
      </If>
      <If test={!(toggleDetails || segment === SEGMENT_TYPES.DERIVATIVES)
        && selectedChart === SELECT_CHART_MODES[1].id}
      >
        <LightViewChartSection
          id={id}
          toggleDetails={(segment === SEGMENT_TYPES.DERIVATIVES && !mobileBrowser()) || toggleDetails}
          segment={segment}
          {...props}
        />
      </If>
      <If test={selectedChart === SELECT_CHART_MODES[0].id}>
        <ChartSection
          id={id}
          toggleDetails={(segment === SEGMENT_TYPES.DERIVATIVES && !mobileBrowser()) || toggleDetails}
          segment={segment}
          {...props}
        />
      </If>
    </div>
  );
}

export default Overview;
