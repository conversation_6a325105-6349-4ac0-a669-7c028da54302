@import '../../../../../styles/main';

.container {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
}

.restrictedContainer {
  max-height: 40rem;
}

.derivatives {
  height: 44rem;

  @include respond(phone) {
    height: auto;
  }
}

.header {
  margin-bottom: 1rem;
}

.marketDepth {
  > :nth-child(2) {
    > :first-child {
      max-width: 45%;
    }

    > :last-child {
      max-width: 45%;
    }
  }

  > :nth-child(3) {
    margin-top: 1.5rem;
  }
}
