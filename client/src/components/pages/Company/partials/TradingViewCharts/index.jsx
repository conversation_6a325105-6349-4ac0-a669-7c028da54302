import React from 'react';

import { classNames as cx } from '@utils/style';
import If from '@common/If';
import { useCharts } from '@pages/Company/partials/Charts/chartsContext';
import { STATICS } from '@pages/Company/partials/Charts/ENUMS';
import RangeControls from '@pages/Company/partials/Charts/partials/RangeControls';
import FooterControls from '@pages/Company/partials/Charts/partials/FooterControls';
import ToolsBar from '@pages/Company/partials/Charts/partials/ToolsBar';
import SideBar from '@pages/Company/partials/Charts/partials/SideBar';
import ScripActions from '@pages/Company/partials/Charts/partials/ScripActions';
import { Percent } from '@common/Prices';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import { SELECT_CHART_MODES } from '@layout/LoggedInLayout/enums';
import SkeletonLoader from '@common/SkeletonLoader';
import { TYPE } from '@common/SkeletonLoader/enums';
import ErrorView from '@common/WithLoader/ErrorView';
import CONFIG from '@/config';
import styles from '../Charts/index.scss';

function TradingViewCharts({
  showScripActions = false,
  chartIndices = false,
  hideShadow = false,
  hideRange = false,
  corporateActions = false,
  containerClass,
  hideShare = false,
}) {
  const { selectedChart } = useLoggedInContext();
  const ctx = useCharts();
  const {
    toggleDetails,
    activeRange,
    isError,
    handleRange,
    chartsRef,
    iframeRef,
    fullScreen,
    rangeValue,
    inProgress,
  } = ctx;

  const renderControls = () => {
    if (toggleDetails) {
      return <ToolsBar hideShare={hideShare} />;
    }

    return (
      <div
        className={cx(styles.rangeControls, {
          [styles.scripActions]: !chartIndices,
          [styles.chartIndices]: chartIndices,
        })}
      >
        <If test={!chartIndices && showScripActions}>
          <ScripActions />
        </If>
        <If test={!chartIndices && !showScripActions}>
          <span className={styles.rangeContainer}>
            <span>{STATICS.Returns}</span>
            <span>
              <If test={rangeValue && !inProgress}>
                <Percent value={rangeValue} className={styles.returnsValue} withSign />
              </If>
            </span>
          </span>
        </If>
        <RangeControls setChartRange={handleRange} activeRange={activeRange} />
      </div>
    );
  };

  if (isError) {
    return <ErrorView />;
  }

  const iframeSrc = `${CONFIG.IFRAME_CHARTS}/equity-charts/trading-view/`;

  return (
    <div
      ref={chartsRef}
      className={cx(styles.container, {
        [containerClass]: containerClass,
        [styles.shadow]: !hideShadow && !toggleDetails,
        [styles.toggleContainer]: toggleDetails,
        [styles.fullScreenContainer]: fullScreen,
        [styles.corporateActions]: corporateActions,
      })}
    >
      <If test={!hideRange}>
        {renderControls()}
      </If>
      <div
        className={cx(styles.mainView, {
          [styles.mainViewFullScreen]: fullScreen,
          [styles.homeScreenChart]: chartIndices,
        })}
      >
        <div
          className={cx(styles.iframeContainer, {
            [styles.iframeContainerToggled]: toggleDetails,
          })}
        >
          <If test={inProgress}>
            <div className={styles.loader}>
              <SkeletonLoader
                type={TYPE.CHARTS}
                showScripActions={showScripActions}
              />
            </div>
          </If>
          <If test={fullScreen}>
            <ScripActions />
          </If>
          <iframe
            className={styles.iframe}
            ref={iframeRef}
            id="chart"
            src={iframeSrc}
            title="Charts"
          />
          <If test={toggleDetails && selectedChart === SELECT_CHART_MODES[0].id}>
            <FooterControls />
          </If>
        </div>
        <If test={fullScreen}>
          <SideBar />
        </If>
      </div>
    </div>
  );
}

export default TradingViewCharts;
