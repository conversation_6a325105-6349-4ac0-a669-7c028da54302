import React, {
  useCallback, useEffect, useState, useRef,
} from 'react';
import PropTypes from 'prop-types';
import { classNames as cx } from '@utils/style';
import { mergeRefs } from '@utils/react';
import Tooltip, { TOOLTIP_DIRECTION } from '@common/Tooltip';
import { mobileBrowser } from '@utils';
import { flushSync } from 'react-dom';
import styles from './index.scss';
import { MODAL_TYPES } from './enums';
import useMovablePopupBehaviour from './useMovablePopupBehaviour';
import Icon from '../Icon';
import { ICON_NAME } from '../Icon/enums';

function Modal({
  handleClose, Component, open = false, componentProps, type,
  draggable = false, disableClose = false, closeIcon = ICON_NAME.CLOSE_POPUP,
  onOpenAnimationEnd = () => null, onCloseAnimationEnd = () => null,
  modalContainerStyles, noAnimationOnOut = false, closebackDropEnable = true, closeBtnStyle,
  isBackDropClickEnable = true,
}) {
  const modalDraggable = draggable && type !== MODAL_TYPES.SIDEPANE && !mobileBrowser();
  const modalContainerRef = useRef();
  const [className, setClassName] = useState('');
  const setModalTypes = useCallback(() => {
    if (mobileBrowser() && !type) {
      return MODAL_TYPES.BOTTOM_SHEET;
    } if (!type) {
      return MODAL_TYPES.SIDEPANE;
    }
    return type;
  }, [type]);
  const [modalType, setModalType] = useState(setModalTypes());
  const {
    dragEnabled,
    toggleDragEnabled,
    containerCbRef,
    dragAreaCbRef,
  } = useMovablePopupBehaviour({
    open,
    draggable: modalDraggable,
  });
  useEffect(() => {
    setModalType(setModalTypes());
  }, [type, Component, setModalTypes]);

  const onKeyDownListener = useCallback(
    (e) => (e.keyCode === 27) && handleClose(),
    [handleClose],
  );

  useEffect(() => {
    if (open) {
      window.document.addEventListener('keydown', onKeyDownListener);
    }

    return () => {
      if (open) {
        window.document.removeEventListener('keydown', onKeyDownListener);
      }
    };
  }, [dragEnabled, onKeyDownListener, open]);

  useEffect(() => {
    if (open) {
      flushSync(() => {
        setClassName(`${styles.visible} ${styles.in}`);
      });
    } else {
      flushSync(() => {
        setClassName(`${styles.visible} ${noAnimationOnOut ? styles.noAnimationOnOut : styles.out}`);
      });
    }
  }, [noAnimationOnOut, open]);

  const onAnimationEnd = useCallback(() => {
    if (open) {
      onOpenAnimationEnd();
      return flushSync(() => {
        setClassName(styles.visible);
      });
    }
    onCloseAnimationEnd();
    return flushSync(() => {
      setClassName('');
    });
  }, [onCloseAnimationEnd, onOpenAnimationEnd, open]);

  const getModalContainerClass = () => {
    switch (modalType) {
      case MODAL_TYPES.POPUP: return styles.popup;
      case MODAL_TYPES.LARGE_POPUP: return styles.largePopup;
      case MODAL_TYPES.SIDEPANE: return styles.sidepane;
      case MODAL_TYPES.BOTTOM: return styles.bottom;
      case MODAL_TYPES.BOTTOM_SHEET: return styles.bottomSheet;
      case MODAL_TYPES.MobileAPPDOWNLOAD_POPUP: return styles.MobileAppDownloadPopup;
      default: return styles.sidepane;
    }
  };

  const onModalClose = () => {
    handleClose();
    if (componentProps.onModalClose) {
      componentProps.onModalClose(componentProps);
    }
  };

  const handleCloseModalOnbackDrop = () => {
    if (closebackDropEnable) {
      return onModalClose();
    }
    return () => {};
  };

  return (
    <div
      className={cx(className, {
        [styles.dragEnabled]: dragEnabled,
        [styles.draggable]: draggable,
      })}
      role="presentation"
      onClick={(e) => {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
      }}
    >
      <div
        className={styles.backdrop}
        onAnimationEnd={onAnimationEnd}
        role="presentation"
        onClick={() => (dragEnabled || !isBackDropClickEnable ? null : handleCloseModalOnbackDrop())}
        data-modal-backdrop="true"
      />
      <div
        className={cx([styles.modalContainer, getModalContainerClass()], {
          [modalContainerStyles]: modalContainerStyles,
        })}
        ref={mergeRefs([modalContainerRef, containerCbRef])}
      >
        {modalDraggable && (
          <div role="presentation" onClick={toggleDragEnabled} className={styles.drag}>
            <Tooltip
              message={dragEnabled ? 'Fixed pop-up' : 'Moveable pop-up'}
              tooltipDirection={TOOLTIP_DIRECTION.BOTTOM}
            >
              <Icon name={dragEnabled ? ICON_NAME.DRAG_DISABLE : ICON_NAME.DRAG_ENABLE} size={2.4} />
            </Tooltip>
          </div>
        )}
        {!disableClose && (
          <div role="presentation" onClick={onModalClose} className={cx(styles.close, { [closeBtnStyle]: closeBtnStyle })}>
            <Icon name={closeIcon} size={2.4} />
          </div>
        )}
        {
          Component && componentProps && (
            <Component
              {...(componentProps || {})}
              modalType={modalType}
              modalDraggable={modalDraggable}
              dragAreaRef={dragAreaCbRef}
              handleClose={handleClose}
            />
          )
        }
      </div>
    </div>
  );
}

Modal.propTypes = {
  Component: PropTypes.oneOfType([PropTypes.element, PropTypes.node, PropTypes.func]),
  open: PropTypes.bool.isRequired,
  componentProps: PropTypes.oneOfType([PropTypes.object]),
  type: PropTypes.oneOf(Object.values(MODAL_TYPES)),
  switchable: PropTypes.bool,
};

Modal.defaultProps = {
  componentProps: {},
  switchable: false,
  Component: null,
};

export default Modal;
