import React, {
  useCallback, createContext, useState, useContext, useMemo,
} from 'react';
import PlaceOrder from '@modules/OrderFlow/PlaceOrder';
import useNonIrModal from '@common/useNonIrModal';
import { isEmpty } from 'lodash';
import { mobileBrowser } from 'utils';
import MTFOnboardingSidepane from '@modules/MTF/MTFOnboardingSidepane';
import { SEGMENT_TYPES, TRANSACTION_TYPES } from '../../utils/enum';
import { MODAL_TYPES } from './Modal/enums';
import Modal from './Modal';

export const DraggableModalContext = createContext({ openDraggableModal: () => { } });

export const DraggableModalContextProvider = ({ children }) => {
  const [modalProps, updateModalProps] = useState({});
  const openDraggableModal = useCallback((props) => {
    updateModalProps({ ...props, open: true, draggable: true });
  }, []);

  const handleClose = useCallback(() => {
    updateModalProps((props) => ({ ...props, open: false }));
  }, []);

  const MemoizedComponent = useMemo(() => ({ ...props }) => (
    <modalProps.Component {...modalProps.componentProps} {...props} />
  ), [modalProps.componentProps]);

  return (
    // eslint-disable-next-line react/jsx-no-constructed-context-values
    <DraggableModalContext.Provider value={{ openDraggableModal }}>
      {children}
      {!isEmpty(modalProps)
        ? (
          <Modal {...modalProps} Component={MemoizedComponent} handleClose={handleClose} />
        ) : null}
    </DraggableModalContext.Provider>
  );
};

function usePlaceOrder(company, exchange, securityId, {
  isin, tickSize, lotSize, productType, segment = SEGMENT_TYPES.CASH,
  siblingSecurityId, siblingTickSize, forceExchangeSelect, showProfitLoss, instrumentType,
  eventAction, eventCategory, eventLabel, instrument_Id, isWatchlist, orderCount = 0,
  isModifyBasketOrder = false, order_id, placeOrderInBasket = null, exch_symbol,
}) {
  const { openDraggableModal } = useContext(DraggableModalContext);
  const { handleIrAction } = useNonIrModal();

  const showActivateMtf = useCallback((componentProps) => {
    openDraggableModal({
      type: MODAL_TYPES.POPUP,
      Component: PlaceOrder,
      componentProps: {
        ...componentProps,
        setShowMtfOnboarding: () => {
          openDraggableModal({
            type: MODAL_TYPES.SIDEPANE,
            Component: MTFOnboardingSidepane,
            componentProps: {
              source: 'order',
              ...componentProps,
            },
          });
        },
        isMtfModalClosed: true,
        onModalClose: () => {},
      },
    });
  }, [openDraggableModal]);

  const showMtfOnboardingSidepane = useCallback((componentProps) => {
    openDraggableModal({
      type: MODAL_TYPES.SIDEPANE,
      Component: MTFOnboardingSidepane,
      componentProps: {
        ...componentProps,
        source: 'order',
        showActivateMtf,
        onModalClose: showActivateMtf,
      },
    });
  }, [openDraggableModal, showActivateMtf]);

  const buy = useCallback(({
    initialQuantity, orderType, initialPrice, initialTriggerPrice, initialTargetValue, initialStoplossValue, channel,
  } = {}) => {
    const buyAction = () => {
      openDraggableModal({
        type: mobileBrowser() ? MODAL_TYPES.BOTTOM_SHEET : MODAL_TYPES.POPUP,
        Component: PlaceOrder,
        componentProps: {
          name: company,
          exchange,
          securityId,
          segment,
          transactionType: TRANSACTION_TYPES.BUY,
          initialQuantity,
          orderType,
          initialPrice,
          initialTriggerPrice,
          initialTargetValue,
          initialStoplossValue,
          channel,
          isin,
          tickSize,
          lotSize,
          siblingSecurityId,
          siblingTickSize,
          productType,
          forceExchangeSelect,
          showProfitLoss,
          instrumentType,
          eventAction,
          eventCategory,
          eventLabel,
          instrument_Id,
          isWatchlist,
          orderCount,
          isModifyBasketOrder,
          order_id,
          placeOrderInBasket,
          exch_symbol,
          isMtfModalClosed: undefined,
          setShowMtfOnboarding: showMtfOnboardingSidepane,
        },
      });
    };
    handleIrAction(buyAction, segment)();
  }, [handleIrAction, segment, openDraggableModal, company, exchange, securityId, isin, tickSize,
    lotSize, siblingSecurityId, siblingTickSize, productType, forceExchangeSelect, showProfitLoss,
    instrumentType, eventAction, eventCategory, eventLabel, instrument_Id, isWatchlist, orderCount,
    isModifyBasketOrder, order_id, placeOrderInBasket, exch_symbol, showMtfOnboardingSidepane]);

  const sell = useCallback(({
    initialQuantity, orderType, initialPrice, initialTriggerPrice, initialTargetValue, initialStoplossValue, channel,
  } = {}) => {
    const sellAction = () => {
      openDraggableModal({
        type: mobileBrowser() ? MODAL_TYPES.BOTTOM_SHEET : MODAL_TYPES.POPUP,
        Component: PlaceOrder,
        componentProps: {
          name: company,
          exchange,
          securityId,
          segment,
          transactionType: TRANSACTION_TYPES.SELL,
          initialQuantity,
          initialPrice,
          initialTriggerPrice,
          initialTargetValue,
          channel,
          initialStoplossValue,
          orderType,
          isin,
          tickSize,
          lotSize,
          siblingSecurityId,
          siblingTickSize,
          productType,
          forceExchangeSelect,
          showProfitLoss,
          instrumentType,
          eventAction,
          eventCategory,
          eventLabel,
          instrument_Id,
          isWatchlist,
          orderCount,
          isModifyBasketOrder,
          order_id,
          placeOrderInBasket,
          exch_symbol,
          isMtfModalClosed: undefined,
          setShowMtfOnboarding: showMtfOnboardingSidepane,
        },
      });
    };
    handleIrAction(sellAction, segment)();
  }, [handleIrAction, segment, openDraggableModal, company, exchange, securityId, isin, tickSize,
    lotSize, siblingSecurityId, siblingTickSize, productType, forceExchangeSelect, showProfitLoss,
    instrumentType, eventAction, eventCategory, eventLabel, instrument_Id, isWatchlist, orderCount,
    isModifyBasketOrder, order_id, placeOrderInBasket, exch_symbol, showMtfOnboardingSidepane]);

  return { buy, sell };
}

export default usePlaceOrder;
