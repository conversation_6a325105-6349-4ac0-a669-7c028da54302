@import '../../../styles/main';

.tooltip {
  position: relative;
}

.tooltipTrigger {
  display: inline-block;
}

.tooltipBubble {
  left: 50%;
  min-width: 5rem;
  position: absolute;
  transform: translateX(-50%);
  z-index: 10;

  @include box-shadow(0, -2px, 10px);

  &::after {
    border-bottom: 6px solid $grey2;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    content: '';
    left: 50%;
    position: absolute;
  }
}

.topTooltipBubble {
  bottom: 100%;
  margin-bottom: 1rem;

  &::after {
    bottom: -.5rem;
    transform: translateX(-50%) rotate(180deg);
  }
}

.bottomTooltipBubble {
  top: 100%;
  margin-top: .6rem;

  &::after {
    top: -.5rem;
    transform: translateX(-50%);
  }
}

.tooltipMessage {
  background: $grey2;
  border-radius: 3px;
  color: $default;
  padding: .75rem;
  text-align: center;

  @include typography(h8);
}

.customStyles .tooltipBubble {
  width: 30rem;
}

.customStyles .tooltipMessage {
  text-align: left;
}
