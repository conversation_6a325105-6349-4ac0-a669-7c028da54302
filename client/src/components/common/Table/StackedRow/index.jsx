import React from 'react';
import { classNames as cx } from '@utils/style';
import styles from './styles.scss';

function StackedRow({
  slotTopStart, slotTopEnd, slotBottomStart, slotBottomEnd,
}) {
  return (
    <>
      <div className={styles.column}>
        {slotTopStart}
        <div>{slotBottomStart}</div>
      </div>
      <div className={cx([styles.column, styles.end])}>
        <div>{slotTopEnd}</div>
        <div>{slotBottomEnd}</div>
      </div>
    </>
  );
}

export default StackedRow;
