import React from 'react';
import Icon from '@common/Icon';
import If from '@common/If';
import { ICON_NAME } from '@common/Icon/enums';
import { classNames as cx } from '@utils/style';
import { mobileBrowser } from '@utils';
import { button } from '@commonStyles';
import styles from './index.scss';

const Search = ({
  placeholder, searchQuery = '', handleOnSearch = () => {},
  handleSubmit = () => {}, clearText, disableBtn, showSearchButton = true,
}) => (
  <div className={styles.searchContainer}>
    <label htmlFor="searchbar">
      <If test={!mobileBrowser()}><Icon name={ICON_NAME.SEARCH} size={2} className={styles.searchIcon} /></If>
      <input
        placeholder={placeholder}
        value={searchQuery}
        onChange={handleOnSearch}
        id="searchbar"
        autoComplete="off"
        onKeyDown={(e) => {
          e.stopPropagation();
          if (e.keyCode === 13) handleSubmit();
        }}
      />
      {
        searchQuery
          ? <Icon name={ICON_NAME.CANCEL} size={1.6} className={styles.clearBtn} onIconClick={clearText} />
          : null
      }
    </label>
    <If test={showSearchButton}>
      <button
        type="button"
        className={cx([button.btn, styles.btnView], {
          [button.disabledBtn]: disableBtn,
        })}
        disabled={disableBtn}
        onClick={handleSubmit}
        label="Search"
      >
        Search
      </button>
    </If>
  </div>
);

export default Search;
