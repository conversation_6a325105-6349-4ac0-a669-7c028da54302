import React, {
  useEffect, useRef, useState, useCallback, useContext,
} from 'react';
import Icon from '@common/Icon';
import { UserContext } from '@layout/App/UserContext';
import If from '@common/If';
import { ICON_NAME } from '@common/Icon/enums';
import { classNames as cx } from '@utils/style';
import isEqual from 'lodash/isEqual';
import cloneDeep from 'lodash/cloneDeep';
import Checkbox from '@common/Checkbox';
import { PRODUCT_TYPES } from '@utils/enum';
import { button } from '@commonStyles';
import { mobileBrowser } from '@utils';
import styles from './index.scss';

const totalAppliedFilters = (list) => Object.values(list).reduce((acc, item) => [...acc, ...item], []).length;

function CategoryFilter({
  category,
  filterList,
  handleSelectedFilters = () => {},
  listedItemTitle = null,
  listedItems = [],
  resetFilter,
  showDowntimeMessage = false,
  setShowDowntimeMessage = () => {},
  initialFilters,
  isFilterOpen = true,
  setIsFilterOpen = () => {},
}) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(filterList);
  const filterRef = useRef(null);
  const {
    mtfFeatureFlag,
  } = useContext(UserContext);

  const handleSubmit = useCallback(() => {
    if (!isEqual(activeTab, filterList)) {
      handleSelectedFilters(cloneDeep(activeTab));
    }
    setIsDropdownOpen(false);
    setIsFilterOpen(false);
  }, [activeTab, filterList, handleSelectedFilters, setIsFilterOpen]);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (filterRef.current && !filterRef.current.contains(e.target)) {
        handleSubmit();
      }
    };
    if (isDropdownOpen) document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeTab, filterList, handleSelectedFilters, handleSubmit, isDropdownOpen]);

  const openDropDown = (e) => {
    e.stopPropagation();
    setActiveTab(filterList);
    if (isDropdownOpen) {
      setIsDropdownOpen(false);
      setIsFilterOpen(false);
    } else {
      if (!isFilterOpen) setIsFilterOpen(true);
      setIsDropdownOpen(true);
      setIsFilterOpen(true);
    }
  };

  const handleFilter = (filterName, tagId, disabled = false) => {
    if (disabled) return;
    const temp = cloneDeep(activeTab);
    const isActive = temp[filterName].indexOf(tagId);
    if (isActive > -1) {
      temp[filterName].splice(isActive, 1);
    } else {
      temp[filterName].push(tagId);
    }
    setActiveTab(temp);
  };

  const clearFilter = (e) => {
    e.stopPropagation();
    resetFilter();
    setActiveTab(initialFilters);
    setIsDropdownOpen(false);
    setIsFilterOpen(false);
  };

  const iconName = isDropdownOpen ? ICON_NAME.DROP_DOWN : ICON_NAME.DROP_UP;
  const filterLen = totalAppliedFilters(filterList);

  useEffect(() => {
    if (!mtfFeatureFlag) {
      category.filter((item) => item.id !== PRODUCT_TYPES.MTF);
    }
  }, [category, mtfFeatureFlag]);

  return (
    <div
      className={styles.filter}
      ref={filterRef}
    >
      <div
        className={styles.text}
        role="presentation"
        onClick={openDropDown}
      >
        <Icon
          name={filterLen ? ICON_NAME.FILTER_ACTIVE : ICON_NAME.FILTER}
          size={5.2}
        />
        <div className={cx(styles.heading, {
          [styles.active]: filterLen,
        })}
        >
          Filter
        </div>
        {filterLen ? <span className={styles.totalFilters}>{filterLen}</span> : null}
        <Icon name={iconName} size={1.8} className={cx(null, { [styles.arrow]: filterLen })} />
        {filterLen ? <div className={styles.resetFilter} role="presentation" onClick={clearFilter}>Clear all</div> : null}
      </div>
      <If test={isDropdownOpen && mobileBrowser()}>
        <div role="presentation" onClick={handleSubmit} className={styles.backdrop} />
      </If>
      {(isDropdownOpen && isFilterOpen) ? (
        <div className={styles.filterContainer}>
          <div className={styles.filterhead}>
            <div className={styles.title}>
              Filter
            </div>
            {totalAppliedFilters(activeTab) ? <div className={styles.clearFilter} role="presentation" onClick={clearFilter}>Clear all</div> : null}
          </div>
          {
            category.map((filterData, index) => (
              <div className={styles.filterParameter} key={index}>
                <div className={styles.title}>{filterData.name}</div>
                <div>
                  {filterData.data.map((tag, indexNo) => (
                    <>
                      <If test={(mtfFeatureFlag && tag.id === PRODUCT_TYPES.MTF) || tag.id !== PRODUCT_TYPES.MTF}>
                        <div
                          key={indexNo}
                          id={tag.id}
                          className={cx(styles.tag, {
                            [styles.activeTab]: activeTab[filterData.labelId.toString()].indexOf(tag.id) > -1,
                            [styles.disabledTab]: (tag.downtimeMessage && filterData.disabled) || filterData.disabled,
                          })}
                          onClick={handleFilter.bind(null, filterData.labelId.toString(), tag.id, filterData.disabled
                          || (tag.downtimeMessage && filterData.disabled))}
                          role="presentation"
                        >
                          {tag.value}
                        </div>

                      </If>
                      <If test={showDowntimeMessage && tag.downtimeMessage}>
                        <div className={styles.downtimeMessage}>
                          <Icon name={ICON_NAME.DOWNTIME} size={4} className={styles.icon} />
                          {tag.downtimeMessage}
                          <span role="presentation" onClick={() => setShowDowntimeMessage(false)}>
                            <Icon name={ICON_NAME.CLOSE} size={1.5} className={styles.close} />
                          </span>
                        </div>
                      </If>
                    </>
                  ))}
                </div>
              </div>
            ))
          }
          <If test={listedItems.length}>
            <div className={styles.checkedItems}>
              <div className={styles.title}>{listedItemTitle}</div>
              {listedItems.map((item) => (
                <div className={styles.checklist}>
                  <Checkbox
                    checked={activeTab[item.labelId.toString()].indexOf(item.id) > -1}
                    onChange={handleFilter.bind(null, item.labelId.toString(), item.id, listedItems.length === 1)}
                  />
                  <span className={styles.name}>{item.name}</span>
                </div>
              ))}
            </div>
          </If>
          <If test={mobileBrowser()}>
            <div className={styles.resultsButton}>
              <button
                onClick={handleSubmit}
                className={cx(button.btnLarge, {
                  [button.disabledBtn]: !Object.values(activeTab).flat(1).length,
                  [button.secondaryBtnFill]: Object.values(activeTab).flat(1).length,
                })}
              >
                Show Results
              </button>
            </div>
          </If>
        </div>
      ) : null}
    </div>
  );
}

export default CategoryFilter;
