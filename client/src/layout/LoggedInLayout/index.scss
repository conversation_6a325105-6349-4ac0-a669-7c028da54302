@import '../../styles/main';

.container {
  background-color: $bg-color;
  display: flex;
  flex-direction: column;
}

/* stylelint-disable declaration-no-important */
.tradingIdea {
  padding: 0 !important;
  height: auto !important;
  overflow: inherit !important;
}
/* stylelint-enable declaration-no-important */

.content {
  flex: 1 1 auto;
  overflow: auto;
  padding: 2rem 4rem;
  height: calc(100vh - 5rem);

  @include respond(phone) {
    padding: 0 0 3.2rem;
    min-height: 100vh;
    min-height: -webkit-fill-available;
    min-height: -moz-available;
  }
}

.profileContent {
  flex: 1 1 auto;
  overflow: auto;
  padding: 2rem 4rem;
  height: 100vh;

  @include respond(phone) {
    padding: 0 0 3.2rem;
    min-height: 100vh;
    min-height: -webkit-fill-available; // scss-lint:disable VendorPrefix
    min-height: -moz-available;
  }
}

.extraPadding {
  height: calc(100vh - 10rem);
}

.containerHeight {
  @include respond(tab-land) {
    padding-bottom: 5rem;
  }

  @include respond(phone) {
    padding-bottom: 10rem;
  }
}

.tooltip {
  width: 10rem;
}
