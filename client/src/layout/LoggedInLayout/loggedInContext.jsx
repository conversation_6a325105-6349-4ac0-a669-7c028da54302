/* eslint-disable react/jsx-no-constructed-context-values */
import React, {
  createContext, useState, useContext, useEffect, useCallback, useRef, useMemo,
} from 'react';
import moment from 'moment';
import { getActiveTheme, setSelectedTheme } from '@utils/style';
import LocalStorage from '@service/LocalStorage';
import {
  THEMES, EXCHANGE, SEGMENT_TYPES, LOCATION_DETAILS,
} from '@utils/enum';
import { isEmpty } from 'lodash';
import { mapChartConstants } from '@pages/Company/partials/Charts/utils';
import { getIndianDate, isWebView } from '@utils';
import CONFIG from '@/config';
import { UserContext } from '../App/UserContext';
import { getTimers, getCaData } from './api';
import { LOCAL_STORAGE_KEYS, SELECT_CHART_MODES } from './enums';

const localStorageKey = 'header-pinned-scrip';
let timeAtLastCall;

const LoggedInContext = createContext({});

function LoggedInProvider({ children, ...props }) {
  const [activeTheme, setActiveTheme] = useState(getActiveTheme() || THEMES.LIGHT);
  const [equityCardInfo, setEqutyCardInfo] = useState({});
  const [chartConstants, setChartConstants] = useState({ loading: true, failed: false, data: null });
  const [miscConfig, setMiscConfig] = useState({ loading: true, failed: false, data: null });
  const [timersConfig, setTimersConfig] = useState();
  const endOfDayTimer = useRef(null);
  const [pinnedScrip, setPinnedScrip] = useState([]);
  const [headerIndices, setHeaderIndices] = useState([]);
  const [preOpenInfoMessage, setPreOpenInfoMessage] = useState(null);
  const [marketcapMapping, setMarketcapMapping] = useState([]);
  const [bankTransferContent, setBankTransferContent] = useState([]);
  const [corporateActionsData, setCorporateActionsData] = useState([]);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedChart, setSelectedChart] = useState(LocalStorage.get(LOCAL_STORAGE_KEYS.CHART_SELECTED,
    false) || SELECT_CHART_MODES[1].id);
  const locationDetails = useMemo(
    () => LocalStorage.get(LOCATION_DETAILS, true),
    [],
  );
  const {
    isSikkimUser, locationDisabled,
  } = useContext(UserContext);
  const [getLocationDetails, setGetLocationDetails] = useState(false);

  const setSelectedChartType = (chartId) => {
    LocalStorage.set(LOCAL_STORAGE_KEYS.CHART_SELECTED, chartId, '', false);
    setSelectedChart(chartId);
  };

  const setTheme = (theme) => {
    setSelectedTheme(theme);
    setActiveTheme(theme);
  };

  const fetchEquityCardInfoData = useCallback(async () => {
    try {
      const res = await fetch(`https://static.paytmmoney.com/data/v1/${CONFIG.ENV}/equity-infocard.json`);
      const { data } = await res.json();
      setEqutyCardInfo(data);
    } catch (error) { }
  }, []);

  const fetchMappingData = useCallback(async () => {
    try {
      const res = await fetch('https://static.paytmmoney.com/data/v1/marketcap-mapping.json');
      const { data } = await res.json();
      setMarketcapMapping(data);
    } catch (error) { }
  }, []);

  const fetchChartConstants = useCallback(async () => {
    try {
      setChartConstants((prev) => ({ ...prev, loading: true }));
      const constants = await (await fetch('https://static.paytmmoney.com/data/v1/equity_chart_constants.json')).json();
      setChartConstants((prev) => ({
        ...prev,
        loading: false,
        failed: false,
        data: mapChartConstants(constants),
      }));
    } catch (err) {
      setChartConstants((prev) => ({ ...prev, failed: true, loading: false }));
    }
  }, []);

  const getCorporateActionData = useCallback(async () => {
    try {
      const date = moment().format('YYYY-MM-DD');
      const { data: { results } } = await getCaData({ startDate: date, endDate: date });
      setCorporateActionsData(results);
    } catch (error) { }
  }, []);

  const fetchHeaderIndices = useCallback(async () => {
    const savedPinnedScrip = LocalStorage.get(localStorageKey, false);
    if (savedPinnedScrip) {
      setPinnedScrip(savedPinnedScrip);
    }
    try {
      const res = await fetch('https://static.paytmmoney.com/data/v1/indices/header-indices.json');
      const { data } = await res.json();
      setHeaderIndices(data);
      if (!savedPinnedScrip) {
        setPinnedScrip(data.slice(0, 2));
      }
    } catch (error) { }
  }, []);

  const fetchTimersConfig = useCallback(async () => {
    try {
      const { data } = await getTimers();
      timeAtLastCall = new Date().getTime();
      setTimersConfig(data);
    } catch (error) { }
  }, []);

  const fetchMiscConfig = useCallback(async () => {
    try {
      setMiscConfig((prev) => ({ ...prev, loading: true }));
      const data = await (await fetch('https://static.paytmmoney.com/data/v1/eq-web-misc-config.json')).json();
      setMiscConfig((prev) => ({
        ...prev,
        loading: false,
        failed: false,
        data,
      }));
    } catch (err) {
      setMiscConfig((prev) => ({ ...prev, failed: true, loading: false }));
    }
  }, []);

  const fetchPreOpenInfoMessage = useCallback(async () => {
    try {
      const res = await fetch(`https://static.paytmmoney.com/data/v1/${CONFIG.SIGNALS_CONFIG.environment}/IPO-preopen-config.json`);
      const { data } = await res.json();
      setPreOpenInfoMessage(data);
    } catch (error) {
    }
  }, []);

  const fetchBankTransferContentConfig = useCallback(async () => {
    try {
      setMiscConfig((prev) => ({ ...prev, loading: true }));
      const data = await (await fetch(' https://static.paytmmoney.com/data/v1/bank-transfer-content.json')).json();
      setBankTransferContent((prev) => ({
        ...prev,
        loading: false,
        failed: false,
        data,
      }));
    } catch (err) {
      setBankTransferContent((prev) => ({ ...prev, failed: true, loading: false }));
    }
  }, []);

  useEffect(() => {
    fetchEquityCardInfoData();
    fetchChartConstants();
    fetchTimersConfig();
    getCorporateActionData();
    fetchHeaderIndices();
    fetchMiscConfig();
    fetchMappingData();
    fetchPreOpenInfoMessage();
    fetchBankTransferContentConfig();
    // clear webview 2fa data
    if (isWebView()) {
      LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.REFRESH_2FA_TOKEN);
    }
  }, [fetchChartConstants, fetchTimersConfig, getCorporateActionData, fetchEquityCardInfoData,
    fetchHeaderIndices, fetchMiscConfig, fetchMappingData, fetchPreOpenInfoMessage, fetchBankTransferContentConfig]);

  useEffect(() => {
    setGetLocationDetails(isSikkimUser
        && (!locationDetails || !Object.keys(locationDetails).length) && locationDisabled);
  }, [isSikkimUser, locationDisabled, locationDetails]);

  const reAddTimers = useCallback(() => {
    if (!document.hidden && timeAtLastCall) {
      const timeElapsed = new Date().getTime() - timeAtLastCall;
      timeAtLastCall = new Date().getTime();
      setTimersConfig((config) => {
        if (config) {
          const mkStatusConfig = config.mkt_status_timer_config.find(({
            exchange, segment,
          }) => exchange === EXCHANGE.NSE && segment === SEGMENT_TYPES.CASH);
          if (mkStatusConfig && mkStatusConfig.cur_date_time) {
            mkStatusConfig.cur_date_time += timeElapsed;
          }
          const squareOffConfig = config.square_off_timer_config.find(({
            exchange, segment,
          }) => exchange === EXCHANGE.NSE && segment === SEGMENT_TYPES.CASH);
          if (squareOffConfig && squareOffConfig.cur_date_time) {
            squareOffConfig.cur_date_time += timeElapsed;
          }
          const newConfig = { ...config };
          if (mkStatusConfig) newConfig.mkt_status_timer_config = [mkStatusConfig];
          if (squareOffConfig) newConfig.square_off_timer_config = [squareOffConfig];
          return newConfig;
        }
        return config;
      });
    }
  }, []);

  useEffect(() => {
    document.addEventListener('visibilitychange', reAddTimers);
    return () => {
      document.removeEventListener('visibilitychange', reAddTimers);
    };
  }, [reAddTimers]);

  const { openConfig, closeConfig } = useMemo(() => {
    if (timersConfig) {
      const config = timersConfig.mkt_status_timer_config.find(({
        exchange, segment,
      }) => exchange === EXCHANGE.NSE && segment === SEGMENT_TYPES.CASH);
      if (isEmpty(config)) {
        return { openConfig: {}, closeConfig: {} };
      }
      return {
        openConfig: {
          ...config,
          timerTime: config.mkt_open_time,
          countdownTime: config.open_count_down_time,
        },
        closeConfig: {
          ...config,
          timerTime: config.mkt_close_time,
          countdownTime: config.close_count_down_time,
        },
      };
    }
    return { openConfig: {}, closeConfig: {} };
  }, [timersConfig]);

  const squareOff = useMemo(() => {
    if (timersConfig) {
      const config = timersConfig.square_off_timer_config.find(({
        exchange, segment,
      }) => exchange === EXCHANGE.NSE && segment === SEGMENT_TYPES.CASH);
      if (isEmpty(config)) {
        return {};
      }
      return {
        ...config,
        timerTime: config.square_off_time,
        countdownTime: config.square_off_count_down_time,
      };
    }
    return {};
  }, [timersConfig]);

  useEffect(() => {
    if (timersConfig) {
      const squareOffConfig = timersConfig.square_off_timer_config.find(({
        exchange, segment,
      }) => exchange === EXCHANGE.NSE && segment === SEGMENT_TYPES.CASH);
      if (squareOffConfig && squareOffConfig.cur_date_time) {
        const indianDate = getIndianDate(squareOffConfig.cur_date_time);
        const endOfDay = new Date(`${indianDate}T23:59:59.999+05:30`);
        endOfDayTimer.current = setTimeout(() => {
          fetchTimersConfig();
        }, endOfDay.getTime() - squareOffConfig.cur_date_time);
      }
    }
    return () => endOfDayTimer.current && clearTimeout(endOfDayTimer.current);
  }, [fetchTimersConfig, timersConfig]);

  return (
    <LoggedInContext.Provider value={{
      theme: activeTheme,
      setTheme,
      equityCardInfo,
      chartConstants,
      timersConfig,
      corporateActionsData,
      openConfig,
      closeConfig,
      squareOff,
      squareOffMsg: squareOff.square_off_timer_msg,
      openMsg: openConfig.open_msg,
      closeMsg: closeConfig.close_msg,
      pinnedScrip,
      setPinnedScrip,
      headerIndices,
      setHeaderIndices,
      marketcapMapping,
      setMarketcapMapping,
      miscConfig,
      bankTransferContent,
      preOpenInfoMessage,
      showSearch,
      setShowSearch,
      currDateTime: squareOff.cur_date_time,
      selectedChart,
      setSelectedChartType,
      getLocationDetails,
      ...props,
    }}
    >
      {children}
    </LoggedInContext.Provider>
  );
}

const useLoggedInContext = () => useContext(LoggedInContext);

export {
  LoggedInProvider,
  useLoggedInContext,
  LoggedInContext,
};
