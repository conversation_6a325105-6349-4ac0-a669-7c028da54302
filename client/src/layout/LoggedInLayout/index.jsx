import React, {
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  useHistory,
  useLocation,
} from 'react-router-dom';
import Routes from '@layout/Routes';
import Header from '@modules/Header';
import {
  classNames as cx, getActiveTheme, setDataTheme, getDataTheme,
} from '@utils/style';
import { UserContext } from '@layout/App/UserContext';
import {
  ROUTE_NAME, THEMES, REGENERATE_TOTP, LOCATION_DETAILS,
} from '@utils/enum';
import LocalStorage from '@service/LocalStorage';
import { LoadingComponent } from '@pages/Pricing/index';
import { LoggedInProvider } from '@layout/LoggedInLayout/loggedInContext';
import { DataFeedContextProvider } from '@layout/App/DataFeedContext';
import MarketStatus from '@modules/MarketStatus';
import { OrderFeedContextProvider } from '@layout/App/OrderFeedContext';
import { HoldingsContextProvider } from '@modules/Holdings';
import { FundsSummaryContextProvider } from '@layout/App/FundsSummaryContext';
import MobileDownload from '@layout/App/MobileDownload';
import useIrStatus from '@common/useIrStatus';
import If from '@common/If';
import Footer, { LINKS as FOOTER_LINKS } from '@modules/Footer/Mobile';
import { ModalFlow } from '@layout/App/ModalContext';
import { EDIS_AUTHORISATION_KEY } from '@pages/Home/partials/EdisCollection/enum';
import UpgradeFlow from '@pages/Home/partials/UpgradeFlow';
import { useIrData } from 'layout/App/UserContext';
import Nudge from '@common/Nudge/Nudge';
import Tooltip from '@common/Tooltip';
import { mobileBrowser, isWebView } from '@utils';
import RouteNames from '@/routes';
import { getComponents } from '../utils';
import ComponentsList from './routes';
import styles from './index.scss';
import { getSleekCard } from './SleekCard/api';
import SleekCard from './SleekCard';
import UserConsentLayout from './UserConsent/UserConsentLayout';
import BlockUserFlow from './BlockUserFlow';
import FnoBlocker from './FnoBlocker';

const Components = getComponents(ComponentsList);
const isMobile = mobileBrowser();

const MobileFooter = ({ showFooter }) => (
  <If test={showFooter}>
    <Footer />
  </If>
);
function LoggedInLayout() {
  const history = useHistory();
  const {
    ssoToken: isLoggedIn, login, isSikkimUser, setNudgeDispatch, twoFaToken, isMerchant, mssoToken,
    mtwoFaToken, mtfFeatureFlag,
  } = useContext(UserContext);
  const { userId } = useIrData();
  const locationDetails = LocalStorage.get(LOCATION_DETAILS, true);
  const wrapperRef = useRef();
  const [cardConfig, setCardConfig] = useState();
  const [showMessage, setShowMessage] = useState(true);
  const location = useLocation();
  const isMarginPledgeMobile = useMemo(() => isMobile && location.pathname
    .includes(RouteNames[ROUTE_NAME.MARGIN_PLEDGE].url),
  [location.pathname]);
  const isIPO = useMemo(() => (
    location.pathname.startsWith(RouteNames[ROUTE_NAME.IPO].url)
  ), [location]);
  const isSGB = useMemo(() => (
    location.pathname.startsWith(RouteNames[ROUTE_NAME.SGB].url)
  ), [location]);
  const isAlgoTradingMobile = useMemo(() => isWebView() && location.search
    .includes('type=algo'), [location.search]);
  const isEdisAuthorisation = location.pathname.includes(EDIS_AUTHORISATION_KEY);

  setDataTheme((isMarginPledgeMobile && getDataTheme()) || getActiveTheme() || THEMES.LIGHT);
  const { inProgress } = useIrStatus();

  useEffect(() => {
    if (!isLoggedIn && !isMerchant) {
      login();
    }
  }, [isLoggedIn, login, isMerchant]);

  useEffect(() => {
    if ((!twoFaToken && isLoggedIn) || (mssoToken && !mtwoFaToken)) {
      let cond;
      if (LocalStorage.get(REGENERATE_TOTP)) {
        cond = ([RouteNames[ROUTE_NAME.PASSCODE].url,
          RouteNames[ROUTE_NAME.MANAGE_TOTP].url].indexOf(location.pathname) === -1);
      } else {
        cond = ([RouteNames[ROUTE_NAME.PASSCODE].url,
          RouteNames[ROUTE_NAME.MANAGE_PASSCODE].url].indexOf(location.pathname) === -1);
      }
      if (cond && !isIPO && !isSGB) {
        if (!isMarginPledgeMobile && !isAlgoTradingMobile) {
          LocalStorage.deleteItem(REGENERATE_TOTP);
          if (mssoToken) {
            history.replace(
              `${RouteNames[ROUTE_NAME.PASSCODE].url}${location.search}`,
            );
          } else {
            history.replace(
              `${RouteNames[ROUTE_NAME.PASSCODE].url}?returnUrl=${encodeURIComponent(location.pathname + location.search)}`,
            );
          }
        }
      }
    }
  }, [isLoggedIn, isIPO, history, location, isMarginPledgeMobile,
    isSGB, isAlgoTradingMobile, twoFaToken, mssoToken, mtwoFaToken]);

  useEffect(() => {
    if (wrapperRef.current) wrapperRef.current.scrollTop = 0;
  }, [location.pathname]);

  useEffect(() => {
    if (isLoggedIn || mssoToken) {
      getSleekCard().then(({ data: { sleekCard } = {} }) => {
        if (sleekCard && sleekCard.message) {
          let { cta_txt } = sleekCard;
          if (cta_txt.length > 15) {
            cta_txt = (
              <Tooltip message={sleekCard.cta_txt} className={styles.tooltip}>
                {`${cta_txt.slice(0, 15)}...`}
              </Tooltip>
            );
          }
          setCardConfig({ ...sleekCard, cta_txt });
        }
      }).catch();
    }
  }, [isLoggedIn, mssoToken]);

  if (!isLoggedIn && !mssoToken) return null;

  const isMarginPledge = location.pathname.indexOf(RouteNames[ROUTE_NAME.MARGIN_PLEDGE].url) > -1;
  const isManageTOTP = location.pathname.indexOf(RouteNames[ROUTE_NAME.MANAGE_TOTP].url) > -1;
  const isASubLink = (subPaths) => subPaths?.find((e) => location.pathname.includes(e));

  const showFooter = isMobile && !isMarginPledge && !isEdisAuthorisation && !isIPO
    && FOOTER_LINKS.find(({ path, subPaths }) => location.pathname.includes(path) || isASubLink(subPaths))
    && !isManageTOTP;
  if (showFooter
    && !FOOTER_LINKS.find(({ path, subPaths }) => location.pathname.includes(path) || isASubLink(subPaths))) {
    return inProgress ? <LoadingComponent /> : <MobileDownload />;
  }

  const hideHeader = mobileBrowser()
    && (!location.pathname.includes(RouteNames[ROUTE_NAME.HOME].url) || isMarginPledge);

  const isOrdersPage = location.pathname.indexOf(RouteNames[ROUTE_NAME.ORDERS].url) > -1;
  const isTradingIdea = location.pathname.includes(RouteNames[ROUTE_NAME.TRADING_IDEAS].url);
  const isRouteEdisRevoke = location.pathname.includes(RouteNames[ROUTE_NAME.EDIS_REVOKE_AUTH].url);

  const layout = (
    <LoggedInProvider>
      <div className={styles.container}>
        <div className={styles.profileContent} ref={wrapperRef}>
          <Routes config={Components} />
        </div>
      </div>
    </LoggedInProvider>
  );

  if (!twoFaToken
  && !isIPO && !isMarginPledgeMobile && !isSGB && !isAlgoTradingMobile) return layout;

  return (
    <LoggedInProvider isSleekCardActive={cardConfig && showMessage} loggedInlayoutRef={wrapperRef}>
      <Nudge
        appName="equity-web-app-client"
        userId={userId}
        history={history}
        platform="web"
        setNudgeDispatch={setNudgeDispatch}
      />
      <DataFeedContextProvider>
        <OrderFeedContextProvider>
          <HoldingsContextProvider>
            <FundsSummaryContextProvider>
              <MarketStatus>
                <ModalFlow>
                  <If test={!isWebView()}>
                    <UserConsentLayout />
                    <BlockUserFlow />
                    <UpgradeFlow />
                    <FnoBlocker />
                  </If>
                  <div className={styles.container}>
                    <If test={!hideHeader && !isRouteEdisRevoke
                       && ((isSikkimUser && locationDetails && Object.keys(locationDetails).length)
                       || !isSikkimUser)}
                    >
                      <Header isIPO={isIPO} isSGB={isSGB} />
                    </If>
                    <If test={cardConfig && showMessage && !isMobile && !isIPO && !isSGB}>
                      <SleekCard cardConfig={cardConfig} setShowMessage={setShowMessage} />
                    </If>
                    <div
                      className={cx(styles.content, {
                        [styles.extraPadding]: (cardConfig && showMessage && !isMobile && !isTradingIdea),
                        [styles.containerHeight]: isOrdersPage,
                        [styles.profileContent]: (isMobile),
                        [styles.tradingIdea]: isTradingIdea,
                        [styles.mtf]: mtfFeatureFlag,
                      })}
                      ref={wrapperRef}
                    >
                      <Routes config={Components} />
                    </div>
                    <MobileFooter showFooter={showFooter} />
                  </div>
                </ModalFlow>
              </MarketStatus>
            </FundsSummaryContextProvider>
          </HoldingsContextProvider>
        </OrderFeedContextProvider>
      </DataFeedContextProvider>
    </LoggedInProvider>
  );
}

export default LoggedInLayout;
