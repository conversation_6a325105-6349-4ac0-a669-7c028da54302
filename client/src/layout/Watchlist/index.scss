@import '../../styles/main';

$tab-active-color: #223368;
$header-shadow-color: rgba(1, 42, 114, .1);

.container {
  display: flex;
}

.portfolioContainer {
  flex: 1;

  @include respond(phone) {
    padding: 1rem;
  }
}

.watchList {
  min-width: 27%;
  max-width: 27%;
}

@media only screen and (min-width: 1366px) {
  .watchList {
    min-width: 36rem;
    max-width: 36rem;
  }
}

.routerContainer {
  display: flex;
  margin-left: 2.1rem;
  flex: 1;

  @include respond(phone) {
    margin-left: 0;
  }
}

.content {
  flex: 1 1 0;
}

.headerContainer {
  background-color: $default;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 2rem 1rem;
  padding-bottom: 0;
  box-shadow: 0 .2rem .8rem 0 $header-shadow-color;
  z-index: 10;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-bottom: 1rem;

  .price {
    display: flex;
    gap: .65rem;
  }
}

.name {
  color: $grey0;

  @include typography(h5, semibold);
}

.tab {
  width: 100%;
  text-align: center;

  @include typography(h6, semibold);

  @include respond(phone) {
    /* stylelint-disable-next-line declaration-no-important */
    font-size: 1.4rem !important;
  }
}

.contentContainer {
  padding-top: 9.4rem;
}

.activeTab {
  /* stylelint-disable-next-line declaration-no-important */
  color: $tab-active-color !important;
  border-bottom: solid 2px $primary-color;
}
