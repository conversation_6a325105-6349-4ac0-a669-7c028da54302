import React from 'react';
import { useLoggedInContext } from '@layout/LoggedInLayout/loggedInContext';
import WatchlistSidebar from '@modules/WatchlistSidebar';
import Routes from '@layout/Routes';
import { UserEventContextProvider } from '@layout/App/UserEventContext';
import { WatchlistContextProvider } from '@modules/WatchlistSidebar/watchlistContext';
import { ModalFlow } from '@layout/App/ModalContext';
import { DraggableModalContextProvider } from '@common/usePlaceOrder';
import If from '@common/If';
import Tablist from '@common/Tabs/Tablist';
import { useHistory, useLocation } from 'react-router-dom';
import { EDIS_AUTHORISATION_KEY } from '@pages/Home/partials/EdisCollection/enum';
import { mobileBrowser, isWebView } from '@utils';
import { getComponents } from '../utils';
import styles from './index.scss';
import ComponentsList from './routes';
import { tabConfig } from './config';

const Components = getComponents(ComponentsList);

function WatchlistLayout() {
  const history = useHistory();
  const location = useLocation();
  const { getLocationDetails } = useLoggedInContext();
  const tab = tabConfig.find(({ link }) => location.pathname.includes(link))?.id;
  const showTab = mobileBrowser() && tabConfig.find(({ link }) => location.pathname.includes(link));

  const isEdisAuthCollection = location.pathname.includes(EDIS_AUTHORISATION_KEY);

  const setTab = (tabId) => {
    if (tabId) {
      const { link } = tabConfig.find(({ id }) => tabId === id);
      history.replace(link);
    }
  };

  if (getLocationDetails && !isWebView()) {
    history.push('/capture-location');
    return null;
  }

  return (
    <UserEventContextProvider>
      <WatchlistContextProvider>
        <ModalFlow>
          <DraggableModalContextProvider>
            <div className={styles.container}>
              <If test={!mobileBrowser()}>
                <div className={styles.watchList}>
                  <WatchlistSidebar />
                </div>
              </If>
              <If test={showTab}>
                <div className={styles.portfolioContainer}>
                  <If test={!isEdisAuthCollection}>
                    <div className={styles.headerContainer}>
                      <div className={styles.header}>
                        <div className={styles.name}>Portfolio</div>
                      </div>
                      <Tablist
                        options={tabConfig}
                        className={styles.tab}
                        activeClassName={styles.activeTab}
                        activeTab={tab}
                        setTab={setTab}
                      />
                    </div>
                  </If>
                  <div className={styles.contentContainer}>
                    <Routes config={Components} />
                  </div>
                </div>
              </If>
              <If test={!showTab}>
                <div className={styles.routerContainer}>
                  <Routes config={Components} />
                </div>
              </If>
            </div>
          </DraggableModalContextProvider>
        </ModalFlow>
      </WatchlistContextProvider>
    </UserEventContextProvider>
  );
}

export default WatchlistLayout;
