const marketLayoutRoutes = require('../Market/routes');
const { routes: orderRoutes } = require('../Orders/routes');
const indexRoutes = require('../../components/pages/Index/routes');
const portfolioRoutes = require('../Portfolio/routes');
const pnlRoutes = require('../Pnl/routes');
const { ROUTE_NAME } = require('../../utils/enum');
const fnoLayoutRoutes = require('../Fno/routes');

const routes = [
  {
    id: ROUTE_NAME.HOME,
    link: '/dashboard',
    component: () => import(/* webpackChunkName: "home" */ '../../components/pages/Home'),
    exact: false,
  },
  {
    id: ROUTE_NAME.MARKET,
    link: '/market',
    component: () => import(/* webpackChunkName: "market" */ '../Market'),
    exact: false,
    childRoutes: marketLayoutRoutes,
  },
  {
    id: ROUTE_NAME.PNL,
    link: '/p&l',
    component: () => import(/* webpackChunkName: "p&l" */ '../Pnl'),
    exact: false,
    childRoutes: pnlRoutes,
  },
  {
    id: ROUTE_NAME.PORTFOLIO_LAYOUT,
    link: '/portfolio',
    exact: false,
    component: () => import(/* webpackChunkName: "portfolio-layout" */ '../Portfolio'),
    childRoutes: portfolioRoutes,
  },
  {
    id: ROUTE_NAME.POSITIONS,
    link: '/positions',
    component: () => import(/* webpackChunkName: "positions" */ '../../components/pages/Positions'),
  },
  {
    id: ROUTE_NAME.ORDERS,
    link: '/orders',
    component: () => import(/* webpackChunkName: "orders" */ '../Orders'),
    exact: false,
    childRoutes: orderRoutes,
  },
  {
    id: ROUTE_NAME.WATCHLIST_DASHBOARD,
    link: '/watchlist',
    params: '/:watchlistId?',
    exact: false,
    component: () => import(/* webpackChunkName: "manage-watchlist" */ '../../components/pages/Watchlist'),
  },
  {
    id: ROUTE_NAME.COMPANY_MORE,
    link: '/company/:id/more/:tab?',
    exact: false,
    component: () => import(/* webpackChunkName: "company-more" */ '../../components/pages/Company/partials/More'),
  },
  {
    id: ROUTE_NAME.DEB_MORE,
    link: '/deb/:id/more/:tab?',
    exact: false,
    component: () => import(/* webpackChunkName: "company-more" */ '../../components/pages/Company/partials/More'),
  },
  {
    id: ROUTE_NAME.DBT_MORE,
    link: '/dbt/:id/more/:tab?',
    exact: false,
    component: () => import(/* webpackChunkName: "company-more" */ '../../components/pages/Company/partials/More'),
  },
  {
    id: ROUTE_NAME.GB_MORE,
    link: '/gb/:id/more/:tab?',
    exact: false,
    component: () => import(/* webpackChunkName: "company-more" */ '../../components/pages/Company/partials/More'),
  },
  {
    id: ROUTE_NAME.CB_MORE,
    link: '/cb/:id/more/:tab?',
    exact: false,
    component: () => import(/* webpackChunkName: "company-more" */ '../../components/pages/Company/partials/More'),
  },
  {
    id: ROUTE_NAME.COMPANY_PAGE,
    link: '/company',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
    exact: false,
  },
  {
    id: ROUTE_NAME.INDEX_PAGE,
    link: '/index',
    params: '/:id',
    component: () => import(/* webpackChunkName: "index" */ '../../components/pages/Index'),
    exact: false,
    childRoutes: indexRoutes,
  },
  {
    id: ROUTE_NAME.ETF,
    link: '/etf',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
    exact: false,
  },
  {
    id: ROUTE_NAME.REIT,
    link: '/reit',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
    exact: false,
  },
  {
    id: ROUTE_NAME.INVITU,
    link: '/invitu',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
    exact: false,
  },
  {
    id: ROUTE_NAME.DEB,
    link: '/deb',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
  },
  {
    id: ROUTE_NAME.DBT,
    link: '/dbt',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
  },
  {
    id: ROUTE_NAME.GB,
    link: '/gb',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
  },
  {
    id: ROUTE_NAME.CB,
    link: '/cb',
    params: '/:id',
    component: () => import(/* webpackChunkName: "company" */ '../../components/pages/Company'),
  },
  {
    id: ROUTE_NAME.FUTIDX,
    link: '/futidx',
    params: '/:id',
    component: () => import(/* webpackChunkName: "derivative" */ '../../components/pages/Derivative'),
    exact: false,
  },
  {
    id: ROUTE_NAME.FUTSTK,
    link: '/futstk',
    params: '/:id',
    component: () => import(/* webpackChunkName: "derivative" */ '../../components/pages/Derivative'),
    exact: false,
  },
  {
    id: ROUTE_NAME.OPTIDX,
    link: '/optidx',
    params: '/:id',
    component: () => import(/* webpackChunkName: "derivative" */ '../../components/pages/Derivative'),
    exact: false,
  },
  {
    id: ROUTE_NAME.OPTSTK,
    link: '/optstk',
    params: '/:id',
    component: () => import(/* webpackChunkName: "derivative" */ '../../components/pages/Derivative'),
    exact: false,
  },
  {
    id: ROUTE_NAME.FUNDS,
    link: '/funds',
    params: '/:transactionId?',
    component: () => import(/* webpackChunkName: "manage-funds" */ '../../components/pages/ManageFunds'),
    exact: false,
  },
  {
    id: ROUTE_NAME.FNO,
    link: '/fno',
    component: () => import(/* webpackChunkName: "fno" */ '../Fno'),
    childRoutes: fnoLayoutRoutes,
    exact: false,
  },
];

module.exports = routes;
