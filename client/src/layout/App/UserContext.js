import React, {
  createContext, useMemo, useCallback, useEffect, useState, useContext, useRef, useReducer,
} from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import LocalStorage from '@service/LocalStorage';
import useWebConfig from '@common/useWebConfig';
import moment from 'moment';
import {
  COOKIES,
  USERNAME,
  THEMES,
  ROUTE_NAME,
  IR_STATUS,
  FORGOT_PASSCODE,
  REGENERATE_TOTP,
  LOCATION_DETAILS,
  IS_BASKET_ACCESSIBLE,
  MTF_FEATURE_FLAG,
} from '@utils/enum';
import { ICON_SVG } from '@common/Icon/enums';
import { updateCustomerId } from '@service/AnalyticsService';
import { getNewSubscriptionsDetails } from '@pages/Home/partials/UpgradeFlow/api';
import { getIRStatus } from '@layout/LoggedInLayout/api';
import { ALGO_REDIRECT_TO_FUNDS } from '@pages/ManageFunds/partials/ManageFundsModal/config';
import { getBasketOrderStatus } from '@pages/StockInstructions/BasketOrders/basket-order-api';
import { AUTOPAY_LOCAL_STORAGE_KEYS } from '@pages/AutoPay/enum';
import { getMtfList } from '@modules/MTF/api';
import { LOCAL_STORAGE_KEYS } from '@layout/LoggedInLayout/enums';
import useOrderRestrictionsConfig from '@common/useOrderRestrictionsConfig';
import { handle401ForPublisher } from '@service/ApiService';
import {
  getCookieValue, deleteCookie, isBrowser, getUrlParameter,
} from '@utils';
import AppReducer, { ACTIONS, appInitialState } from './AppReducer';
import { EXPIRED_SCREEN_SHOWED, TRIAL_STATUS } from '../LoggedInLayout/UserConsent/enum';
import {
  getUserData,
  getKycDetails,
  getProfileImage,
  getInvestmentReadinessData,
  getBackOfficeInfo,
  logoutUser,
  identifySikkimUsers,
  getMtfFeatureFlag,
} from './api';
import { getIrStatus, isInvestmentReady } from './utils';
import Routes from '../../routes';
import CONFIG from '../../config';
// eslint-disable-next-line import/no-relative-packages
import { EQUITY_BASE_URL } from '../../../../config/ENUMS';
import { PRODUCT_TYPE } from './enums';

const UserContext = createContext({});
const DEFAULT_USER_ICON = ICON_SVG[THEMES.DARK].EMPTY_PROFILE_PIC;
const ONE_YEAR = 31536000;
const NUMBER_OR_RETRIES = 3;

const UserContextProvider = ({ children }) => {
  const location = useLocation();
  const history = useHistory();
  const accessToken = isBrowser() && getUrlParameter('accessToken', window.location.search);
  const requestToken = isBrowser() && getUrlParameter('requestToken', window.location.search);
  const apiKey = isBrowser() && getUrlParameter('apiKey', window.location.search);
  const merchantFlow = accessToken || requestToken || apiKey;
  const isMerchant = merchantFlow || (isBrowser() && getUrlParameter('requestToken', getUrlParameter('returnUrl', window.location.search))) || (isBrowser() && getUrlParameter('apiKey', getUrlParameter('returnUrl', window.location.search)));
  const ssoToken = useMemo(() => (isBrowser() && location ? getCookieValue(COOKIES.SSO_TOKEN) : null), [location]);
  const twoFaToken = useMemo(() => (isBrowser() && location ? getCookieValue(COOKIES.TWOFA_TOKEN) : null), [location]);
  const mssoToken = useMemo(() => (isBrowser() && location
  && isMerchant ? getCookieValue(COOKIES.M_SSO_TOKEN) : null), [isMerchant, location]);
  const mtwoFaToken = useMemo(() => (isBrowser() && location
  && isMerchant ? getCookieValue(COOKIES.M_TWOFA_TOKEN) : null), [isMerchant, location]);
  const userAgent = useMemo(() => (isBrowser() && location ? getCookieValue(COOKIES.USER_AGENT) : null), [location]);
  const mUserAgent = useMemo(() => (isBrowser() && location ? getCookieValue(COOKIES.M_USER_AGENT) : null), [location]);
  const userId = userAgent || (isMerchant && mUserAgent) ? JSON.parse((isMerchant && mUserAgent)
    ? mUserAgent : userAgent).user_id : null;
  const platform = userAgent ? JSON.parse(userAgent).platform : null;
  const [isSikkimUser, setIsSikkimUser] = useState(false);
  const [isSikkimUserIdentified, setIsSikkimUserIdentified] = useState(false);
  const [maxRetryCount, setMaxRetryCount] = useState(NUMBER_OR_RETRIES);
  const [isLocationPermissionProvided, setIsLocationPermissionProvided] = useState(false);
  const [locationDetails, setLocationDetails] = useState({});
  const [locationDisabled, setLocationDisabled] = useState(locationDetails && !Object.keys(locationDetails).length);
  const logoutRef = useRef(false);
  const pmlEnv = isBrowser() && getUrlParameter('pmlEnv', window.location.search);
  const [showBasketOrder, setShowBasketOrder] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState();
  const { webConfig, fetchInProgress } = useWebConfig();
  const [isH5PollingExhausted, setIsH5PollingExhausted] = useState(true);
  const { getExchangeConfig, priceTickSizeBlockingFlag, priceDprBlockingFlag } = useOrderRestrictionsConfig();
  const [riskDisclosureAcceptance, setRiskDisclosureAcceptance] = useState(
    (isBrowser() && LocalStorage.get(LOCAL_STORAGE_KEYS.RISK_DISCLOSURE_ACCEPTANCE)) || false,
  );
  const initialState = appInitialState;

  const [Context, dispatch] = useReducer(AppReducer, initialState);

  const setNudgeDispatch = useCallback((value) => {
    dispatch({
      type: ACTIONS.SET_NUDGE_DISPATCH,
      value,
    });
  }, []);
  const [blockerData, setBlockerData] = useState({});
  const [combinedIrData, setCombinedIrData] = useState();
  const [mtfFeatureFlag, setMtfFeatureFlag] = useState(false);
  const [mtfScrips, setMtfScrips] = useState();
  const [nonBlockerError, setNonBlockerError] = useState(null);
  const login = useCallback(({
    returnUrl, merchantApiKey, takeToCombinedDashboard, state,
  } = {}) => {
    if (getCookieValue(COOKIES.SSO_TOKEN)) history.push(Routes[ROUTE_NAME.HOME].url);
    if (takeToCombinedDashboard) {
      window.location.href = `${CONFIG.AUTH}?returnUrl=${encodeURIComponent(`${window.location.origin}/dashboard`)}`;
    } else if (returnUrl || merchantApiKey) {
      if (merchantApiKey) {
        if (state) {
          window.location.href = `${CONFIG.AUTH}/merchant-login?apiKey=${merchantApiKey}&state=${state}`;
        } else {
          window.location.href = `${CONFIG.AUTH}/merchant-login?apiKey=${merchantApiKey}`;
        }
      } else {
        window.location.href = `${CONFIG.AUTH}?returnUrl=${encodeURIComponent(window.location.origin + EQUITY_BASE_URL
          + returnUrl)}`;
      }
    } else if (pmlEnv) {
      if (pmlEnv === 'publisherApi') {
        handle401ForPublisher();
      } else {
        window.location.href = `${CONFIG.AUTH}?pmlEnv=${pmlEnv}`;
      }
    } else window.location.href = `${CONFIG.AUTH}?returnUrl=${encodeURIComponent(window.location.href)}`;
  }, [history, pmlEnv]);

  const logout = useCallback(({ returnUrl, merchantApiKey, state } = {}) => {
    (getCookieValue(COOKIES.SSO_TOKEN) ? logoutUser() : Promise.resolve()).finally(() => {
      deleteCookie(COOKIES.SSO_TOKEN);
      deleteCookie(COOKIES.USER_AGENT);
      deleteCookie(COOKIES.TWOFA_TOKEN);
      LocalStorage.deleteItem(IS_BASKET_ACCESSIBLE);
      LocalStorage.deleteItem(LOCATION_DETAILS);
      LocalStorage.deleteItem(FORGOT_PASSCODE);
      LocalStorage.deleteItem(REGENERATE_TOTP);
      LocalStorage.deleteItem(COOKIES.DEVICE_ID);
      LocalStorage.deleteItem(LOCAL_STORAGE_KEYS.RISK_DISCLOSURE_ACCEPTANCE);
      login({ returnUrl, merchantApiKey, state });
    });
  }, [login]);

  const showBasketOrderForUser = useCallback(() => {
    getBasketOrderStatus().then((res) => {
      const basket_order_items = res.page.filter((val) => val.id === CONFIG.BASKET_ORDER_ID);
      if (basket_order_items[0].views[0]?.items[0]?.isAccessible === 'true') {
        LocalStorage.set(IS_BASKET_ACCESSIBLE, true, (moment().endOf('day').valueOf() - new Date().getTime()) / 1000);
        setShowBasketOrder(true);
      } else {
        LocalStorage.deleteItem(IS_BASKET_ACCESSIBLE);
        setShowBasketOrder(false);
      }
    })
      .catch(() => {
        if (LocalStorage.get(IS_BASKET_ACCESSIBLE)) {
          setShowBasketOrder(true);
        }
      });
  }, []);

  const getMTFData = useCallback(() => {
    let count = 0;
    async function fetchMtfScrips() {
      try {
        getMtfList().then((res) => {
          setMtfScrips(res.data);
        });
      } catch (err) {
        count += 1;
        if (count < 4) {
          fetchMtfScrips();
        }
      }
    }
    fetchMtfScrips();
  }, []);
  const fetchCombinedIrData = useCallback(async () => {
    try {
      const { data } = await getIRStatus(userId);
      setCombinedIrData(data);
    } catch (error) { }
  }, [userId]);

  const fetchMtfFeatureFlag = useCallback(async () => {
    try {
      const response = await getMtfFeatureFlag();
      setMtfFeatureFlag(response.data.equityMtf.isMTFFeatureEnabled);
      LocalStorage.set(MTF_FEATURE_FLAG, response.data.equityMtf.isMTFFeatureEnabled);
    } catch (err) {
      setMtfFeatureFlag(LocalStorage.get(MTF_FEATURE_FLAG));
      console.log(err);
    }
  }, []);

  const fetchBlockerConfig = useCallback(async () => {
    try {
      const blockUserData = await (await fetch('https://static.paytmmoney.com/mini-app/data/web-popup.json')).json();
      setBlockerData(blockUserData);
    } catch (err) { }
  }, []);

  // To handle previous cookies set by MF
  if (userAgent && !userId) {
    if (ssoToken) logout();
    else deleteCookie(COOKIES.USER_AGENT);
    if (!userAgent && ssoToken) logout();
  }
  const [userDetails, setUserDetails] = useState({});

  useEffect(() => {
    if (userId) {
      fetchBlockerConfig();
      showBasketOrderForUser();
      fetchCombinedIrData();
      fetchMtfFeatureFlag();
      getMTFData();
    }
  }, [showBasketOrderForUser, fetchCombinedIrData, userId, ssoToken, fetchBlockerConfig,
    fetchMtfFeatureFlag, getMTFData]);

  useEffect(() => {
    if (userId && ssoToken) updateCustomerId(userId, ssoToken);
  }, [userId, ssoToken]);

  useEffect(() => {
    let cookieListener;
    if (ssoToken || mssoToken) {
      cookieListener = window.setInterval(() => {
        const token = getCookieValue(COOKIES.SSO_TOKEN);
        const mToken = getCookieValue(COOKIES.M_SSO_TOKEN);
        if (!token && !mToken && !logoutRef.current) {
          logoutRef.current = true;
          logout();
        }
      }, 5000);
    }
    return () => { if (cookieListener) window.clearInterval(cookieListener); };
  }, [logout, ssoToken, mssoToken, history, location.pathname, location.search]);

  useEffect(() => {
    if (userId) {
      setUserDetails((prevState) => ({
        ...prevState,
        profileId: userId,
      }));
      getUserData(userId, isMerchant)
        .then(({ data: userInfo }) => {
          LocalStorage.set(USERNAME, userInfo.mobileNumber, ONE_YEAR);
          setUserDetails((prevState) => ({
            ...prevState,
            displayName: userInfo.displayName,
          }));
          if (userInfo.displayPicLocation && userInfo.displayPicLocation.indexOf('http') === 0) {
            getProfileImage(userInfo.displayPicLocation)
              .then((data) => {
                const displayImage = URL.createObjectURL(data);
                setUserDetails((prevState) => ({
                  ...prevState,
                  displayImage,
                }));
              });
          }
        });
    }
  }, [isMerchant, logout, userId]);

  const [kycDataFetched, setKycDataFetched] = useState(false);
  const getKycData = useCallback(() => {
    if (!kycDataFetched) {
      setKycDataFetched(true);
    }
  }, [kycDataFetched]);

  useEffect(() => {
    if ((userId && webConfig.restrictedLocations) && (maxRetryCount > 0 && !isSikkimUserIdentified)) {
      identifySikkimUsers(userId)
        .then(({ data: fields }) => {
          setIsSikkimUserIdentified(true);
          if (fields.fields.STATE.fieldValue
             && webConfig.restrictedLocations.includes(fields.fields.STATE.fieldValue.toLowerCase())) {
            setIsSikkimUser(true);
          }
        }).catch((err) => {
          if (err === undefined) {
            window.location.assign('/dashboard');
          }
          setIsSikkimUserIdentified(false);
          setMaxRetryCount(maxRetryCount - 1);
        });
    }
  }, [isSikkimUserIdentified, maxRetryCount, webConfig.restrictedLocations, userId]);

  useEffect(() => {
    if (kycDataFetched && userId) {
      getKycDetails(userId)
        .then(({ data: KycData }) => {
          if (KycData) {
            setUserDetails((prevState) => ({
              ...prevState,
              panNumber: KycData.panNumber,
              dob: KycData.personal.dob,
              gender: KycData.personal.gender,
              email: KycData.personal.email,
              mobile: KycData.personal.mobile,
              name: KycData.personal.name,
              kin: KycData.personal.kin,
            }));
          }
        });
    }
  }, [kycDataFetched, userId]);

  const [irDataFetched, setirDataFetched] = useState(false);
  const getIrData = useCallback(() => {
    if (!irDataFetched) {
      setirDataFetched(true);
    }
  }, [irDataFetched]);

  const fetchInvestmentReadinessData = useCallback(() => {
    getInvestmentReadinessData(userId, isMerchant)
      .then(({ data: irData }) => {
        const cashSubProduct = irData.bucketWiseSubproductIR.find((el) => el.subProduct === 'CASH') || {};
        const { isPaymentPending } = irData;
        const {
          irStatus, readinessDate, errorCode, buckets,
        } = cashSubProduct;
        const { irStatus: irStatusFO, readinessDate: readinessDateFO } = irData.bucketWiseSubproductIR.find((el) => el.subProduct === 'FO') || {};
        const updatedIrStatus = getIrStatus(cashSubProduct);
        const date = new Date(readinessDate);
        const dateFO = new Date(readinessDateFO);
        date.setHours(0, 0, 0, 0);
        setUserDetails((prevState) => ({
          ...prevState,
          irStatus: updatedIrStatus,
          irStatusFO,
          readinessDate: date.getTime(),
          readinessDateFO: dateFO.getTime(),
          isInvestmentReady: isInvestmentReady(irStatus),
          isInvestmentReadyFO: isInvestmentReady(irStatusFO),
          errorCode,
          isCrossOnboarding: irData.isCrossOnboarding,
          isPaymentPending,
          buckets,
        }));
        window.pmIrStatus = updatedIrStatus.toLowerCase();
      });
  }, [userId, isMerchant]);

  useEffect(() => {
    if (irDataFetched && userId) {
      fetchInvestmentReadinessData();
    }
  }, [irDataFetched, userId, fetchInvestmentReadinessData]);

  const getSubscriptionsDetailsFunction = useCallback(() => {
    getNewSubscriptionsDetails(userId, PRODUCT_TYPE).then(({ data }) => {
      if (data) {
        setSubscriptionDetails(data[0]);
      }
    });
  }, [userId]);

  useEffect(() => {
    if (userId) {
      getSubscriptionsDetailsFunction();
    }
  }, [getSubscriptionsDetailsFunction, isMerchant, ssoToken, userId]);

  useEffect(() => {
    if (userId) {
      const {
        isInvestmentReady: isIR, isCrossOnboarding,
      } = userDetails;
      const { trialStatus, paymentType, planStatus } = subscriptionDetails || {};
      const isExpiredScreenShowed = LocalStorage.get(EXPIRED_SCREEN_SHOWED);

      const inBlockUpgradeFlow = location.pathname.includes(Routes[ROUTE_NAME.UPGRADE].url)
      || location.pathname.includes(Routes[ROUTE_NAME.BLOCK].url);
      const redirectToAutoPay = LocalStorage.get(AUTOPAY_LOCAL_STORAGE_KEYS.REDIRECT_AUTOPAY);
      if (redirectToAutoPay?.redirect) {
        if (location.pathname.includes(Routes[ROUTE_NAME.FUNDS].url)) {
          history.replace(`${Routes[ROUTE_NAME.AUTOPAY].url}`);
          LocalStorage.set(AUTOPAY_LOCAL_STORAGE_KEYS.REDIRECT_AUTOPAY, { ...redirectToAutoPay, redirect: false });
        } else {
          LocalStorage.deleteItem(AUTOPAY_LOCAL_STORAGE_KEYS.REDIRECT_AUTOPAY);
        }
      }
      const redirectToFunds = LocalStorage.get(ALGO_REDIRECT_TO_FUNDS);
      if (redirectToFunds) {
        LocalStorage.deleteItem(ALGO_REDIRECT_TO_FUNDS);
        history.replace(`${Routes[ROUTE_NAME.FUNDS].url}`);
      } else if (!inBlockUpgradeFlow) {
        const isSubscriptionRequired = (trialStatus === TRIAL_STATUS.EXPIRED
        && !isExpiredScreenShowed && paymentType != null && planStatus === 'LIVE');
        if (isIR === true && isSubscriptionRequired && getCookieValue(COOKIES.TWOFA_TOKEN)) {
          history.replace(
            `${Routes[ROUTE_NAME.UPGRADE].url}?returnUrl=${encodeURIComponent(location.pathname + location.search)}`,
          );
        }
        if (isIR === false && isCrossOnboarding === true) {
          history.replace(
            Routes[ROUTE_NAME.BLOCK].url,
          );
        }
      }
    }
  }, [history, location, ssoToken, subscriptionDetails, userDetails, userId]);
  const [boDataFetched, setboDataFetched] = useState(false);
  const getBoData = useCallback(() => {
    if (!boDataFetched) {
      setboDataFetched(true);
    }
  }, [boDataFetched]);

  useEffect(() => {
    if (userId && boDataFetched
      && ([IR_STATUS.ACTIVE, IR_STATUS.DORMANCY_IN_PROGRESS,
        IR_STATUS.DORMANT_REVOKED, IR_STATUS.REKYC_IN_PROGRESS].indexOf(userDetails.irStatus) > -1)) {
      setboDataFetched(true);
      getBackOfficeInfo(userId)
        .then(({ data: { ucc, boid, dpid } }) => {
          setUserDetails((prevState) => ({
            ...prevState,
            ucc,
            boid,
            dpid,
          }));
        });
    }
  }, [boDataFetched, userDetails.irStatus, userId]);

  const updateUserDetails = useCallback((details) => {
    setUserDetails((prevState) => ({
      ...prevState,
      ...details,
    }));
  }, []);

  return (
    <UserContext.Provider
      // eslint-disable-next-line react/jsx-no-constructed-context-values
      value={{
        ssoToken,
        mssoToken,
        twoFaToken,
        userId,
        logout,
        login,
        getKycData,
        getIrData,
        getBoData,
        setCombinedIrData,
        displayImage: DEFAULT_USER_ICON,
        updateUserDetails,
        platform,
        showBasketOrder,
        combinedIrData,
        mtfFeatureFlag,
        isSikkimUser,
        isLocationPermissionProvided,
        setIsLocationPermissionProvided,
        locationDetails,
        setLocationDetails,
        locationDisabled,
        setLocationDisabled,
        subscriptionDetails,
        getSubscriptionsDetailsFunction,
        webConfig,
        fetchInProgress,
        riskDisclosureAcceptance,
        setRiskDisclosureAcceptance,
        fetchCombinedIrData,
        fetchInvestmentReadinessData,
        mtfScrips,
        blockerData,
        isH5PollingExhausted,
        setIsH5PollingExhausted,
        getExchangeConfig,
        nonBlockerError,
        setNonBlockerError,
        setNudgeDispatch,
        Context,
        dispatch,
        isMerchant,
        mtwoFaToken,
        priceTickSizeBlockingFlag,
        priceDprBlockingFlag,
        getMTFData,
        ...userDetails,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

function useKycData() {
  const ctx = useContext(UserContext);
  const { getKycData, panNumber } = ctx;

  useEffect(() => {
    if (!panNumber) {
      getKycData();
    }
  }, [getKycData, panNumber]);

  return ctx;
}

function useIrData() {
  const ctx = useContext(UserContext);
  const { getIrData, irStatus } = ctx;

  useEffect(() => {
    if (!irStatus) {
      getIrData();
    }
  }, [getIrData, irStatus]);

  return ctx;
}

function useBoData() {
  const ctx = useContext(UserContext);
  const { getBoData, boid } = ctx;

  useEffect(() => {
    if (!boid) {
      getBoData();
    }
  }, [boid, getBoData]);

  return ctx;
}

export {
  UserContext, UserContextProvider, useIrData, useBoData, useKycData,
};
