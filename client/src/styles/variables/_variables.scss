// SCSS Variables

//colors
$forever-white: var(--forever-white);
$forever-white1: var(--forever-white1);
$forever-black: var(--forever-black);
$default: var(--default);
$primary-color: var(--primary-color);
$light-blue: var(--light-blue);
$secondary-color: var(--secondary-color);
$red: var(--red);
$green: var(--green);
$dark-green: var(--dark-green);
$yellow: var(--yellow);
$light-yellow: var(--light-yellow);
$soft-blue: var(--soft-blue);
$navy-blue: var(--navy-blue);
$success-green: var(--success-green);
$soft-green: var(--soft-green);
$dark-yellow: var(--dark-yellow);
$pink: var(--pink);
$grey0: var(--grey0);
$grey1: var(--grey1);
$grey2: var(--grey2);
$grey3: var(--grey3);
$grey4: var(--grey4);
$grey5: var(--grey5);
$grey7: var(--grey7);
$grey8: var(--grey8);
$bg-color: var(--bg-color);
$bg-color1: var(--bg-color1);
$bg-color2: var(--bg-color2);
$bg-color3: var(--bg-color3);
$box-shadow-color: var(--box-shadow-color);
$backdrop-bg-color: var(--backdrop-bg-color);
$secondary-hyper-link: var(--secondary-hyper-link);
$msite-bg-color: var(--msite-bg-color);
$msite-table-bg: var(--msite-table-bg);
$backdrop-bg-whiteColor: var(--backdrop-bg-whiteColor);
$symbol-box-shadow-color: var(--symbol-box-shadow-color);
$symbol-border-color: var(--symbol-border-color);
$container-box-shadow: var(--container-box-shadow);
$forever-white-rgb: var(--forever-white-rgb);
$darkish-blue: var(--darkish-blue);
$paytm-blue: var(--paytm-blue);

// StyleGuide 2.0
$midnightBlack: var(--midnightBlack);
$paytmBlue: var(--paytmBlue);
$indigo: var(--indigo);
$snow-white: var(--snow-white);
$sandstone: var(--sandstone);
$emeraldLite: var(--emeraldLite);
$strawberryRed: var(--strawberryRed);
$carrot: var(--carrot);
$cafe-honey: var(--cafe-honey);
$emerald: var(--emerald);
$teal: var(--teal);
$sunshine: var(--sunshine);
$peacockBlue: var(----peacockBlue);
$lakeBlue: var(--lakeBlue);
$poolBlue: var(--poolBlue);
$goldenYellow: var(--goldenYellow);
$aqua-blue: var(--aqua-blue);
$parakeet: var(--parakeet);
$pastelPurple: var(--pastelPurple);
$brightSun: var(--brightSun);
$beige: var(--beige);
$latte: var(--latte);
$ultramarineBlue: var(--ultramarineBlue);
$cobaltBlue: var(--cobaltBlue);

//buttons
$pseudo-active: rgba(0, 0, 0, .2);
$pseudo-hover: rgba(0, 0, 0, .1);
$pseudo-disabled: rgba(0, 0, 0, 0);

// Index Bar
$hover-fade: rgba(var(--r), var(--g), var(--b), .95);

// scss-lint:disable ColorVariable
$row-control-holder-gradient: var(--row-control-holder-gradient);

// orders status bar color
$progressBar-gradient: var(--progressBar-gradient);

// gradient for statements info cards
$infoBoxGradient: var(--infoBox-gradient);

// company-charts
$company-charts-view-separator: solid 1px rgba(212, 213, 219, .5);

// faqs-separator
$separator-border-line: solid .1rem rgba(196, 205, 211, .376);

// support-cards box shadow
$card-hover-box-shadow: 0 6px 16px 0 rgba(199, 199, 199, .25);

// static image folder path
$static-image-path: '/stocks/static/images';

$alpha-5: .05;
$alpha-10: .1;
$alpha-20: .2;
$alpha-50: .5;
$alpha-70: .7;
$alpha-80: .8;
$alpha-100: 1;
