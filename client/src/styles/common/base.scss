// Global Reset

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  box-sizing: border-box;

  font-size: 62.75%; // 10px
  scroll-behavior: smooth;
}

html,
body {
  height: 100%;
  width: 100%;
  color: var(--grey1);
}

@font-face {
  font-family: Mulish;
  font-style: normal;

  font-weight: 400;
  src: local('Mulish Regular'), url('/stocks/static/fonts/Mulish-Regular.woff') format('woff');
  font-display: swap;
  unicode-range: U+0041-007A, U+0020;
}

@font-face {
  font-family: Mulish;
  font-style: normal;

  font-weight: 600;
  src: local('Mulish SemiBold'), url('/stocks/static/fonts/Mulish-SemiBold.woff') format('woff');
  font-display: swap;
  unicode-range: U+0041-007A, U+0020;
}

@font-face {
  font-family: Mulish;
  font-style: normal;

  font-weight: 800;
  src: local('Mulish Bold'), url('/stocks/static/fonts/Mulish-Bold.woff') format('woff');
  font-display: swap;
  unicode-range: U+0041-007A, U+0020;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  src: url('/stocks/static/fonts/Inter-fonts.woff2') format('woff2');
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  src: url('/stocks/static/fonts/Inter-fonts.woff2') format('woff2');
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  src: url('/stocks/static/fonts/Inter-fonts.woff2') format('woff2');
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  src: url('/stocks/static/fonts/Inter-fonts.woff2') format('woff2');
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 800;
  src: url('/stocks/static/fonts/Inter-fonts.woff2') format('woff2');
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 300;
  src: url('/stocks/static/fonts/Muli.woff2') format('woff2');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 400;
  src: url('/stocks/static/fonts/Muli.woff2') format('woff2');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 500;
  src: url('/stocks/static/fonts/Muli.woff2') format('woff2');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 600;
  src: url('/stocks/static/fonts/Muli.woff2') format('woff2');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 700;
  src: url('/stocks/static/fonts/Muli-semiBold.woff2') format('woff2');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

@font-face {
  font-family: Muli;
  font-style: normal;
  font-weight: 900;
  src: url('/stocks/static/fonts/Muli-bold.woff') format('woff');
  unicode-range: U0-0FF, U131, U152-153, U2BB-2BC, U2C 6, U2DA, U2DC, U2000-206F, U2074, U20AC, U2122, U2191, U2193, U2212, U2215, UFEFF, UFFFD;
}

body {
  font-family: Mulish, Lato, Helvetica, Arial, sans-serif;
  overflow: hidden;
}

button {
  font-family: inherit;
}

input {
  outline: none;
  background-color: inherit;
  font-family: inherit;
}

a,
a:visited,
a:hover {
  text-decoration: none;
}

[data-theme='global'] {
  --default: #fff;
  --forever-white: #fff;
  --bg-color: #f9fbfd;
  --primary-color: #004393;
  --secondary-color: #0658b9;
  --blue-1: #00baf1;
  --light-blue: #f6fbff;
  --dark-green: #2ba037;
  --lighter-yellow: #faf5f1;
  --light-yellow: #fffce4;
  --green: #33ac40;
  --black: #000;
  --red: #d23d50;
  --grey-1: #494949;
  --grey1: #494949;
  --grey-2: #727682;
  --grey-3: #9b9da0;
  --grey-4: #f3f3f5;
  --grey-5: #f9f9f9;
  --grey5: #f6f7fa;
  --grey-6: #fbfbfc;
  --grey7: #f0f0f0;
}

// CSS Variables
[data-theme='light'] {
  --forever-white: #fff;
  --forever-white1: #fff;
  --forever-black: #000;
  --default: #fff;
  --primary-color: #00c1f2;
  --secondary-color: #004393;
  --secondary-hyper-link: #004393;
  --light-blue: #f6fbff;
  --red: #d23d50;
  --green: #59aa00;
  --dark-green: #27ae60;
  --success-green: #21c179;
  --yellow: #ee9c16;
  --light-yellow: #fffce4;
  --soft-blue: #6a97d7;
  --grey0: #232939;
  --grey1: #494949;
  --grey2: #727682;
  --grey3: #a7abb6;
  --grey4: #eeeef0;
  --grey5: #f6f7fa;
  --grey7: #f0f0f0;
  --grey8: #e7eaf0;
  --bg-color: #f5f7f9;
  --bg-color1: #f4fbfe;
  --bg-color2: #fefbed;
  --bg-color3: #fdf2c7;
  --navy-blue: #95d9f6;
  --soft-green: #a2ddbe;
  --dark-yellow: #f8cf8b;
  --pink: #f2acab;
  --paytm-blue: #00b8f5;
  --forever-white-rgb: 225, 225, 225;
  --box-shadow-color: rgba(173, 175, 182, 0.3);
  --backdrop-bg-color: #494949;
  --backdrop-bg-whiteColor: rgba(255, 255, 255, .8);
  --symbol-border-color: rgba(173, 175, 182, 0.4);
  --symbol-box-shadow-color: rgba(89, 102, 135, 0.06);
  --darkish-blue: rgba(0, 67, 147, 0.25);
  //--r, --g, --b are for SkeletonLoader as of now there is no way to use
  // rbga(var(--color), 0) with scss,
  //rgb and rgba functions misinterpret CSS variables as a single value instead of their output in scss
  --r: 255;
  --g: 255;
  --b: 255;
  --row-control-holder-gradient:
    linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 5%,
      #fff 55%,
      rgba(255, 255, 255, .75) 83%,
      rgba(255, 255, 255, 0)
    );
  --progressBar-gradient: linear-gradient(to right, #7ac7ff, #4689cc 71%, #2461ab 109%);
  --infoBox-gradient: linear-gradient(to left, #6a97d7, #4689cc 25%, #004393 80%);
  --msite-bg-color: rgba(243, 243, 245, 0.5);
  --msite-table-bg: #f7f7f7;
  --container-box-shadow: rgba(128, 152, 213, .06);

  // StyleGuide 2.0
  --midnightBlack: 16, 16, 16;        // #101010
  --paytmBlue: 0, 184, 245;           // #00B8F5
  --indigo: 1, 42, 114;               // #012A72
  --snow-white: 255, 255, 255;        // #FFFFFF
  --sandstone: 208, 145, 0;           // #D09100
  --emeraldLite: 50, 244, 157;        // #32F49D
  --strawberryRed: 253, 81, 84;       // #FD5154
  --carrot: 255, 157, 0;              // #FF9D00
  --cafe-honey: 255, 248, 225;         // #fff8e1
  --emerald: 33, 193, 121;            // #21C179
  --teal: 194, 230, 205;              // #C2E6CD
  --sunshine: 255, 237, 159;          // #FFED9F
  --peacockBlue: 86, 204, 222;        // #56CCDE
  --lakeBlue: 224, 246, 250;          // #E0F6FA
  --poolBlue: 224, 242, 250;          // #E0F2FA
  --goldenYellow: 229, 201, 124;      // #E5C97C
  --aqua-blue: 245, 249, 254;          // #F5F9FE
  --parakeet: 82, 204, 106;           // #52CC6A
  --pastelPurple: 146, 115, 208;      // #9273d0
  --brightSun: 254, 213, 51;          // #fed533
  --beige: 255, 242, 196;             // #fff2c4
  --latte: 255, 227, 161;             // #FFE3A1
  --ultramarineBlue: 41, 0, 122;      // #29007A
  --cobaltBlue: 0, 172, 239;          // #00ACEF
  --textGrey600: rgba(16, 16, 16, 0.7);
}

[data-theme='dark'] {
  --forever-white: #e3e3e8;
  --forever-white1: #101010;
  --forever-black: #000;
  --default: #1c1c1e;
  --primary-color: #0aabd4;
  --secondary-hyper-link: #e3e3e8;
  --secondary-color: #0354b4;
  --light-blue: #0b0e10;
  --red: #d03044;
  --green: #619c20;
  --dark-green: #009b41;
  --yellow: #d88f17;
  --light-yellow: #2e2a23;
  --soft-blue: #4c83d1;
  --grey0: #f5f5f5;
  --grey1: #e3e3e8;
  --grey2: #898989;
  --grey3: #676767;
  --grey4: #313133;
  --grey5: #29292b;
  --grey7: #f0f0f0;
  --grey8: #1c1c1e;
  --bg-color: #000;
  --bg-color1: #1c1c1e;
  --bg-color2: #252111;
  --bg-color3: #504419;
  --navy-blue: #95d9f6;
  --soft-green: #a2ddbe;
  --dark-yellow: #f8cf8b;
  --pink: #f2acab;
  --success-green: #32f49d;
  --paytm-blue: #0a86bf;
  --forever-white-rgb: 225, 225, 225;
  --box-shadow-color: rgba(0, 0, 0, 0.2);
  --backdrop-bg-color: #3a3a3a;
  --backdrop-bg-whiteColor: rgba(255, 255, 255, 0.8);
  --symbol-border-color: rgba(173, 175, 182, 0.4);
  --symbol-box-shadow-color: rgba(89, 102, 135, 0.06);
  --r: 28;
  --g: 28;
  --b: 30;
  --row-control-holder-gradient:
    linear-gradient(
      to right,
      rgba(0, 0, 0, 0) 5%,
      #131313 55%,
      rgba(16, 16, 16, 0.75) 83%,
      rgba(0, 0, 0, 0)
    );
  --progressBar-gradient: linear-gradient(to right, #7ac7ff, #4689cc 71%, #2461ab 109%);
  --infoBox-gradient: linear-gradient(to left, #6a97d7, #4689cc 25%, #004393 80%);
  --msite-bg-color: rgba(243, 243, 245, 0.5);
  --msite-table-bg: #f7f7f7;
  --container-box-shadow: rgba(128, 152, 213, .06);
  --darkish-blue: rgba(0, 67, 147, 0.25);

  // StyleGuide 2.0
  --midnightBlack: 255, 255, 255;      // #ffffff
  --paytmBlue: 10, 171, 212;           // #0aabd4
  --indigo: 9, 105, 218;               // #0969da
  --snow-white: 23, 23, 29;            // #17171d
  --sandstone: 156, 109, 1;            // #9c6d01
  --emeraldLite: 50, 244, 157;         // #32F49D
  --strawberryRed: 208, 48, 68;        // #d03044
  --carrot: 226, 151, 29;              // #e2971d
  --cafe-honey: 44, 40, 28;             // #2c281c
  --emerald: 2, 168, 93;               // #02a85d
  --teal: 48, 66, 54;                  // #304236
  --sunshine: 90, 81, 43;              // #5a512b
  --peacockBlue: 27, 162, 183;         // #1ba2b7
  --lakeBlue: 36, 54, 57;              // #243639
  --poolBlue: 33, 45, 50;              // #212d32
  --goldenYellow: 197, 166, 80;        // #c5a650
  --aqua-blue: 32, 37, 45;              // #20252d
  --parakeet: 23, 181, 55;             // #17b537
  --pastelPurple: 131, 103, 187;       // #8367bb
  --brightSun: 235, 190, 11;           // #ebbe0b
  --beige: 255, 242, 196;              // #fff2c4
  --latte: 255, 227, 161;              // #FFE3A1
  --ultramarineBlue: 41, 0, 122;       // #29007A
  --cobaltBlue: 0, 172, 239;           // #00ACEF
  --textGrey600: rgba(238, 238, 238, 0.7);
  input {
    color: var(--grey1);
  }
}

[type='number']::-webkit-outer-spin-button,
[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

[type='number'] {
  -moz-appearance: textfield;
}

[type='password']::-ms-reveal {
  display: none;
}
