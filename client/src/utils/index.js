import moment from 'moment';
import crypto from 'crypto-js';
import { combineLatest, empty } from 'rxjs';
import LocalStorage from '@service/LocalStorage';
import DOMPurify from 'isomorphic-dompurify';
import {
  ROUTE_NAME, INSTRUMENTS, COOKIES, TARGET_PLATFORM, LOCATION_DETAILS,
} from './enum';
import Routes from '../routes';

const mobileNumberRegex = /^[6-9]\d{9}$/;

function formatTimestamp(timestamp, timeFormat) {
  return moment(timestamp).format(timeFormat);
}

function formatTimestampToDate(timestamp) {
  return formatTimestamp(timestamp, 'DD MMM YYYY');
}

function formatTimestampToDateTime(timestamp) {
  return formatTimestamp(timestamp, 'DD MMM YYYY, hh:mm A');
}

function formatTimestampToIstTime(timestamp) {
  return formatTimestamp(timestamp, 'hh:mm A');
}

function formatTimestampTo24Hours(timestamp) {
  return formatTimestamp(timestamp, 'HH:mm');
}

function formatTimestampToDateTimeIst(timestamp) {
  return formatTimestamp(timestamp, 'dddd, DD MMM YYYY, HH:mm:ss');
}

const moveElementInArray = (arr, from, to) => {
  const newArr = [...arr];
  newArr.splice(to, 0, newArr.splice(from, 1)[0]);
  return newArr;
};

function isValidNumber(value) {
  if (!value) return false;
  if (Number.isNaN(value) || value === Infinity) {
    return false;
  }
  return true;
}

function roundValue(value, decimals = 2, roundOffToLowerInt = false) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const base = 10 ** decimals;
  const mathFn = roundOffToLowerInt ? Math.floor : Math.round;
  return (mathFn(value * base) / base).toFixed(decimals);
}

function getAbsoluteValue(value, decimals = 2, roundOffToLowerInt = false) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const roundedValue = roundValue(value, decimals, roundOffToLowerInt);
  return roundedValue >= 0 ? roundedValue : roundValue(-1 * roundedValue, decimals, roundOffToLowerInt);
}

function getFractionalValue(value, decimals = 2) {
  if (!isValidNumber(value)) {
    return '00';
  }
  const base = 10 ** decimals;
  return Math.round(value * base).toFixed().slice(-decimals);
}

function isDecimalRequired(value, decimals = 2) {
  if (!isValidNumber(value)) {
    return '0';
  }
  const roundedValue = roundValue(value, decimals);
  return parseFloat(roundedValue);
}

function formatPrice(value, decimals = 2, roundOffToLowerInt = false) {
  if (!isValidNumber(value)) {
    return `0${decimals ? `.${'0'.repeat(decimals)}` : ''}`;
  }
  return new Intl.NumberFormat('en-IN', { minimumFractionDigits: decimals }).format(getAbsoluteValue(value, decimals, roundOffToLowerInt));
}

const regExTester = (type) => {
  const regExobj = {
    name: /^[a-zA-Z]+((['. -]{1}[a-zA-Z])?[a-zA-Z]*)*$/,
    email: new RegExp(
      [
        '^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*',
        ')|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.',
        '[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$',
      ].join(''),
    ),
    text: /^(?!\s*$).+/,
    textarea: /[A-Za-z0-9'.\-\s,]+/,
    tel: /^(\+\d{1,3}[- ]?)?\d{10}$/,
    gstin: /[0-9]{2}[a-zA-Z]{5}[0-9]{4}[a-zA-Z][A-Z0-9][Z][A-Z0-9]/, // Provide  input value as uppercase
    pincode: /^[1-9][0-9]{5}$/,
    password: /^(?=.*\d)(?=.*[a-zA-Z]).{5,15}$/,
  };
  const regExRule = regExobj[type];
  const ex = new RegExp(regExRule);
  return ex;
};

function generateRandomString(len) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < len; i += 1) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const isBrowser = () => typeof window !== 'undefined';

export const isMobile = {
  android() {
    return navigator.userAgent.match(/Android/i) ? 'android' : false;
  },
  iOS() {
    return navigator.userAgent.match(/iPhone|iPad|iPod/i) ? 'ios' : false;
  },
  any() {
    return isMobile.android() || isMobile.iOS();
  },
};

function mobileBrowser() {
  if (!isBrowser()) return false;
  return isMobile.any();
}

function getEncodedURI(url, pathname) {
  return `redirect=${encodeURIComponent(url)}&pathname=${pathname.slice(1)}`;
}

function getMaskedValue(str) {
  return `${str}`.replace(/.(?=.{4})/g, '*');
}

function copyToClipboard(str) {
  const el = document.createElement('textarea');
  el.value = str;
  document.body.appendChild(el);
  el.select();
  document.execCommand('copy');
  document.body.removeChild(el);
}

function downloadFileFromData(fileData, fileName = 'download', fileType = 'pdf') {
  const file = new Blob([fileData], { type: 'application/pdf' });
  const url = window.URL.createObjectURL(file);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', `${fileName}.${fileType}`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function getTime({ dateTime, format }) {
  return moment(dateTime).format(format);
}

function getDateWithMonthInWords(date) {
  const d = moment(date, 'DD-MM-YYYY').format('YYYY-MM-DD');
  return moment(d).format('DD MMM YYYY');
}

function getDateInDdMmmYyyy(date) {
  return moment(date).format('DD MMM YYYY');
}

function getDateDifference(startMoment, endDate, dateFormat) {
  if (!endDate) {
    return 0;
  }
  const endMoment = typeof endDate === 'number' ? moment(endDate) : moment(endDate, dateFormat);
  return endMoment.isValid() ? startMoment.diff(endMoment) : 0;
}

function decimalSeparator(number) {
  const numberString = number.toString();
  let intPart = numberString;
  let decimalPart = null;
  if (numberString.includes('.')) {
    decimalPart = 0;
    [intPart, decimalPart] = numberString.split('.');
  }
  return {
    intPart: parseInt(intPart, 10),
    decimalPart,
  };
}

function getCookieValue(name) {
  const b = document.cookie.match(`(^|[^;]+)\\s*${name}\\s*=\\s*([^;]+)`);
  return b ? decodeURIComponent(b.pop()) : '';
}

function setCookie(name, value, expTime) {
  const expOn = expTime ? new Date(expTime) : new Date(2021, 11, 31);
  const expires = expOn.toUTCString();
  const path = '/';
  const secure = 'Secure';
  const cookie = `${name}=${value}; expires=${expires}; path=${path}; domain=.paytmmoney.com; ${secure}`;
  document.cookie = cookie;
}

function deleteCookie(name) {
  const expires = new Date().toUTCString();
  const path = '/';
  const cookie = `${name}=; expires=${expires}; path=${path}; domain=.paytmmoney.com`;
  const cookieOnCurrentDomain = `${name}=; expires=${expires}; path=${path};`;
  document.cookie = cookie;
  document.cookie = cookieOnCurrentDomain;
}

function getLocationDetails() {
  if (isBrowser()) {
    return LocalStorage.get(LOCATION_DETAILS, true) ? JSON.stringify(LocalStorage.get(LOCATION_DETAILS, true)) : null;
  }
  return null;
}

function getUserID() {
  if (isBrowser()) {
    const userAgent = getCookieValue(COOKIES.USER_AGENT);
    if (userAgent) {
      return (JSON.parse(userAgent)?.user_id || '');
    }
  }
  return '';
}

function convertStringToNumber(str) {
  return str.replace(/[^\d.-]/g, '');
}

function getValueFromPriceFormat(inputValue) {
  const parsedValue = `${inputValue}`.split(',').join('');
  return parseFloat(convertStringToNumber(parsedValue));
}

function getFloatValue(value) {
  return parseFloat(value).toFixed(2);
}

const getCombinedFeed = (stockFeed) => combineLatest(stockFeed.map(({ feed }) => feed || empty()));

const getFeedByReqType = (stockFeed, reqType) => {
  const reqFeed = stockFeed.find(({ type }) => type === reqType);
  return reqFeed ? reqFeed.feed : empty();
};

const getLtpChangeFromTradeClose = (trade, close) => {
  const change = trade?.lastTradePrice - close?.previousPrice;
  const percentageChange = (change * 100) / close?.previousPrice;
  const ltp = trade?.lastTradePrice;
  return {
    ltp, change, percentageChange, pClose: close?.previousPrice,
  };
};

const formatDate = (date) => {
  if (!date) return null;
  const onesDigit = date % 10;
  const tensNumber = date % 100;
  if (onesDigit === 1 && tensNumber !== 11) {
    return `${date}st`;
  }
  if (onesDigit === 2 && tensNumber !== 12) {
    return `${date}nd`;
  }
  if (onesDigit === 3 && tensNumber !== 13) {
    return `${date}rd`;
  }
  return `${date}th`;
};

const formatDateTimeString = (date, inputDtFormat, outputDtFormat) => {
  // const isValidDate = moment(date, inputDtFormat, true).isValid(); // use for strict format mode
  const isValidDate = moment(date, inputDtFormat).isValid();
  if (isValidDate) {
    return moment(date, inputDtFormat).format(outputDtFormat);
  }
  return null;
};

const isArrayElementsValid = (arr) => {
  let isValid = true;
  arr.some((val) => {
    const isNumber = typeof val === 'number';
    isValid = isNumber;
    return !isNumber;
  });
  return isValid;
};

const capitalizeFirstLetter = (str) => {
  const str_l = str.toLowerCase();
  return `${str[0].toUpperCase()}${str_l.substring(1)}`;
};

const getQueryParam = (query) => Object.entries(query).reduce((acc, [key, value]) => {
  if (value) {
    if (acc.length === 1) {
      return `${acc}${key}=${value}`;
    }
    return `${acc}&${key}=${value}`;
  } return acc;
}, '?');

const setQueryParam = ({ query, pathname, replace }) => {
  const queryParams = getQueryParam(query);
  replace(`${pathname}${queryParams}`);
};

const getUrlParameter = (name, queryString) => {
  const regex = new RegExp(`[\\?&]${name}=([^&#]*)`);
  const results = regex.exec(queryString);
  return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

const datesDiffInDays = (from, to) => {
  const diffInTime = to.getTime() - from.getTime();
  const diffInDays = diffInTime / (1000 * 3600 * 24);
  return Math.ceil(diffInDays);
};

const getPercentageChange = (oldValue, newValue) => {
  if (!oldValue) return NaN;
  const change = newValue - oldValue;
  return (change * 100) / oldValue;
};

function sum(collection = [], property = null) {
  return collection.reduce((runningSum, currentItem) => {
    const current = parseFloat(property ? currentItem[property] : currentItem);
    return runningSum + current;
  }, 0.0);
}

function mapQueryString(query) {
  if (!query) {
    return {};
  }
  const queryMap = {};
  const queryParts = query.split('&');
  queryParts.forEach((part) => {
    const [key, value] = part.split('=');
    queryMap[key] = value;
  });
  return queryMap;
}

function checkValidMobileNumber(input) {
  return input.length === 10 && mobileNumberRegex.test(input);
}

const renameKeys = (keysMap, obj) => Object.keys(obj).reduce(
  (acc, key) => ({
    ...acc,
    ...{ [keysMap[key] || key]: obj[key] },
  }), {},
);

const scrollToTop = () => {
  window.scrollTo(0, 0);
};

const emptyFn = () => { };

const emptyArr = [];

const emptyObj = {};

const deduplicate = (arr) => [...new Map(arr.map((item) => [item.id, item])).values()];

const width = 1000;
const height = 700;

const openWindowWithPost = ({ url, params, target = '_self' }) => {
  const form = document.createElement('form');
  form.setAttribute('method', 'post');
  form.setAttribute('action', url);

  Object.keys(params).forEach((name) => {
    const input = document.createElement('input');
    input.setAttribute('type', 'hidden');
    input.setAttribute('name', name);
    input.setAttribute('value', params[name]);
    form.appendChild(input);
  });

  if (target === '_blank') {
    const left = (window.screen.width / 2) - (width / 2);
    const top = (window.screen.height / 2) - (height / 2);
    const w = window.open(
      '',
      '_blank',
      `top=${top},left=${left},width=${width},height=${height}`,
    );
    w.document.body.appendChild(form);
    form.submit();
  } else {
    document.body.appendChild(form);
    form.submit();
  }
};

function getSourceNameFromRoute(routes, pathname) {
  let routeLink;
  const sourceRoute = routes.reduce((route) => {
    const { link } = route;
    if (Array.isArray(link)) {
      routeLink = route.defaultLink;
    } else {
      routeLink = link;
    }
    if (routeLink !== pathname) {
      return `${pathname.split('/')[1]}`;
    }
    return route;
  });
  if (sourceRoute.name) {
    return sourceRoute.name.toLowerCase();
  }
  return sourceRoute;
}

function getInstrumentLink(instrumentType) {
  switch (instrumentType) {
    case INSTRUMENTS.INDEX:
      return Routes[ROUTE_NAME.INDEX_PAGE].url;

    case INSTRUMENTS.ETF:
      return Routes[ROUTE_NAME.ETF].url;

    case INSTRUMENTS.REIT:
      return Routes[ROUTE_NAME.REIT].url;

    case INSTRUMENTS.INVITU:
      return Routes[ROUTE_NAME.INVITU].url;

    case INSTRUMENTS.FUTIDX:
      return Routes[ROUTE_NAME.FUTIDX].url;

    case INSTRUMENTS.FUTSTK:
      return Routes[ROUTE_NAME.FUTSTK].url;

    case INSTRUMENTS.OPTIDX:
      return Routes[ROUTE_NAME.OPTIDX].url;

    case INSTRUMENTS.OPTSTK:
      return Routes[ROUTE_NAME.OPTSTK].url;

    case INSTRUMENTS.DBT:
      return Routes[ROUTE_NAME.DBT].url;

    case INSTRUMENTS.DEB:
      return Routes[ROUTE_NAME.DEB].url;

    case INSTRUMENTS.GB:
      return Routes[ROUTE_NAME.GB].url;

    case INSTRUMENTS.CB:
      return Routes[ROUTE_NAME.CB].url;

    default:
      return Routes[ROUTE_NAME.COMPANY_PAGE].url;
  }
}

function isBondInstrument(instrument_type) {
  return (instrument_type === INSTRUMENTS.DBT
    || instrument_type === INSTRUMENTS.GB
    || instrument_type === INSTRUMENTS.CB
    || instrument_type === INSTRUMENTS.DEB);
}

function createLogger(scope) {
  return function logger(loc, ...args) {
    const label = `[[ ${scope} :: ${loc} ]] ~~~> `;
    // eslint-disable-next-line prefer-spread
    console.log.apply(console, [].concat(label, args));
  };
}

const timeZoneOffset = new Date().getTimezoneOffset();

function getIndianDate(cur_date_time) {
  const currentDate = new Date(cur_date_time + (timeZoneOffset + 330) * 60000);
  return moment(currentDate).format().split('T')[0];
}

function getTimeToEvent(cur_date_time, timerTime, showMktTime = false) {
  const [hours, minutes, seconds = '00'] = timerTime.split(':');
  const eventTime = new Date(`${getIndianDate(cur_date_time)}T${hours}:${minutes}:${seconds}.000+05:30`);
  if (showMktTime) {
    return {
      mktTime: timerTime,
      timeToEvent: eventTime.getTime() - cur_date_time,
    };
  }
  return (eventTime.getTime() - cur_date_time);
}

function getFormattedTime(time) {
  const [minutes, seconds] = [Math.floor(time / 60), time % 60];
  return `${minutes < 10 ? 0 : ''}${minutes}:${seconds < 10 ? 0 : ''}${seconds}`;
}

function formatStrToUrlParam(str) {
  return str.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '-').toLowerCase();
}

const isWebView = () => {
  const userAgent = getCookieValue(COOKIES.USER_AGENT);
  const platforms = [TARGET_PLATFORM.ANDROID, TARGET_PLATFORM.IOS, TARGET_PLATFORM.IBL_ANDROID,
    TARGET_PLATFORM.IBL_IOS];
  const { platform } = userAgent !== '' ? JSON.parse(userAgent) : {};
  return mobileBrowser() && (platforms.includes(platform));
};

const uniqueArrayVal = (arr, key) => (
  arr.filter((obj, index) => arr.findIndex((item) => item[key] === obj[key]) === index)
);
const SECRET_KEY_MSG = 'Paytmmoney login page';
function obfuscatePassword(data) {
  const iv = crypto.enc.Base64.parse('');
  const key = crypto.SHA256(SECRET_KEY_MSG);
  let encryptedString = '';
  if (typeof data === 'string') {
    const encData = data.slice();
    encryptedString = crypto.AES.encrypt(encData, key, {
      iv,
      mode: crypto.mode.CBC,
      padding: crypto.pad.Pkcs7,
    });
  } else {
    encryptedString = crypto.AES.encrypt(JSON.stringify(data), key, {
      iv,
      mode: crypto.mode.CBC,
      padding: crypto.pad.Pkcs7,
    });
  }
  return encryptedString.toString();
}

function extractData(encrypted) {
  const iv = crypto.enc.Base64.parse('');
  const key = crypto.SHA256(SECRET_KEY_MSG);
  const decrypted = crypto.AES.decrypt(encrypted, key, {
    iv,
    mode: crypto.mode.CBC,
    padding: crypto.pad.Pkcs7,
  });
  return decrypted.toString(crypto.enc.Utf8);
}

const SANITIZE_INPUT_CONFIG = {
  ALLOWED_TAGS: [],
};

const DEFAULT_SANITIZE_CONFIG = {
  FORBID_TAGS: ['svg', 'math', 'style', 'foreignObject', 'iframe', 'object', 'embed'],
  FORBID_ATTR: ['xlink:href', 'xmlns', 'formaction', 'style', 'target', '__proto__'],
  ALLOWED_URI_REGEXP: /^(?:(?!javascript:|data:|vbscript:).)*$/i,
};

const sanitizeHtml = (html, config) => DOMPurify.sanitize(html, config || DEFAULT_SANITIZE_CONFIG);

function paramsToObject(entries) {
  const result = {};
  Array.from(entries).forEach(([key, value]) => {
    result[key] = value;
  });
  return result;
}

export {
  formatTimestamp,
  getDateWithMonthInWords,
  getDateInDdMmmYyyy,
  formatTimestampToDate,
  formatTimestampToIstTime,
  formatTimestampToDateTimeIst,
  moveElementInArray,
  roundValue,
  getAbsoluteValue,
  getFractionalValue,
  isDecimalRequired,
  formatPrice,
  regExTester,
  generateRandomString,
  isBrowser,
  mobileBrowser,
  getEncodedURI,
  copyToClipboard,
  decimalSeparator,
  getTime,
  getCookieValue,
  setCookie,
  deleteCookie,
  convertStringToNumber,
  getCombinedFeed,
  getFeedByReqType,
  getLtpChangeFromTradeClose,
  formatDate,
  isArrayElementsValid,
  downloadFileFromData,
  capitalizeFirstLetter,
  isValidNumber,
  formatTimestampToDateTime,
  getUrlParameter,
  datesDiffInDays,
  getQueryParam,
  setQueryParam,
  getPercentageChange,
  sum,
  getDateDifference,
  mapQueryString,
  getValueFromPriceFormat,
  getFloatValue,
  getMaskedValue,
  checkValidMobileNumber,
  renameKeys,
  scrollToTop,
  emptyFn,
  emptyArr,
  openWindowWithPost,
  deduplicate,
  getSourceNameFromRoute,
  getInstrumentLink,
  formatDateTimeString,
  createLogger,
  getIndianDate,
  getFormattedTime,
  getTimeToEvent,
  formatStrToUrlParam,
  isBondInstrument,
  formatTimestampTo24Hours,
  isWebView,
  getLocationDetails,
  uniqueArrayVal,
  getUserID,
  obfuscatePassword,
  sanitizeHtml,
  extractData,
  paramsToObject,
  emptyObj,
  SANITIZE_INPUT_CONFIG,
};
