const SORTING_ORDER = {
  ASCENDING: 'ASCENDING',
  DESCENDING: 'DESCENDING',
};

const EXCHANGE = {
  NSE: 'NSE',
  BSE: 'BSE',
};

const MTF_NOT_ACTIVATED_STATUSES = ['NOT_OPTED', 'SUBSCRIPTION_PENDING', 'UNDER_REGISTRATION', 'REVOKED'];

const MTF_NOT_ACTIVATED_STATUSES_ACCOUNTS = ['NOT_OPTED', 'SUBSCRIPTION_PENDING', 'UNDER_REGISTRATION'];

const MTF_STATUSES = {
  NOT_OPTED: 'NOT_OPTED',
  SUBSCRIPTION_PENDING: 'SUBSCRIPTION_PENDING',
  UNDER_REGISTRATION: 'UNDER_REGISTRATION',
  ACTIVE: 'ACTIVE',
  REVOKED: 'REVOKED',
};

const SEGMENT_TYPES = {
  CASH: 'E',
  DERIVATIVES: 'D',
};

const EXCHANGE_CODE = {
  [EXCHANGE.NSE]: 1,
  [EXCHANGE.BSE]: 4,
  [SEGMENT_TYPES.CASH]: {
    [EXCHANGE.NSE]: 1,
    [EXCHANGE.BSE]: 4,
  },
  [SEGMENT_TYPES.DERIVATIVES]: {
    [EXCHANGE.NSE]: 2,
    [EXCHANGE.BSE]: 8,
  },
};

const EXIT = 'EXIT';

const PRODUCT_TYPES = {
  DELIVERY: 'C',
  MARGIN: 'M',
  INTRADAY: 'I',
  BRACKET_ORDER: 'B',
  COVER_ORDER: 'V',
  MTF: 'F',
};

const ORDER_TYPES = {
  MKT: 'MKT',
  LMT: 'LMT',
  SLM: 'SLM',
  SL: 'SL',
};

const TRANSACTION_TYPES = {
  BUY: 'B',
  SELL: 'S',
};

const STATUS = {
  PREOPEN: 'PRE-OPEN',
  LIVE: 'LIVE',
  CLOSED: 'CLOSED',
  POSTCLOSE: 'POST-CLOSE',
};

const THEMES = {
  DARK: 'dark',
  LIGHT: 'light',
  GLOBAL: 'global',
};

const MARKET_CAP_TYPE = {
  MCAP_KEY: 'mcap_type',
  LARGE_CAP: 'Large Cap',
  MID_CAP: 'Mid Cap',
  SMALL_CAP: 'Small Cap',
};

const INSTRUMENTS = {
  STOCK: 'ES',
  INDEX: 'I',
  ETF: 'ETF',
  FUTIDX: 'FUTIDX',
  FUTSTK: 'FUTSTK',
  OPTIDX: 'OPTIDX',
  OPTSTK: 'OPTSTK',
  REIT: 'REIT',
  INVITU: 'InvITU',
  DBT: 'DBT',
  GB: 'GB',
  CB: 'CB',
  DEB: 'DEB',
};

const WEEKDAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

const COOKIES = {
  SSO_TOKEN: 'x-sso-token',
  M_SSO_TOKEN: 'x-m-sso-token',
  USER_AGENT: 'x-user-agent',
  UID: 'x-uid',
  DEVICE_ID: 'device_id',
  LOGIN_FLOW: 'login-flow',
  TWOFA_TOKEN: 'x-2fa-token',
  M_TWOFA_TOKEN: 'x-m-2fa-token',
  TWOFA_TOKEN_EXPIRY: 'x-m-2fa-token-expiry',
  M_USER_AGENT: 'x-m-user-agent',
};

const USERNAME = 'userName';
const TRANSACTION_ID = 'transactionId';
const FNO_SEARCH_ENABLE = 'fno-search-enable';
const ORDERS_SORT = 'ordersSort';
const POSITIONS_SORT = 'positionsSort';
const LOCATION_DETAILS = 'locationDetails';
const HOLDINGS_SORT = 'holdingsSort';
const EXCHANGE_INST_NAME = 'exchange_inst_name';
const REGENERATE_TOTP = 'regenerateTOTP';
const FORGOT_PASSCODE = 'forgotPasscode';
const STICKY_ORDER = 'stickyOrder';
const IS_BASKET_ACCESSIBLE = 'isBasketAccessible';
const MTF_INFO_LINK = 'https://www.paytmmoney.com/blog/all-you-need-to-know-about-mtf/';
const MTF_FEATURE_FLAG = 'mtfFeatureFlag';
const OTP_DATA = 'otpApiResponse';

const IR_STATUS = {
  ACTIVE: 'ACTIVE',
  VERIFIED: 'VERIFIED',
  KYC_PENDING: 'KYC_PENDING',
  KYC_IN_PROGRESS: 'KYC_IN_PROGRESS',
  KYC_VERIFICATION_IN_PROGRESS: 'KYC_VERIFICATION_IN_PROGRESS',
  ACCOUNT_CREATION_IN_PROGRESS: 'ACCOUNT_CREATION_IN_PROGRESS',
  UNDER_REGISTRATION: 'UNDER_REGISTRATION',
  E_SIGN_PENDING: 'E_SIGN_PENDING',
  REVOKED: 'REVOKED',
  DORMANCY_IN_PROGRESS: 'DORMANCY_IN_PROGRESS',
  DORMANT_REVOKED: 'DORMANT_REVOKED',
  REKYC_IN_PROGRESS: 'REKYC_IN_PROGRESS',
  NOT_INVESTED: 'NOTINVESTED',
  NOT_TRADED: 'NOTTRADED',
  NOT_OPTED: 'NOT_OPTED',
  DOCUMENTS_PENDING: 'DOCUMENTS_PENDING',
  DOCUMENTS_SUBMITTED: 'DOCUMENTS_SUBMITTED',
  ESIGN_PENDING: 'ESIGN_PENDING',
  ESIGN_UNDER_VERIFICATION: 'ESIGN_UNDER_VERIFICATION',
};

const IR_STATUS_REVOKE_SUBTYPE = {
  AADHAAR_PAN_CONNECTION: 'AADHAR_NOT_SEEDED_PAN_REVOKED',
  KRA_VALIDATION_REVOKED: 'KRA_VALIDATION_REVOKED',
  FREEZE: 'ACCOUNT_FREEZE',
};

const ROUTE_NAME = {
  LOGGED_IN: 'LOGGED_IN',
  HOME: 'HOME',
  MARKET: 'MARKET',
  PORTFOLIO: 'PORTFOLIO',
  PNL: 'PNL',
  PNL_DETAIL: 'PNL_DETAIL',
  PORTFOLIO_ANALYTICS: 'PORTFOLIO_ANALYTICS',
  PORTFOLIO_EDIS_COLLECTION: 'PORTFOLIO_EDIS_COLLECTION',
  POSITIONS: 'POSITIONS',
  ORDERS: 'ORDERS',
  WATCHLIST_DASHBOARD: 'WATCHLIST_DASHBOARD',
  COMPANY_PAGE: 'COMPANY_PAGE',
  ETF: 'ETF',
  FUNDS: 'FUNDS',
  INDEX_PAGE: 'INDEX_PAGE',
  USER_PROFILE: 'USER_PROFILE',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  STOCK_INSTRUCTIONS: 'STOCK_INSTRUCTIONS',
  ALL_STATEMENTS: 'ALL_STATEMENTS',
  PAST_ORDERS: 'PAST_ORDERS',
  NOTIFICATION_PREFERENCES: 'NOTIFICATION_PREFERENCES',
  MANAGE_PASSCODE: 'MANAGE_PASSCODE',
  MANAGE_APPS: 'MANAGE_APPS',
  MANAGE_COMMUNICATION: 'MANAGE_COMMUNICATION',
  CHANGE_PASSCODE: 'CHANGE_PASSCODE',
  CHANGE_APP_LAUNCHSCREEN: 'CHANGE_APP_LAUNCHSCREEN',
  MANAGE_TOTP: 'MANAGE_TOTP',
  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
  STOCK_SIPS: 'STOCK_SIPS',
  PRICE_ALERTS: 'PRICE_ALERTS',
  TODAY_ORDERS: 'TODAY_ORDERS',
  UPCOMING_SIPS: 'UPCOMING_SIPS',
  PROFILE: 'PROFILE',
  LOGIN: 'LOGIN',
  PASSCODE: 'PASSCODE',
  APP_PERMISSION: 'APP_PERMISSION',
  MERCHANT_NON_IR: 'MERCHANT_NON_IR',
  APP_LINKS: 'APP_LINKS',
  HOLIDAYS: 'HOLIDAYS',
  CONTACT_US: 'CONTACT_US',
  WATCHLIST_LAYOUT_ROUTES: 'WATCHLIST_LAYOUT_ROUTES',
  INDICES: 'INDICES',
  MOVERS: 'MOVERS',
  RECENT: 'RECENT',
  INDEX_OVERVIEW: 'INDEX_OVERVIEW',
  INDEX_STOCKS: 'INDEX_STOCKS',
  INDEX_INSTRUCTIONS: 'INDEX_INSTRUCTIONS',
  COMPANY_OVERVIEW: 'COMPANY_OVERVIEW',
  COMPANY_POSITIONS: 'COMPANY_POSITIONS',
  COMPANY_PORTFOLIO: 'COMPANY_PORTFOLIO',
  COMPANY_INSTRUCTIONS: 'COMPANY_INSTRUCTIONS',
  COMPANY_FINANCIAL: 'COMPANY_FINANCIAL',
  COMPANY_ABOUT: 'COMPANY_ABOUT',
  LANDING: 'LANDING',
  BENEFITS: 'BENEFITS',
  FEATURES: 'FEATURES',
  ONBOARDING: 'ONBOARDING',
  ACCOUNT_PROCESS: 'ACCOUNT_PROCESS',
  KYC_PAYMENT: 'KYC_PAYMENT',
  POLICIES: 'POLICIES',
  TERMS_AND_CONDITIONS: 'TERMS_AND_CONDITIONS',
  PRIVACY_POLICY: 'PRIVACY_POLICY',
  DISCLAIMER: 'DISCLAIMER',
  REGULATORY_CONTENT: 'REGULATORY_CONTENT',
  CUSTOMER: 'CUSTOMER',
  CUSTOMER_SUPPORT: 'CUSTOMER_SUPPORT',
  TRADING_HOLIDAYS: 'TRADING_HOLIDAYS',
  SAFETY_AND_SECURITY: 'SAFETY_AND_SECURITY',
  DOWNLOAD_CENTER: 'DOWNLOAD_CENTER',
  GRIEVANCE_REDRESSAL: 'GRIEVANCE_REDRESSAL',
  GRIEVANCE_ESCALATION_MATRIX: 'GRIEVANCE_ESCALATION_MATRIX',
  GRIEVANCE_DATA: 'GRIEVANCE_DATA',
  GRIEVANCE_DATA_EX: 'GRIEVANCE_DATA_EX',
  GRIEVANCE_DATA_DP: 'GRIEVANCE_DATA_DP',
  GRIEVANCE_DATA_RA: 'GRIEVANCE_DATA_RA',
  PRICING: 'PRICING',
  MARGIN_PLEDGE_STOCKS_AND_HAIRCUT: 'MARGIN_PLEDGE_STOCKS_AND_HAIRCUT',
  MARGIN_HAIRCUT: 'MARGIN_HAIRCUT',
  BROKERAGE_CALCULATOR: 'BROKERAGE_CALCULATOR',
  BROKERAGE_CALCULATOR_PROFILE: 'BROKERAGE_CALCULATOR_PROFILE',
  TPIN_AUTH: 'TPIN_AUTH',
  EDIS_REDIRECT: 'EDIS_REDIRECT',
  EDIS_REVOKE_AUTH: 'EDIS_REVOKE_AUTH',
  ETFS: 'ETFS',
  IPO: 'IPO',
  IPO_LISTING: 'IPO_LISTING',
  IPO_DETAILS: 'IPO_DETAILS',
  IPO_ORDERS: 'IPO_ORDERS',
  SGB: 'SGB',
  SGB_LISTING: 'SGB_LISTING',
  SGB_DETAILS: 'SGB_DETAILS',
  SGB_ORDERS: 'SGB_ORDERS',
  FUTIDX: 'FUTIDX',
  FUTSTK: 'FUTSTK',
  OPTIDX: 'OPTIDX',
  OPTSTK: 'OPTSTK',
  FNO_FAQ: 'FNO_FAQ',
  MARGIN_CALCULATOR: 'MARGIN_CALCULATOR',
  MARGIN_CALCULATOR_PROFILE: 'MARGIN_CALCULATOR_PROFILE',
  MARGIN_PLEDGE: 'MARGIN_PLEDGE',
  MARGIN_REQUESTS: 'MARGIN_REQUESTS',
  MARGIN_PLEDGE_REQUESTS: 'MARGIN_PLEDGE_REQUESTS',
  MARGIN_UNPLEDGE_REQUESTS: 'MARGIN_UNPLEDGE_REQUESTS',
  MARGIN_PLEDGE_REVIEW: 'MARGIN_PLEDGE_REVIEW',
  MARGIN_PLEDGE_HOLDINGS: 'MARGIN_PLEDGE_HOLDINGS',
  MARGIN_PLEDGE_STATUS: 'MARGIN_PLEDGE_STATUS',
  MARGIN_PLEDGE_SELECT: 'MARGIN_PLEDGE_SELECT',
  MARGIN_PLEDGE_INTRO: 'MARGIN_PLEDGE_INTRO',
  MARGIN_PLEDGE_HOME: 'MARGIN_PLEDGE_HOME',
  MARGIN_PLEDGE_AUTH_REDIRECT: 'MARGIN_PLEDGE_AUTH_REDIRECT',
  ESIGN_QRCODE: 'ESIGN_QRCODE',
  PORTFOLIO_LAYOUT: 'PORTFOLIO_LAYOUT',
  LOCATION_CAPTURE: 'LOCATION_CAPTURE',
  PORTFOLIO_OFFMARKET: 'PORTFOLIO_OFFMARKET',
  PORTFOLIO_OFFMARKET_REVIEW: 'PORTFOLIO_OFFMARKET_REVIEW',
  FNO: 'FNO',
  FNO_DASHBOARD: 'FNO_DASHBOARD',
  FNO_MOVERS: 'FNO_MOVERS',
  FNO_OPTION_CHAIN: 'FNO_OPTION_CHAIN',
  FNO_FII_DII: 'FNO_FII_DII',
  UPGRADE: 'UPGRADE',
  BLOCK: 'BLOCK',
  GTT: 'GTT',
  BANK_TRANSFER: 'BANK_TRANSFER',
  UPGRADE_REDIRECT: 'UPGRADE_REDIRECT',
  REIT: 'REIT',
  INVITU: 'INVITU',
  FREE_ETF: 'FREE_ETF',
  WATCHLIST: 'WATCHLIST',
  COMPANY_MORE: 'COMPANY_MORE',
  DEB: 'DEB',
  DBT: 'DBT',
  GB: 'GB',
  CB: 'CB',
  DEB_MORE: 'DEB_MORE',
  DBT_MORE: 'DBT_MORE',
  GB_MORE: 'GB_MORE',
  CB_MORE: 'CB_MORE',
  PORTFOLIO_INSIGHTS: 'PORTFOLIO_INSIGHTS',
  STOCK_ETF_SCREENER: 'STOCK_ETF_SCREENER',
  FUTURE_CONTRACT: 'FUTURE_CONTRACT',
  FUND_HISTORY: 'FUND_HISTORY',
  LEDGER_HISTORY: 'LEDGER_HISTORY',
  SUBSCRIPTIONS: 'SUBSCRIPTIONS',
  AUTOPAY: 'AUTOPAY',
  HEAT_MAP_SECTION: 'HEAT_MAP_SECTION',
  BASKET_ORDERS: 'BASKET_ORDERS',
  BASKET_PAGE: 'BASKET_PAGE',
  GLOBAL_MARKET: 'GLOBAL_MARKET',
  FNO_NEWS: 'FNO_NEWS',
  HIGH_LOW_MOVERS: 'HIGH_LOW_MOVERS',
  TRADING_IDEAS: 'TRADING_IDEAS',
  MTF: 'MTF',
  AVAILABLE_SEGMENTS: 'AVAILABLE_SEGMENTS',
  MTF_STATEMENTS: 'MTF_STATEMENTS',
  RISK_DICSLOSURE: 'RISK_DISCLOSURE',
  STATUTORY_DOCUMENTS: 'STATUTORY_DOCUMENTS',
  MITC: 'MITC',
  MTF_SCRIPS: 'MTF_SCRIPS',
};

const MOBILE_SYSTEM_TYPE = {
  ANDROID: 'android',
  IOS: 'ios',
};

const INFO_CARD = {
  WATCHLIST: 'watchlist',
  HOLDINGS: 'holdings',
  POSITIONS: 'positions',
  ORDERS: 'orders',
  ADD_FUNDS: 'add_funds',
  WITHDRAW_FUNDS: 'withdraw_funds',
  ACCOUNTS: 'accounts',
  FNO_DASHBOARD: 'fno_dashboard',
};

const TARGET_PLATFORM = {
  ANDROID: 'android',
  IOS: 'ios',
  WEB: 'web',
  IBL_ANDROID: 'ibl-android',
  IBL_IOS: 'ibl-ios',
  MWEB: 'web-mobile',
};
const INDEX_EXCHANGE_ID = 0;

const EDIS_BULK_AUTH_KEY = 'edisBulkAuthPayload';

const FIELDS_LABLES = {
  PRICE: 'Price',
  REM_QTY: 'Rem. Qty',
  QUANTITY: 'Quantity',
  TRIGGER_PRICE: 'Trigger Price',
  EXECUTED_QUANTITY: 'Executed Quantity',
  PENDING_EXECUTION: 'Pending Execution',
};

const LOCATION_DATA = 'location-data';

const PREPROD_URL = 'https://web-preprod.paytmmoney.com';

const NO_BLOCKING_ERROR_CASES = [
  'showMessageNoBlock', 'noMessageNoBlock',
];

const BLOCKING_ERROR_CASES = [
  'showMessageAndBlock',
];

const RUPEE_SYMBOL = '₹';

const EMPTY_ARRAY = [];

const EMPTY_OBJECT = {};

module.exports = {
  SORTING_ORDER,
  EXCHANGE,
  SEGMENT_TYPES,
  EXCHANGE_CODE,
  PRODUCT_TYPES,
  ORDER_TYPES,
  TRANSACTION_TYPES,
  STATUS,
  THEMES,
  INSTRUMENTS,
  WEEKDAYS,
  COOKIES,
  IR_STATUS,
  IR_STATUS_REVOKE_SUBTYPE,
  USERNAME,
  TRANSACTION_ID,
  ROUTE_NAME,
  MOBILE_SYSTEM_TYPE,
  FNO_SEARCH_ENABLE,
  STICKY_ORDER,
  ORDERS_SORT,
  POSITIONS_SORT,
  LOCATION_DETAILS,
  HOLDINGS_SORT,
  INFO_CARD,
  TARGET_PLATFORM,
  INDEX_EXCHANGE_ID,
  EXCHANGE_INST_NAME,
  MARKET_CAP_TYPE,
  EDIS_BULK_AUTH_KEY,
  FIELDS_LABLES,
  REGENERATE_TOTP,
  FORGOT_PASSCODE,
  LOCATION_DATA,
  EXIT,
  IS_BASKET_ACCESSIBLE,
  MTF_INFO_LINK,
  MTF_NOT_ACTIVATED_STATUSES,
  MTF_STATUSES,
  MTF_NOT_ACTIVATED_STATUSES_ACCOUNTS,
  MTF_FEATURE_FLAG,
  OTP_DATA,
  PREPROD_URL,
  NO_BLOCKING_ERROR_CASES,
  BLOCKING_ERROR_CASES,
  RUPEE_SYMBOL,
  EMPTY_ARRAY,
  EMPTY_OBJECT,
};
