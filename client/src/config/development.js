export default {
  DATA: 'https://api-eq-dev.paytmmoney.com',
  ORDERS: 'https://api-eq-order-dev.paytmmoney.com',
  BASE: 'https://api-staging.paytmmoney.com',
  PLATFORM: 'https://pf-stg.paytmmoney.com',
  AUTH: 'https://login-stg.paytmmoney.com',
  CHARTS: 'https://equity-stg.paytmmoney.com',
  IFRAME_CHARTS: 'https://api-eq-dev.paytmmoney.com',
  PROFILE: 'https://api-preprod.paytmmoney.com',
  DATA_BROADCAST: 'wss://broadcast-dev.paytmmoney.com',
  ORDER_BROADCAST: 'wss://broadcast-order-dev.paytmmoney.com/bcorder?version=2',
  EDIS_RESET_PIN: 'https://mockedis.cdslindia.com/home/<USER>',
  EDIS_GENERATE_PIN: 'https://mockedis.cdslindia.com/home/<USER>',
  EQUITY_DOWNTIME: 'https://static.paytmmoney.com/data/v1/stage/frontend-config.json',
  FNO_SEARCH: 'https://static.paytmmoney.com/data/v1/staging/fno-dashboard-search-scrips.json',
  FNO_SEARCH_V2: 'https://static.paytmmoney.com/data/v2/staging/fno-dashboard-search-scrips.json',
  FREE_ETF: 'https://static.paytmmoney.com/data/v1/stage/etf-gift-tnc.json',
  CONSENT_REMINDER: 'https://static.paytmmoney.com/data/v1/stage/ConsentReminder.json',
  MARKET_INDICES_URL: 'https://static.paytmmoney.com/data/v1/staging/indices/fno-indices.json',
  FNO_MARKET_INDICES_URL: 'https://static.paytmmoney.com/data/v2/staging/trader_mode/options/options_config.json',
  PRICING_TOP_CARDS: 'https://static.paytmmoney.com/data/v2/stage/pricechange_new.json',
  BUYBACK_ENTRY: 'https://web-staging.paytmmoney.com/stocks/corporate-actions',
  HEAT_MAP_INDICES: 'https://static.paytmmoney.com/data/v1/staging/heatmap_indices.json',
  TRADING_VIEW: 'https://tradingview-dev.paytmmoney.com',
  S3_STATIC_CONTENTS_URL: 'https://static.paytmmoney.com/customer-support/content.json',
  ACCOUNT_CLOSURE_URL: 'https://static.paytmmoney.com/equity-download-center-prod/Account Closure Request Form new.pdf',
  MERCHANT_AUTH: 'https://pf-stg.paytmmoney.com/merchant-auth',
  DEVELOPER_URL: 'https://developer-stg.paytmmoney.com',
  WEB_CONFIG: 'https://static.paytmmoney.com/data/v1/staging/web-config.json',
  QR_CONFIG: 'https://static.paytmmoney.com/data/v1/stage/qr_onboarding.json',
  UPGRADE_STATIC_DATA: 'https://static.paytmmoney.com/data/v1/staging/subscription/upgrade_web.json',
  EQUITY_STOREFRONT: 'https://storefront.paytmmoney.com/v2/h/prod-pml-web-equity-dashboard',
  NUDGE_STOREFRONT: 'https://storefront.paytmmoney.com/v2/h/web-nudge',
  MARGIN_PLEDGE_STOREFRONT: 'https://storefront.paytmmoney.com/v2/h/margin_pledge',
  BASKET_ORDER_STOREFRONT: 'https://storefront.paytmmoney.com/v2/h/stage-equity-dashboard',
  BANK_ACCOUNT_STOREFRONT: 'https://storefront.paytmmoney.com/v2/h/pml-stage-new-nudges-demo-url',
  SIGNALS_CONFIG: {
    environment: 'staging',
    hmacKey: '09d8c4a437b347e390beaf392306933c',
  },
  ENV: 'staging',
  BROCASTING_ENABLED: {
    OPTION_CHAIN_ROW: true,
  },
  PROMO_ID: 316582,
  TRADING_VIEW_ID: 331195,
  WEB_APP_ID: '052208c4-22ad-43d7-afe7-06d038466781',
  AUTOPAY_FE_CODE: 'equity-stock-mt-subs',
  BASKET_ORDER_ID: 326457,
  PAYTM_DIGITAL_PROXY: 'https://pml-cst-gateway-prod-public.paytmmoney.com/oauth',
  ORDER_RESTRICTION_CONFIG: 'https://static.paytmmoney.com/data/v1/staging/orderrestrictions.json',
  SIG_URL: 'https://sig-staging.paytmmoney.com/v2/api/signals/batch',
  MKT_STATUS_CONFIG: 'https://static.paytmmoney.com/data/v1/staging/market_status.json',
  LOGGER: 'https://api-eq-stg.paytmmoney.com',
  DMS_PATH: 'https://pf-stg-internal.paytmmoney.com/dms/v1/getpdf',
  GRIEVANCE_DATA: 'https://static.paytmmoney.com/data/v1/staging/grievances.json',
};
