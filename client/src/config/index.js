import { isBrowser } from '@utils';
import staging from './staging';
import preprod from './preprod';
import production from './production';
import development from './development';

function getConfig() {
  switch (((isBrowser() && window.pmEnvironment) || '').toLowerCase()) {
    case 'development':
      return development;

    case 'staging':
      return staging;

    case 'preprod':
      return preprod;

    case 'production':
      return production;

    default:
      return staging;
  }
}

export default getConfig();
