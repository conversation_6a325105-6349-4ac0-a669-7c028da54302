import { LOCATION_DATA } from '@utils/enum';
import { getLocationDetails, getUserID } from '@utils';
import CONFIG from '../config';

export const BASE_URL = '/api';

export const REQUEST_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
};

const locationDetails = getLocationDetails();

export const ENDPOINTS = {
  WATCHLIST: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/marketwatch/api/v1/watchlist`,
    },
    CREATE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/marketwatch/api/v1/watchlist`,
    },
    RENAME: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}/rename`,
    },
    DELETE: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (id) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}`,
    },
    REORDER: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/reorder`,
    },
    REORDER_SECURITIES: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}/security/reorder`,
    },
    GET_STOCKS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}/security`,
    },
    GET_CA_DATA: {
      method: REQUEST_METHODS.GET,
      endpoint: (params) => `${CONFIG.DATA}/holdings/v1/get-ca-approved-data?startDate=${params.startDate}&endDate=${params.endDate}`,
    },
    ADD_STOCK: {
      method: REQUEST_METHODS.POST,
      endpoint: (id) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}/security`,
    },
    DELETE_STOCK: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (id, securityId) => `${CONFIG.DATA}/marketwatch/api/v1/watchlist/${id}/security/${securityId}`,
    },
    GET_RECENT_SEARCHED: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v2/recent/searched`,
    },
    GET_TRENDING_STOCKS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v2/popular`,
    },
  },
  AADHAAR_PAN_CONNECTION_STATUS: {
    method: REQUEST_METHODS.POST,
    endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/pan-aadhaar/status`,
  },
  SEARCH: {
    SUGGEST: {
      method: REQUEST_METHODS.GET,
      endpoint: (scope, advancedUser) => `${CONFIG.DATA}/data/v2/suggest?is-advanced-user=${advancedUser}&search-scope=${scope}`,
    },
  },
  ORDERS: {
    VIEW_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/orderbook`,
    },
    GET_TRADE_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/tradedetails`,
    },
  },
  POSITIONS: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/position`,
    },
    GET_MTF_POSITIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/interops/position/mtf`,
    },
    GET_ORDER_HISTORY: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/positiondetails`,
    },
    POST_CONVERT_ORDER: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.ORDERS}/order/txn/v2/convert/regular`,
    },
  },
  INTEROPERABILITY_POSITIONS: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/interops/position`,
    },
    GET_INTEROPERABILITY_ORDER_HISTORY: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.ORDERS}/order/info/v1/interops/positiondetails`,
    },
  },
  AUTH: {
    LOGIN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/login`,
      headers: {
        'content-type': ' application/x-www-form-urlencoded',
      },
    },
    TOKEN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/login/token`,
      headers: {
        'content-type': ' application/x-www-form-urlencoded',
      },
    },
    VALIDATE_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/login/otp/validate`,
    },
    RESEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/login/otp/resend`,
    },
    REGISTER: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/register`,
    },
    REGISTER_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/register/validate`,
    },
    LOGOUT: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.AUTH}/api/auth/logout`,
    },
    CHANGE_PASSWORD: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/change-password`,
    },
    MERCHANT_TOKEN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/generate-merchant-jwt`,
    },
  },
  MARKET_STATUS: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/exchange/mkt/v2/status`,
    },
  },
  APPS: {
    PERMISSION_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.MERCHANT_AUTH}/authorisation/v1/user/merchant/permissions`,
    },
    REVOKE_PERMISSION: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.MERCHANT_AUTH}/authorisation/v1/user/merchant/permissions/revoke`,
    },
    AUTHORIZE_PERMISSION_LIST: {
      method: REQUEST_METHODS.GET,
      isMerchant: true,
      endpoint: (requestToken) => `${CONFIG.MERCHANT_AUTH}/authorisation/v1/user/permissions?requestToken=${requestToken}`,
    },
    AUTHORIZE_PERMISSION: {
      method: REQUEST_METHODS.POST,
      isMerchant: true,
      endpoint: () => `${CONFIG.MERCHANT_AUTH}/authorisation/v1/user/merchant/permissions/authorise`,
    },
    GENERATE_ACCESS_TOKEN: {
      method: REQUEST_METHODS.POST,
      isMerchant: true,
      endpoint: () => `${CONFIG.MERCHANT_AUTH}/auth/v1/merchant/user/live/token`,
    },
    MERCHANT_PERMISSION: {
      method: REQUEST_METHODS.GET,
      endpoint: (requestToken) => `${CONFIG.MERCHANT_AUTH}/authorisation/v1/user/merchant/permissions/verified?requestToken=${requestToken}`,
    },
  },
  ORDER: {
    PLACE: {
      REGULAR: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v2/place/regular`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
      BRACKET: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/place/bracket`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
      COVER: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/place/cover`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },

    },
    CANCEL: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/cancel/regular`,
      headers: {
        ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
      },
    },
    EXIT: {
      COVER: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/exit/cover`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
      BRACKET: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/exit/bracket`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
    },
    MODIFY: {
      REGULAR: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v2/modify/regular`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
      BRACKET: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/modify/bracket`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
      COVER: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/modify/cover`,
        headers: {
          ...(locationDetails && { [LOCATION_DATA]: locationDetails }),
        },
      },
    },
    MARGIN_REQUIRED: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/margin/calculator/api/v1/order`,
    },
    CHARGES: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/charges/info`,
    },
    CHECKSIKKIMUSER: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/pf-kyc/v1/api/${userId}?q=ADDRESS`,
    },
  },
  COMPANY: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: (ids) => `${CONFIG.DATA}/data/v2/pml-details?ids=${ids}`,
    },
    GET_FUNDAMENTALS_TTM: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/data/v2/fundamentals-ttm?type=c&id=${id}`,
    },
    GET_SIMILAR_COMPANIES: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/data/v1/peers?id=${id}`,
    },
    GET_SIPS: {
      method: REQUEST_METHODS.GET,
      endpoint: (isin) => `${CONFIG.DATA}/sip/instruction/api/v1/view/${isin}`,
    },
    GET_LONG_DESCRIPTION: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/data/v1/desc?id=${id}`,
    },
    POST_P_CODE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/data/v2/pclose`,
    },
    GET_DETAILS_BY_ISIN: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/data/v2/isin-pml-details?isins=${id}`,
    },
    GET_DETAILS_BY_SYMBOL: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/data/v2/symbol-pml-details`,
    },
    GET_DETAILS_BY_SECURITY_ID: {
      method: REQUEST_METHODS.GET,
      endpoint: (secId, exchange, segment) => `${CONFIG.DATA}/data/v2/secid-x-pml-details?sec-id=${secId}&x=${exchange}&s=${segment}`,
    },
    GET_INVESTCARE_NUDGE: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/nudge-info/api/v3/scrip`,
    },
  },
  MTF: {
    GET_SCRIPS: {
      method: REQUEST_METHODS.GET,
      endpoint: (securityId, exchange) => `${CONFIG.DATA}/mtf/order/api/v1/scrip?scrip_id=${securityId}&segment=E&exchange=${exchange}`,
    },
    GET_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/mtf/order/api/v1/scrips/shortdesc`,
    },
    GET_IR_DATA: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/subProduct/MTF/investmentReadiness/details`,
    },
    MTF_PLEDGE: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.BASE}/aggr/mtf/v1/pledge/authorize-banner`,
    },
  },
  USER_EVENTS: {
    VIEW_SECURITY: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.DATA}/data/v1/user-event?stockid=${id}&type=v`,
    },
    SEARCH_SECURITY: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.DATA}/data/v1/user-event?stockid=${id}&type=s`,
    },
  },
  PRICE_ALERTS: {
    GET: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/data/v1/get-price-alert?id=${id}`,
    },
    SET: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id, tp, ltp) => `${CONFIG.DATA}/data/v1/price-alert?id=${id}&tp=${tp}&ltp=${ltp}`,
    },
    EDIT: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id, tp, ltp, doc_id) => `${CONFIG.DATA}/data/v1/price-alert?id=${id}&tp=${tp}&ltp=${ltp}&doc_id=${doc_id}`,
    },
    DELETE: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (doc_id) => `${CONFIG.DATA}/data/v1/delete-price-alert?doc_id=${doc_id}`,
    },
  },
  MARKET: {
    GET_RECENT_VIEWED: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v2/recent/viewed`,
    },
    MOVERS: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/marketmovers/api/v1/config`,
      },
      GET: {
        method: REQUEST_METHODS.GET,
        endpoint: (categoryId, count, scope, exchangeId, indexId) => `${CONFIG.DATA}/marketmovers/api/v1/${categoryId}?count=${count}&scope=${scope}&exchange=${exchangeId}&index=${indexId}`,
      },
    },
    LOW_HIGH_MOVERS: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/marketmovers/api/v2/config`,
      },
      GET: {
        method: REQUEST_METHODS.GET,
        endpoint: (categoryId, count, scope, exchangeId, indexId) => `${CONFIG.DATA}/marketmovers/api/v2/stocks?exchange=${exchangeId}&index=${indexId}&scope=${scope}&category=${categoryId}&count=${count}`,
      },
    },
    GET_INDICES: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v1/indices`,
    },
    ETFS_FILTER_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v2/etf-filters`,
    },
  },
  PROFILE: {
    GET_PRICE_ALERTS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v1/get-agg-price-alert`,
    },
    GET_PAST_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/order/info/past/v2/search`,
    },
    GET_PAST_TRADE_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/order/info/past/v1/tradedetails`,
    },
    GET_NOTIFICATION_PREFERENCES: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.BASE}/communication/v1/optout/preferences?product=EQUITY`,
    },
    PUT_NOTIFICATION_PREFERENCES: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.BASE}/communication/v1/optout/preferences?product=EQUITY`,
    },
    GET_USER_DATA: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.PROFILE}/pm/api/v1/users/${id}`,
    },
    IDENTIFY_SIKKIM_USERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.PLATFORM}/pf-kyc/v1/api/${id}?q=ADDRESS`,
    },
    GET_IR_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/user/${userId}/v4/readiness?product=MUTUAL_FUND,EQUITY,NPS`,
    },
    GET_KYC_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.PROFILE}/kyc/v3/user-detail/${id}?personal=true&panNumber=true`,
    },
    GET_PROFILE_IMAGE: {
      method: REQUEST_METHODS.GET,
      endpoint: (imageUrl) => imageUrl,
      headers: {
        accept: 'image/jpeg',
        'Content-Type': 'image/jpeg',
      },
      responseType: 'blob',
    },
    GET_READINESS_DATA: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.PLATFORM}/onboarding/v4/fe/user/${id}/product/EQUITY/investmentReadiness`,
    },
    GET_BACK_OFFICE_INFO: {
      method: REQUEST_METHODS.POST,
      endpoint: (id) => `${CONFIG.DATA}/backoffice/user/v1/aof/${id}`,
    },
    UPDATE_PROFILE: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.PROFILE}/pm/api/v1/web/users/${id}`,
      headers: {
        'content-type': ' application/x-www-form-urlencoded',
      },
    },
    UPDATE_KYC_EMAIL: {
      REQUEST_OTP: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.PLATFORM}/pf-kyc/v1/modify/requestOTP`,
      },
      VALIDATE_OTP: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.PLATFORM}/pf-kyc/v1/modify/validateOTP`,
      },
      SUBMIT_MODIFICATION_DETAILS: {
        method: REQUEST_METHODS.POST,
        endpoint: (userId) => `${CONFIG.PLATFORM}/pf-kyc/v1/modify/${userId}/submit`,
      },
      GET_MODIFICATION_STATUS: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId, type) => `${CONFIG.PLATFORM}/pf-kyc/v1/modify/${userId}/status?type=${type}`,
      },
    },
    MARGIN_PLEDGE: {
      GET_ALL: {
        method: REQUEST_METHODS.GET,
        endpoint: ({ pageSize, pageNumber }) => `${CONFIG.DATA}/margin/pledge/api/v1/requests?page_num=${pageNumber}&page_size=${pageSize}`,
      },
      PLACE: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.DATA}/margin/pledge/api/v1/place`,
      },
      GET_HOLDINGS: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/margin/pledge/api/v1/holdings`,
      },
      GET: {
        method: REQUEST_METHODS.GET,
        endpoint: (pledge_request_id) => `${CONFIG.DATA}/margin/pledge/api/v1/status/${pledge_request_id}`,
      },
      GET_PLEDGE_TIMINGS: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/margin/pledge/api/v1/timings`,
      },
      STOREFRONT_MARGIN_PLEDGE: {
        GET_PLEDGE_CONFIG: {
          method: REQUEST_METHODS.POST,
          endpoint: () => `${CONFIG.MARGIN_PLEDGE_STOREFRONT}`,
          shouldRemoveBasicHeaders: true,
          headers: ({ user_id: getUserID() }),
        },
      },
    },
    MARGIN_UNPLEDGE: {
      GET_ALL: {
        method: REQUEST_METHODS.GET,
        endpoint: ({ pageSize, pageNumber }) => `${CONFIG.DATA}/margin/pledge/api/v1/unpledge/requests?page_num=${pageNumber}&page_size=${pageSize}`,
      },
      PLACE: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.DATA}/margin/pledge/api/v1/unpledge/place`,
      },
      GET: {
        method: REQUEST_METHODS.GET,
        endpoint: (unpledge_request_id) => `${CONFIG.DATA}/margin/pledge/api/v1/unpledge/status/${unpledge_request_id}`,
      },
    },
    AUTOPAY: {
      GET_MANDATE_LIST: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId) => `${CONFIG.BASE}/mandate/api/v2/users/${userId}/otm-list`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
      BANK_ACCOUNT_LIST: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId) => `${CONFIG.BASE}/mandate/api/v2/otm/${userId}/mandate-reg-options`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
      INITIATE_SUBSCRIPTION: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.BASE}/mandate/api/v2/otm/initiate-subscription/paytmpg`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
      VALIDATE_VPA: {
        method: REQUEST_METHODS.GET,
        endpoint: (params) => `${CONFIG.BASE}/mandate/api/v2/user/${params.userId}/validate-vpa?vpa=${params.vpa}`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
        maskedUrl: (params) => `${CONFIG.BASE}/mandate/api/v2/user/${params.userId}/validate-vpa/XXXXX`,
      },
      PAUSE_RESUME_MANDATE: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.BASE}/mandate/api/v2/paytm-pg/pause-resume-subscription`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
      INITIATE_MANDATE_REQUEST: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.BASE}/mandate/api/v2/otm/initiate-mandate-request`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
      DELETE_MANDATE: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.BASE}/mandate/api/v2/paytm-pg/cancel-subscription`,
        headers: {
          'client-fe-code': CONFIG.AUTOPAY_FE_CODE,
        },
      },
    },
    GET_COMBINED_POPUP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/aggr/v1/combined-popup`,
    },
    PLATFORM: {
      GET_PLAN_ID: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId) => `${CONFIG.PLATFORM}/subscription/customer/${userId}/plan?productType=EQ`,
      },
      GET_PLAN_DETAILS: {
        method: REQUEST_METHODS.GET,
        endpoint: (planId) => `${CONFIG.PLATFORM}/subscription/plan-detail/${planId}`,
      },
      GET_SETTLEMENT_FREQUENCY: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId) => `${CONFIG.PLATFORM}/pf-kyc/v1/user-detail/${userId}?personal=true`,
      },
      GET_ESIGN_PDF: {
        method: REQUEST_METHODS.GET,
        endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/esign-forms`,
      },
      GET_ESIGN_PDF_DATA: {
        method: REQUEST_METHODS.GET,
        endpoint: (pdfUrl) => pdfUrl,
        headers: {
          accept: 'application/json, text/plain, */*',
        },
        responseType: 'blob',
      },
    },
  },
  BANK_DETAILS: {
    VERIFY_IFSC: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/pm/api/v1/bankinfo`,
    },
    GET_LIST_OF_BANK: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.BASE}/pm/api/v1/banks`,
    },
    GET_BANK_BY_SEARCH: {
      method: REQUEST_METHODS.GET,
      endpoint: (bankName, searchTxt) => `${CONFIG.PLATFORM}/userprofile/v1/bank/list?bankName=${bankName}&searchText=${searchTxt}`,
    },
    VERIFY_BANK_ACCOUNT: (timeout = 0, urlConfig) => ({
      method: REQUEST_METHODS.GET,
      endpoint: (userId, accountId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts/${accountId}/validateByImps${urlConfig}`,
      timeout,
    }),
    CREATE_BANK_ACCOUNT: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts?product=EQUITY`,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    },
    UPLOAD_BANKPROOF: {
      method: REQUEST_METHODS.PUT,
      endpoint: (userId) => `${CONFIG.BASE}/pm/api/v3/users/${userId}/bank-accounts?product=EQUITY`,
      headers: { 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW' },
    },
    UPDATE_IFSC: {
      method: REQUEST_METHODS.PUT,
      endpoint: (userId, accountId, ifsc) => `${CONFIG.BASE}/pm/api/v1/users/${userId}/bank-accounts/${accountId}/update-ifsc?newIfsc=${ifsc}`,
      headers: { 'Content-Type': 'multipart/form-data' },
      maskedUrl: (userId, accountId) => `${CONFIG.BASE}/pm/api/v1/users/${userId}/bank-accounts/${accountId}/update-ifsc/XXXXXX`,
    },
    CHECK_MISMATCH: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId, accountId) => `${CONFIG.BASE}/pm/api/v1/users/${userId}/bank-accounts/${accountId}/validate-proof`,
      headers: { 'Content-Type': 'multipart/form-data' },
    },
    DELETE_BANK_ACCOUNT: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (userId, bankId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts/${bankId}?product=EQUITY&dryRun=false`,
    },
    CHANGE_DEFAULT_BANK_ACCOUNT: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId, bankId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts/${bankId}/change-default?product=EQUITY`,
    },
    VERIFY_PENNYDROP_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId, accountId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts/${accountId}`,
    },
    STOREFRONT_BANK_ACCOUNT_LINKED: {
      BANK_ACCOUNT_LINKED: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.BANK_ACCOUNT_STOREFRONT}?client=html5`,
        shouldRemoveBasicHeaders: true,
        headers: ({ user_id: getUserID() }),
      },
    },
  },
  STATEMENTS: {
    GET_MONTHLY_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: (month, year) => `${CONFIG.DATA}/backoffice/ext/statements/v1/monthlyGlobalStatements/send/email?month=${month}&year=${year}`,
    },
    DOWNLOAD_MONTHLY_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: (month, year) => `${CONFIG.DATA}/backoffice/ext/statements/v1/monthlyGlobalStatements?month=${month}&year=${year}`,
    },
    DOWNLOAD_MF_MONTHLY_INTEREST_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: (month, year) => `${CONFIG.DATA}/backoffice/ext/statements/v1/mtf/monthly/interest/download?month=${month}&year=${year}`,
    },
    MONTHLY_STATEMENTS_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/backoffice/ext/statements/v1/get/monthlystatement/latestmonth`,
    },
    GET_MONTHLY_MF_INTEREST_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: (month, year) => `${CONFIG.DATA}/backoffice/ext/statements/v1/mtf/monthly/interest/send/email?month=${month}&year=${year}`,
    },
    GET_CONTRACTS_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end, segment) => `${CONFIG.DATA}/backoffice/ext/statements/v2/trade/dates?start_date=${start}&end_date=${end}&segment=${segment}`,
    },
    GET_AUCTIONBILL_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end) => `${CONFIG.DATA}/backoffice/ext/statements/v1/auction/trade/dates?start_date=${start}&end_date=${end}`,
    },
    SEND_MTF_INTEREST_EMAIL_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end) => `${CONFIG.DATA}/backoffice/ext/statements/v1/mtf/interest/email?from_date=${start}&to_date=${end}`,
    },
    SEND_LEDGER_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end) => `${CONFIG.DATA}/backoffice/ext/statements/v1/ledger/email?fromDate=${start}&toDate=${end}`,
    },
    SEND_MTF_LEDGER_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end, status) => `${CONFIG.DATA}/backoffice/ext/statements/v1/ledger/mtf/email?from_date=${start}&to_date=${end}&obir_status=${status}`,
    },
    PL_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/reports/external/v3/pl/generate-identifier`,
    },
    EMAIL_PL_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/reports/external/v3/pl/email/report`,
    },
    TAX_PL_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/reports/pl/tax/v1/generate-tax-report`,
    },
    POLL_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/reports/external/v3/pl/poll?id=${id}`,
    },
    GET_WEEKLY_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end) => `${CONFIG.DATA}/backoffice/ext/statements/v2/weekly/historical/weeks?start_date=${start}&end_date=${end}`,
    },
    EMAIL_TRADE_BOOK_STATEMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end, segment) => `${CONFIG.DATA}/backoffice/ext/statements/v1/tradebook/email?from_date=${start}&to_date=${end}&segment=${segment}`,
    },
    ANNUAL_LEDGER_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/backoffice/ext/statements/v3/ledger/send/email`,
    },
    ANNUAL_GLOBAL_TRANSACTION_STATEMENTS_DOWNLOAD: {
      method: REQUEST_METHODS.GET,
      endpoint: (year) => `${CONFIG.DATA}/backoffice/ext/statements/agts/download?year=${year}`,
    },
    ANNUAL_GLOBAL_TRANSACTION_STATEMENTS_EMAIL: {
      method: REQUEST_METHODS.POST,
      endpoint: (year) => `${CONFIG.DATA}/backoffice/ext/statements/agts/send/email?year=${year}`,
    },
    LOSS_GAIN_TAX_STATEMENTS: {
      method: REQUEST_METHODS.POST,
      endpoint: (segment) => `${CONFIG.DATA}/reports/external/v1/tax-harvest/email-tax-harvest-report?reportType=${segment}`,
    },
  },
  MAP_MY_INDIA: {
    LOCATION_DETAILS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/pf-masterdata/fe/v1/map/geolocation`,
    },
  },
  FUNDS: {
    GET_ORDERPAD_SUMMARY: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/orderpad/funds/summary`,
    },
    GET_SUMMARY: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fms/api/v5/funds/summary?iswithdrawalBalanceRequired=true`,
    },
    GET_PROMPT_MSG: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/funds/qs/msg`,
    },
    GET_PAYMENT_SUGGESTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fms/api/v4/funds/txn/suggestions`,
    },
    MAKE_PAYMENT: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v4/make-payment`,
    },
    MAKE_PAYMENT_V5: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v5/make-payment`,
    },
    GET_TRANSACTION_HISTORY: {
      method: REQUEST_METHODS.GET,
      endpoint: (txnType, pageSize, pageNumber, status) => `${CONFIG.DATA}/fms/api/v3/funds/txns?txn_types=${txnType}&page_size=${pageSize}&page_number=${pageNumber}${status ? `&txn_status=${status}` : ''}`,
    },
    GET_TRANSACTION_INFO: {
      method: REQUEST_METHODS.GET,
      endpoint: (transactionId) => `${CONFIG.DATA}/fms/api/v2/funds/txn?transaction_id=${transactionId}&timeline=true`,
    },
    GET_BANK_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/bank-accounts?product=EQUITY`,
    },
    WITHDRAW_FUNDS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/funds/payout/initiate`,
    },
    INITIATE_PAYMENT: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/fms/api/v2/funds/payin/initiate`,
    },
    GET_PPB_BALANCE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v3/balance-info`,
    },
    GET_PAYMENT_GATEWAY: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v4/pg-selection`,
    },
    GET_PAYMENT_OPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (txnId) => `${CONFIG.BASE}/payments/api/v6/payment-option/${txnId}`,
    },
    GET_PAYOUT_BANK_OPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.BASE}/pm/api/v2/users/${userId}/equity/payout/bank-accounts`,
    },
    GET_SUBSCRIPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/subscription/v2/subscription-data/list/${userId}`,
    },
    GET_LEDGER_HISTORY: {
      method: REQUEST_METHODS.GET,
      endpoint: (start, end) => `${CONFIG.DATA}/backoffice/ext/statements/v2/ledger/transactions?fromDate=${start}&toDate=${end}`,
    },
    GET_MTF_HISTORY: {
      method: REQUEST_METHODS.GET,
      endpoint: (status, isMtfEnabled) => `${CONFIG.DATA}/backoffice/ext/statements/v1/ledger/mtf/transactions?obir_status=${status}&is_mtf_enabled=${isMtfEnabled}`,
    },
    VALIDATE_VPA: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v4/validate-vpa`,
    },
    PROCESS_VPA_PAYMENT: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v4/process-payment`,
    },
    GET_DETAILED_SUMMARY: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/funds/summary/detailed`,
    },
    CANCEL_PAYOUTS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/fms/api/v1/funds/payout/cancel`,
    },
    GET_BANK_TRANSFER_ELIGIBILITY: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/van`,
    },
    CREATE_VAN_DETAILS: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/van`,
    },
    VERIFY_COUPON: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/coupon/validate`,
    },
    APPLY_COUPON: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/coupon/apply`,
    },
    CHANGE_PAYMENT_PLAN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/subscription/v2/change-plan/pg`,
    },
    CHECK_FIRST_PAYIN: {
      method: REQUEST_METHODS.GET,
      endpoint: (txnId) => `${CONFIG.DATA}/fms/api/v1/user/payin/isfirsttxn?transaction_id=${txnId}`,
    },
  },
  INDEX: {
    GET_SIMILAR_INDICES: {
      method: REQUEST_METHODS.GET,
      endpoint: (indexId) => `${CONFIG.DATA}/data/v1/index-peers?id=${indexId}`,
    },
    GAINERS_LOSERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (indexId) => `${CONFIG.DATA}/marketmovers/api/v1/index/gainersandlosers/list?securities=true&index=${indexId}`,
    },
    GAINERS_LOSERS_RANGE: {
      method: REQUEST_METHODS.GET,
      endpoint: (indexId) => `${CONFIG.DATA}/marketmovers/api/v1/index/gainersandlosers/range?index=${indexId}`,
    },
  },
  PASS_CODE: {
    IS_PASS_CODE_EXISTS: {
      METHOD: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/passcode/v2/user/${userId}/passcode`,
    },
    CREATE_PASSCODE: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/passcode/v2/user/${userId}/passcode`,
    },
    RESET_PASSCODE: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/passcode/v2/user/${userId}/passcode/reset`,
    },
    RESET_PASSCODE_IR: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/passcode/v2/user/${userId}/passcode/reset`,
    },
    VERIFY_PASSCODE: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/passcode/v2/user/${userId}/validate`,
    },
    VERIFY_MERCHANT_PASSCODE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => ` ${CONFIG.MERCHANT_AUTH}/merchant/v1/token/request/2fa/valid`,
    },
    GET_PUBLIC_KEY: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.PLATFORM}/passcode/getPublicKey`,
    },
    GENERATE_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.ORDERS}/order/txn/v1/generate/otp`,
    },
    SEND_SET_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/passcode/user/${userId}/otp/send`,
    },
    VERIFY_SET_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/2fa/passcode/user/otp/verify`,
    },
    RESEND_SET_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/passcode/user/${userId}/otp/resend`,
    },
  },
  SIP: {
    GET_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/sip/instruction/api/v1/view`,
    },
    CREATE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/sip/instruction/api/v1/place`,
    },
    UPDATE: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.DATA}/sip/instruction/api/v1/modify`,
    },
    DELETE: {
      method: REQUEST_METHODS.PUT,
      endpoint: (sipId) => `${CONFIG.DATA}/sip/instruction/api/v1/disable/sip/${sipId}`,
    },
    GET_UPCOMING: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/sip/instruction/api/v1/upcoming`,
    },
    CHANGE_AUTOPAY_STATUS: {
      method: REQUEST_METHODS.PUT,
      endpoint: (sipId, status) => `${CONFIG.DATA}/sip/instruction/api/v1/update/status/${sipId}/${status}`,
    },
  },
  BASKET_ORDERS: {
    GET_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/basket-order/api/v1/baskets`,
    },
    CREATE_BASKET: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/basket-order/api/v1/create/basket`,
    },
    DELETE_BASKET: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/basket-order/api/v1/delete/baskets`,
    },
    MODIFY_BASKET: {
      method: REQUEST_METHODS.PUT,
      endpoint: (basket_id) => `${CONFIG.DATA}/basket-order/api/v1/modify/basket/${basket_id}`,
    },
    GET_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (basketId) => `${CONFIG.DATA}/basket-order/api/v1/basket/${basketId}`,
    },
    ADD_ORDER: {
      method: REQUEST_METHODS.POST,
      endpoint: (basketId) => `${CONFIG.DATA}/basket-order/api/v1/add/order/${basketId}`,
    },
    DELETE_ORDER: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (basket_id, order_id) => `${CONFIG.DATA}/basket-order/api/v1/delete/order/${basket_id}/${order_id}`,
    },
    MODIFY_ORDER: {
      method: REQUEST_METHODS.PUT,
      endpoint: (basket_id, order_id) => `${CONFIG.DATA}/basket-order/api/v1/modify/order/${basket_id}/${order_id}`,
    },
  },
  HOLDINGS: {
    ALL: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/holdings/v1/get-user-holdings-data`,
    },
    GET_IDENTIFIER: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/reports/external/v1/inapp-pl/generate-identifier`,
    },
    GET_POLLING_DATA: {
      method: REQUEST_METHODS.GET,
      endpoint: (identifier) => `${CONFIG.DATA}/reports/external/v1/inapp-pl/poll?id=${identifier}`,
    },

    GET_TRANSACTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (isin) => `${CONFIG.DATA}/holdings/v1/get-holdings-transaction-details?isin=${isin}`,
    },
    WEALTHDESK: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/wb/api/v1/partner/client/holdings?partner_id=1`,
    },
    OFFMARKET: {
      GET_TRADES_IN_PROGRESS: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/holdings/v1/trades-in-progress`,
      },
      UPDATE_OFFMARKET_TRADES: {
        method: REQUEST_METHODS.POST,
        endpoint: () => `${CONFIG.DATA}/holdings/v2/off-market-trades`,
      },
    },
    PORTFOLIO_INSIGHTS_CHARTS: {
      method: REQUEST_METHODS.GET,
      endpoint: (theme) => `${CONFIG.BASE}/charts/equity/portfolio/v1?theme=${theme}&range=1m`,
    },
    PORTFOLIO_INSIGHTS_CURRENT_INVESTED_VALUE: {
      method: REQUEST_METHODS.GET,
      endpoint: (fromDate, toDate) => `${CONFIG.DATA}/reports/external/v1/charts/data-points?from=${fromDate}&to=${toDate}`,
    },
  },
  HOME: {
    GET_CHART_IMAGE: {
      method: REQUEST_METHODS.POST,
      endpoint: (theme) => `${CONFIG.CHARTS}/ssr-charts/price?format=png&mode=${theme}`,
    },
    GET_MARKET_MOVERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (categoryId, count, scope) => `${CONFIG.DATA}/marketmovers/api/v1/home/<USER>
    },
    GET_ETFS_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/data/v2/scrips-list`,
    },
    GET_LISTED_IPO: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/listed-ipos`,
    },
    GET_TOP_TRADED_SCRIPS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/scrips/info/v1/topscrips`,
    },
    GET_TNC_ACCEPTANCE: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/tnc-accept`,

    },
    GET_TNC_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId, types) => `${CONFIG.PLATFORM}/userprofile/v2/user/${userId}/tnc-accept/verify?tncTypes=${types}`,
    },
    GET_CALL_US_ENABLED: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.BASE}/aggr/home/<USER>/combined-dashboard`,
    },
    GET_CALL_US_DETAILS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PAYTM_DIGITAL_PROXY}/crm/api/v1/custom/callUs?appClient=PAYTMMONEY`,
    },
  },
  LOG_ERROR: {
    method: REQUEST_METHODS.POST,
    endpoint: () => `${CONFIG.LOGGER}/logger/log`,
    headers: ({ auth2: 'VM1jiFAqAO48l3qZgSZH' }),
  },
  PRICING: {
    GET_TARIFFS: {
      method: REQUEST_METHODS.GET,
      endpoint: (platform) => `${CONFIG.PLATFORM}/platform/tariff/v1/brokerage-calculator-tariff?platform=${platform}`,
    },
    GET_TOP_TARIFFS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.PLATFORM}/platform/tariff/v1/brokerage-calculator-top-tariffs`,
    },
  },
  ADVISORY: {
    SEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/pm/api/v1/advisory/subscription/sendotp`,
    },
    VERIFY: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/communication/v1/advisory/subscription/verify`,
    },
  },
  MARGIN_CALCULATOR: {
    ADD_SCRIPS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/margin/calculator/api/v1/scrips`,
    },
  },
  BROKERAGE_CALCULATOR: {
    GET_STAMP_CHARGE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/platform/tariff/v1/stampduty/get`,
    },
    GET_TARIFFS: {
      method: REQUEST_METHODS.GET,
      endpoint: (platform) => `${CONFIG.PLATFORM}/platform/tariff/v2/brokerage-calculator-exchg-tariff?platform=${platform}`,
    },
  },
  EDIS_AUTH: {
    GENERATE_TPIN: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/edis/authorization/api/v1/generate/tpin`,
    },
    VALIDATE_TPIN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/edis/authorization/api/v3/validate`,
    },
    EDIS_REVOKE_VALIDATE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/edis/authorization/api/v1/revoke/validate`,
    },
    UPDATE_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/edis/authorization/api/v2/status/${id}`,
    },
    EDIS_AUTHORISATION_VIA_LINK: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/edis/authorization/api/v1/collection/validate`,
    },
  },
  SLEEK_CARD: {
    method: REQUEST_METHODS.GET,
    endpoint: () => `${CONFIG.BASE}/aggr/equity/v1/popup`,
  },
  MTF_AGGR: {
    method: REQUEST_METHODS.GET,
    endpoint: () => `${CONFIG.BASE}/aggr/equity/home/<USER>/dashboard?type=investor`,
  },
  ESIGN_QRCODE: {
    method: REQUEST_METHODS.GET,
    endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/esign/user/${userId}/esignCode`,
  },
  IPO: {
    GET_CURRENT_IPO: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/ipo-current`,
    },
    GET_UPCOMING_IPO: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/ipo-upcoming`,
    },
    GET_PAST_IPO: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/ipo-past`,
    },
    IPO_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/ipo/v1/ipo-details?ids=${id}`,
    },
    GET_IPO_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (page, filterSelection) => `${CONFIG.DATA}/ipo/v1/ipo-orders?page=${page}&app-status=${filterSelection}`,
    },
    GET_BANK_IPO_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (page) => `${CONFIG.DATA}/ipo/v1/bank/ipo-orders?page=${page}`,
    },
    GET_RETAIL_FILTER_OPTION: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/app-statuses`,
    },
    IPO_ORDER_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (appl_id) => `${CONFIG.DATA}/ipo/v1/order-details?ids=${appl_id}&next_steps=true&data=ALL`,
    },
    IPO_BANK_ORDER_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (appl_id) => `${CONFIG.DATA}/ipo/v1/bank/order-details?ids=${appl_id}&next_steps=true&data=ALL`,
    },
    IPO_APPLICATION_OPEN: {
      method: REQUEST_METHODS.GET,
      endpoint: (ipoId, category) => `${CONFIG.DATA}/ipo/v1/open-application?category=${category}&id=${ipoId}`,
    },
    IPO_APPLY: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/ipo-apply`,
    },
    IPO_APPLY_HNI_OR_EMPLOYEE: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/bank/ipo-apply`,
    },
    IPO_EDIT_APPLICATION: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/ipo-edit`,
    },
    IPO_CANCEL_HNI_APPLICATION: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/bank/ipo-cancel`,
    },
    GET_UPI_HANDLE: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v2/upi-handles`,
    },
    GET_BANK_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/ipo/v1/bank/banks-list`,
    },
    GET_DETAILS_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: (bankid) => `${CONFIG.DATA}/ipo/v1/bank/user/bank-accounts?ipo_bank_id=${bankid}`,
    },
  },
  SGB: {
    GET_CURRENT_SGB: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/sgb/v1/current-sgb`,
    },
    GET_UPCOMING_SGB: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/sgb/v1/upcoming-sgb`,
    },
    GET_PAST_SGB: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/sgb/v1/past-sgb`,
    },
    SGB_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/sgb/v1/sgb-details?sgb_ids=${id}`,
    },
    GET_SGB_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (page) => `${CONFIG.DATA}/sgb/v1/all-orders?page=${page}`,
    },
    APPLY: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/sgb/v1/apply-sgb`,
    },
    ORDER_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (ref_nos) => `${CONFIG.DATA}/sgb/v1/order-details?ref_nos=${ref_nos}&next_steps=true`,
    },
    RELATED_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: (sgb_id) => `${CONFIG.DATA}/sgb/v1/sgb-orders?sgb_id=${sgb_id}`,
    },
    CANCEL: {
      method: REQUEST_METHODS.PUT,
      endpoint: () => `${CONFIG.DATA}/sgb/v1/cancel-sgb`,
    },
    SGB_APPLICATION_OPEN: {
      method: REQUEST_METHODS.GET,
      endpoint: (id, category) => `${CONFIG.DATA}/sgb/v1/open-application?sgb_id=${id}&category=${category}`,
    },
  },
  TIMER: {
    method: REQUEST_METHODS.GET,
    endpoint: () => `${CONFIG.DATA}/exchange/alert/api/timer`,
  },
  FNO: {
    GET_INVESTMENTS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/investment/dashboard`,
    },
    GET_FII_DII: {
      method: REQUEST_METHODS.GET,
      endpoint: (count, investmentCategory, period, tradeCategory) => `${CONFIG.DATA}/fno/dashboard/api/v1/investment/inst-wise?count=${count}&inv_cat=${investmentCategory}&period=${period}&trade_cat=${tradeCategory}`,
    },
    GLOBAL_INDICES: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/global-indices`,
    },
    FNO_OPTIONS_CONFIG_V1: {
      method: REQUEST_METHODS.GET,
      endpoint: (symbol) => `${CONFIG.DATA}/fno/dashboard/api/v1/option-chain/config?symbol=${symbol}`,
    },
    FNO_OPTIONS_CONFIG: {
      method: REQUEST_METHODS.GET,
      endpoint: (symbol, exchange) => `${CONFIG.DATA}/fno/dashboard/api/v2/option-chain/config?symbol=${symbol}&exchange=${exchange}`,
    },
    FNO_OPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (symbol, expiry, type, exchange) => `${CONFIG.DATA}/fno/dashboard/api/v2/option-chain?symbol=${symbol}&expiry=${expiry}&type=${type}&exchange=${exchange}`,
    },
    FUTURE_CONTRACT: {
      method: REQUEST_METHODS.GET,
      endpoint: (symbol, exchange) => `${CONFIG.DATA}/fno/dashboard/api/v2/future-contracts?symbol=${symbol}&exchange=${exchange}`,
    },
    FNO_NEWS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/fno-news`,
    },
    FNO_CATEGORY_NEWS: {
      method: REQUEST_METHODS.GET,
      endpoint: ({ categories, page_no, page_size }) => `${CONFIG.DATA}/fno/dashboard/api/v1/fno-news/inc?news_category=${categories}&page_no=${page_no}&size=${page_size}`,
    },
    HEAT_MAPS: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/heat-map/config`,
      },
      SECTOR: {
        method: REQUEST_METHODS.GET,
        endpoint: (sector) => `${CONFIG.DATA}/fno/dashboard/api/v1/heat-map/sector?sector=${sector}`,
      },
      INDEX: {
        method: REQUEST_METHODS.GET,
        endpoint: (index) => `${CONFIG.DATA}/fno/dashboard/api/v1/heat-map/index?index=${index}`,
      },
      OI_CHARTS: {
        method: REQUEST_METHODS.GET,
        endpoint: (category, symbol, expiry, spotPrice, theme) => `${CONFIG.BASE}/charts/fno/v2/oi?category=${category}&symbol=${symbol}&expiry=${expiry}&spotPrice=${spotPrice}&theme=${theme}&tooltipInteraction=true`,
      },
      FNO_OI: {
        method: REQUEST_METHODS.GET,
        endpoint: (symbol, expiry) => `${CONFIG.DATA}/fno/dashboard/api/v1/oi?symbol=${symbol}&expiry=${expiry}`,
      },
    },
    FNO_MOVERS: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/movers/config`,
      },
      GET: {
        method: REQUEST_METHODS.GET,
        endpoint: ({
          categoryId, count, scope, sector, type,
        }) => `${CONFIG.DATA}/fno/dashboard/api/v1/movers/sector?category=${categoryId}&count=${count}&scope=${scope}&sector=${sector}&type=${type}`,
      },
    },

    FNO_MARKET_RADAR: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/radar/config`,
      },
      SUMMARY: {
        method: REQUEST_METHODS.GET,
        endpoint: ({
          interval, type,
        }) => `${CONFIG.DATA}/fno/dashboard/api/v1/radar/summary?interval=${interval}&type=${type}`,
      },
    },
    FNO_SCANNER: {
      CONFIG: {
        method: REQUEST_METHODS.GET,
        endpoint: () => `${CONFIG.DATA}/fno/dashboard/api/v1/scanner/config`,
      },
      GET_OPTIONS_DATA: {
        method: REQUEST_METHODS.GET,
        endpoint: ({ category }) => `${CONFIG.DATA}/fno/dashboard/api/v1/scanner/options?category=${category}`,
      },
      GET_FUTURES_DATA: {
        method: REQUEST_METHODS.GET,
        endpoint: ({ categoryId }) => `${CONFIG.DATA}/fno/dashboard/api/v1/scanner/futures?category=${categoryId}&expiry_type=NEAR`,
      },
    },
    FNO_TRENDS_INDICATOR: {
      method: REQUEST_METHODS.GET,
      endpoint: (symbol, exchange) => `${CONFIG.DATA}/fno/dashboard/api/v2/futures/builtup?symbol=${symbol}&exchange=${exchange}&size=1`,
    },
  },
  UPGRADE: {
    GET_COMPUTE_CHARGES: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/subscription/compute-charges`,
    },
    NEW_SUBSCRRIPTION_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (customerId, productType) => `${CONFIG.PLATFORM}/subscription/customer/${customerId}/plan?productType=${productType}`,
    },
    GET_USER_PLAN_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (params) => `${CONFIG.PLATFORM}/subscription/v4/plan-change-detail/${params.fromplanId}/${params.type}`,
    },
    DEBIT_PG_API: {
      method: REQUEST_METHODS.POST,
      endpoint: (params) => `${CONFIG.PLATFORM}/subscription/v3/debit/pg?customerId=${params.customerId}&planId=${params.planId}&paymentType=${params.paymentType}`,
    },
    DEBIT_LEDGER_API: {
      method: REQUEST_METHODS.POST,
      endpoint: (params) => `${CONFIG.PLATFORM}/subscription/v3/debit/ledger?customerId=${params.customerId}&planId=${params.planId}&paymentType=${params.paymentType}`,
    },
    CHANGE_CUSTOMER_PLAN_API: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/subscription/change-plan`,
    },
    GET_SUBSCRIPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (customerId) => `${CONFIG.PLATFORM}/subscription/v4/get-current-plan/${customerId}`,
    },
    GET_SUBSCRIPTION_CHARGES: {
      method: REQUEST_METHODS.GET,
      endpoint: (body) => `${CONFIG.PLATFORM}/subscription/v3/subscription-data/list/${body.customerId}?paymentType=${body.paymentType}`,
    },
    POST_LEDGER_CHARGES: {
      method: REQUEST_METHODS.POST,
      endpoint: (body) => `${CONFIG.PLATFORM}/subscription/v2/debit/ledger?paymentType=${body.paymentType}&customerId=${body.customerId}`,
    },
    GET_PLAN_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: (customerId) => `${CONFIG.PLATFORM}/subscription/v2/plan/list?customerId=${customerId}`,
    },
    GET_PLAN_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (planId) => `${CONFIG.PLATFORM}/subscription/v4/plan/${planId}`,
    },
    INITIATE_PAYMENT: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/subscription/v2/upgrade/pg`,
    },
    INITIATE_PAYMENT_TRAIL: {
      method: REQUEST_METHODS.POST,
      endpoint: (paymentType) => `${CONFIG.PLATFORM}/subscription/v2/upgrade/pg?paymentType=${paymentType}`,
    },
    GET_PAYMENT_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (transactionId) => `${CONFIG.PLATFORM}/subscription/v2/query-payment-status/${transactionId}`,
    },
    GET_PLAN_CHANGE: {
      method: REQUEST_METHODS.GET,
      endpoint: ({ customerId, newPlanId }) => `${CONFIG.PLATFORM}/subscription/v3/plan-change-details?customerId=${customerId}&newPlanId=${newPlanId}`,
    },
    CHANGE_PLAN: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/subscription/v3/change-plan`,
    },
    TRANSACTION_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (body) => `${CONFIG.PLATFORM}/subscription/v3/query-payments/${body.txnId}?paymentType=${body.paymentType}`,
    },
  },
  PAYMENT: {
    GET_NB_PAYMENT_OPTIONS: {
      method: REQUEST_METHODS.GET,
      endpoint: (paymentsTransactionId) => `${CONFIG.BASE}/payments/api/v5/nb-payment-option/${paymentsTransactionId}`,
    },
    GET_BIN_DETAILS: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASE}/payments/api/v4/bin-details`,
    },
    PROCESS_TRANSACTION: {
      method: REQUEST_METHODS.POST,
      endpoint: ({ actionUrl, mid, orderId }) => `${actionUrl}?mid=${mid}&orderId=${orderId}`,
      shouldRemoveBasicHeaders: true,
    },
    CANCEL: {
      method: REQUEST_METHODS.PUT,
      endpoint: (paymentsTransactionId) => `${CONFIG.BASE}/payments/api/cancel-transaction/${paymentsTransactionId}`,
    },
  },
  GTT: {
    ALL_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/gtt-order/api/v2/gtt`,
    },
    AGGREGATED_ORDERS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/gtt-order/api/v1/gtt/aggregate`,
    },
    ORDERS_BY_PML_ID: {
      method: REQUEST_METHODS.GET,
      endpoint: (pmlId) => `${CONFIG.DATA}/gtt-order/api/v2/gtt?pml-id=${pmlId}`,
    },
    GET_EXPIRY_DATE: {
      method: REQUEST_METHODS.GET,
      endpoint: (pmlId) => `${CONFIG.DATA}/gtt-order/api/v1/gtt/expiry-date?pml-id=${pmlId}`,
    },
    CREATE_ORDER: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/gtt-order/api/v2/gtt`,
    },
    UPDATE_ORDER: {
      method: REQUEST_METHODS.PUT,
      endpoint: (id) => `${CONFIG.DATA}/gtt-order/api/v2/gtt/${id}`,
    },
    DELETE_ORDER: {
      method: REQUEST_METHODS.DELETE,
      endpoint: (id) => `${CONFIG.DATA}/gtt-order/api/v1/gtt/${id}`,
    },
  },
  ONBOARDING: {
    POST_STORE_PLATFORM: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/platform`,
    },
    POST_OPT_IN: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v5/user/${userId}/product/EQUITY/journey/optin`,
    },
  },
  CURRENT_TIME: {
    method: REQUEST_METHODS.GET,
    endpoint: () => `${window.location.origin}/stocks/time`,
  },
  ALLOWED_USERS: {
    VALIDATE_USER: {
      method: REQUEST_METHODS.GET,
      endpoint: (id) => `${CONFIG.DATA}/equity-charts/api/allowedusers/${id}`,
    },
  },
  APPSFLYER: {
    AF_EVENT: {
      method: REQUEST_METHODS.PUT,
      endpoint: (userId) => `${CONFIG.PLATFORM}/userprofile/v1/user/${userId}/web/af/events`,
    },
  },
  STOREFRONT: {
    EQUITY_DASHBOARD: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.EQUITY_STOREFRONT}`,
      shouldRemoveBasicHeaders: true,
      headers: ({ user_id: getUserID() }),
    },
    WEB_NUDGE: {
      method: REQUEST_METHODS.POST,
      endpoint: (tag) => `${CONFIG.NUDGE_STOREFRONT}?tag=${tag}`,
      shouldRemoveBasicHeaders: true,
      headers: ({ user_id: getUserID() }),
    },
  },
  STOREFRONT_BASKET_ORDER: {
    BASKET_ORDER: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.BASKET_ORDER_STOREFRONT}`,
      shouldRemoveBasicHeaders: true,
      headers: ({ user_id: getUserID() }),
    },
  },
  ACCOUNT_CLOSURE: {
    EXISTING_REQUEST: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online`,
    },
    USER_PROFILE_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.BASE}/kyc/v1/user-detail/${userId}?personal=true&panNumber=true&address=true`,
    },
    USER_ACCOUNT_DETAILS: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.DATA}/backoffice/user/v1/aof/${userId}`,
    },
    USER_PORTFOLIO_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/fms/api/v5/funds/summary`,
    },
    USER_INVESTMENT_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => `${CONFIG.DATA}/holdings/v1/get-holdings-value`,
    },
    REQUEST_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/communication/otp/v1/requestotp`,
    },
    VALIDATE_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/communication/otp/v2/validateotp`,
    },
    SUBMIT_CLOSURE_REQUEST: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online`,
      headers: {
        'content-type': 'multipart/form-data',
      },
    },
    DOWNLOAD_CLOSURE_FORM: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure-form`,
    },
  },
  ACCOUNT_CLOSURE_REVAMP: {
    SUBMIT_REASON: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online/closureReason`,
    },
    ESIGN: {
      method: REQUEST_METHODS.POST,
      endpoint: (params) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${params.userId}/product/EQUITY/closure/online/esign?lat=${params.latitude}&lng=${params.longitude}&docType=ACCOUNT_CLOSURE_FORM&source=WEB`,
    },
    SUBMIT_CMR: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online/destDemat/details`,
      headers: {
        'content-type': 'multipart/form-data',
      },
    },
    JOURNEY_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online/status`,
    },
    VERIFY_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online/otp/verify`,
    },
    SUBMIT_REQUEST: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/fe/user/${userId}/product/EQUITY/closure/online/`,
    },
    SUBMIT_ESIGN: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/onboarding/v1/user/${userId}/product/EQUITY/subProduct/ALL/journey/esign/submit?docType=ACCOUNT_CLOSURE_FORM`,
      headers: {
        'content-type': 'multipart/form-data',
      },
    },
    SIP_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => '/api/user/account-closure/getSipDetails',
    },
    MANDATE_DETAILS: {
      method: REQUEST_METHODS.GET,
      endpoint: () => '/api/user/account-closure/getMandateDetails',
    },
    GET_ESIGN_PDF: {
      method: REQUEST_METHODS.GET,
      endpoint: (url) => `/api/user/account-closure/getEsignPdf?url=${url}`,
      headers: {
        'Content-Type': 'application/pdf',
        responseType: 'blob',
      },
      responseType: 'blob',
    },

  },
  FILTER: {
    FILTER_LIST: {
      method: REQUEST_METHODS.GET,
      endpoint: (instrumentType) => `${CONFIG.DATA}/data/v3/scrips-list-filters?i=${instrumentType}`,
    },
    FILTERED_LIST_DATA: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.DATA}/data/v3/scrips-list`,
    },

  },
  TOTP: {
    TOTP_STATUS: {
      method: REQUEST_METHODS.GET,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/totp/user/${userId}`,
    },
    SEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/totp/user/${userId}/otp/send`,
    },
    RESEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/2fa/totp/user/otp/resend`,
    },
    VERIFY_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.PLATFORM}/2fa/totp/user/otp/verify`,
    },
    ACTIVATE_TOTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/totp/user/${userId}/code/activate`,
    },
    VERIFY_TOTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/totp/v2/user/${userId}/validate`,
    },
  },
  OTP_2FA: {
    SEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/otp/user/${userId}/otp/send`,
    },
    RESEND_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userId) => `${CONFIG.PLATFORM}/2fa/otp/user/${userId}/otp/resend`,
    },
    VERIFY_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: (userID) => `${CONFIG.PLATFORM}/2fa/otp/v2/user/${userID}/validate`,
    },
    VERIFY_MERCHANT_OTP: {
      method: REQUEST_METHODS.POST,
      endpoint: () => `${CONFIG.AUTH}/api/auth/merchant/otp/verify`,
    },
  },
  GET_FREEZING_NUMBER: {
    method: REQUEST_METHODS.GET,
    endpoint: () => `${CONFIG.PLATFORM}/s2s/onboarding/v1/crm/identifier/ACCOUNT_FREEZE/config`,
  },
};
