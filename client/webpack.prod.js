/* eslint-disable */
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserJSPlugin = require('terser-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const merge = require('webpack-merge');
const CommonConfig = require('./webpack.common.js');
const WorkersConfig = require('./webpack-workers.js');

const config = [];

for (const targetConfig of CommonConfig) {
  const { jsTarget } = targetConfig;
  delete targetConfig.jsTarget;
  config.push(merge(targetConfig, {
    mode: 'production',
    output: {
      filename: `js/${jsTarget}/[name].[chunkhash:8].js`,
      chunkFilename: `js/${jsTarget}/[name].[chunkhash:8].js`,
    },
    optimization: {
      usedExports: true,
      splitChunks: {
        chunks: 'all',
        minSize: 80 * 1024,
        maxSize: 100 * 1024,
        minChunks: 1,
        maxAsyncRequests: 5,
        maxInitialRequests: 3,
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
          },
          common: {
            test: /[\\/]common[\\/]/,
            priority: 0,
            minSize: 20 * 1024,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          }
        }
      },
      minimizer: [new TerserJSPlugin({}), new OptimizeCSSAssetsPlugin({})]
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: 'css/[name].[chunkhash:8].css',
        chunkFilename: 'css/[id].[chunkhash:8].css',
        ignoreOrder: true,
      }),
    ],
    performance: {
      hints: 'warning',
      //@TODO : to work on this to decrease size
      maxEntrypointSize: 120 * 1024,
      assetFilter(assetFilename) {
        return (assetFilename.endsWith('.js') || assetFilename.endsWith('.css')) && assetFilename.indexOf('vendors') === -1;
      },
    },
  }));
}

config.push(WorkersConfig);

module.exports = config;
