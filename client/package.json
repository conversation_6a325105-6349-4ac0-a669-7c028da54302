{"name": "equity-web-app-client", "version": "1.0.0", "description": "Equity Web App Client", "jest": {"testEnvironment": "jsdom", "verbose": true, "setupFiles": ["jest-canvas-mock"], "setupFilesAfterEnv": ["<rootDir>/src/config/test-setup.js", "jest-canvas-mock"], "moduleNameMapper": {"\\.(css|scss)$": "identity-obj-proxy", "^@utils(.*)$": "<rootDir>/src/utils$1", "^@commonStyles(.*)$": "<rootDir>/src/styles/common$1/", "^@service(.*)$": "<rootDir>/src/services$1", "^@common(.*)$": "<rootDir>/src/components/common$1", "^@modules(.*)$": "<rootDir>/src/components/modules$1", "^@constants(.*)$": "<rootDir>/src/constants$1", "^@layout(.*)$": "<rootDir>/src/layout$1", "^@config(.*)$": "<rootDir>/../config/$1", "^@pages(.*)$": "<rootDir>/src/components/pages$1", "^@/(.*)$": "<rootDir>/src/$1"}}, "scripts": {"start": "rm -rf dist && mkdir dist && cp static/index.html dist/ && NODE_ENV=staging webpack serve --env env=dev", "start:watch": "webpack --env env=dev --progress --watch", "build:dev": "NODE_ENV=development webpack --env env=dev --progress", "build:stage": "NODE_OPTIONS=--max_old_space_size=8192 NODE_ENV=staging webpack --env env=prod", "build:prod": "NODE_OPTIONS=--max_old_space_size=8192 NODE_ENV=production webpack --env env=prod", "lint": "../node_modules/.bin/eslint --ext .jsx,.js --ignore-path ../.eslintignore src/ && npm run stylelint-css", "eslint": "eslint --ext .jsx --ext .js src/ --ignore-pattern node_modules/", "eslint:fix": "eslint --ext .jsx --ext .js src/ --ignore-pattern node_modules/ --fix", "sass-lint": "scss-lint", "test": "TZ=UTC jest", "test:fix": "TZ=UTC jest -u", "clear-cache": "jest --clear<PERSON>ache", "stylelint-css": "stylelint '**/*.scss'", "stylelint-fix": "stylelint '**/*.scss' --fix"}, "repository": {"type": "git", "url": "http://rupee.paytmmoney.com/frontend/equity-web-app.git"}, "license": "ISC", "dependencies": {"axios": "^1.10.0", "chart.js": "^3.5.1", "chartjs-plugin-datalabels": "^2.0.0", "crypto-js": "^4.2.0", "isomorphic-dompurify": "^0.20.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^21.1.0", "lodash": "^4.17.15", "lottie-react": "^2.4.0", "moment": "^2.29.4", "node-rsa": "^1.0.7", "postcss": "^8.4.21", "prop-types": "^15.7.2", "querystring-es3": "^0.2.1", "rc-slider": "^9.6.2", "react-chartjs-2": "^3.0.5", "react-datetime": "^2.16.3", "react-slick": "^0.25.2", "react-sortable-hoc": "^1.11.0", "rxjs": "^6.5.3", "slick-carousel": "^1.8.1", "text-encoder": "0.0.4", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.14.7", "@babel/preset-react": "^7.14.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/react-hooks": "^3.2.1", "@webpack-cli/serve": "^2.0.1", "babel-eslint": "^10.0.3", "babel-jest": "^29.7.0", "babel-loader": "^8.0.6", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.4.4", "css-loader": "^3.2.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "isomorphic-ws": "^5.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.4.0", "mini-css-extract-plugin": "^2.7.2", "optimize-css-assets-webpack-plugin": "^6.0.1", "react-test-renderer": "^18.3.1", "regenerator-runtime": "^0.13.3", "sass": "^1.58.3", "sass-loader": "^8.0.0", "string-replace-loader": "^2.2.0", "style-loader": "^1.0.0", "stylelint": "^9.1.3", "stylelint-config-standard": "^18.2.0", "stylelint-order": "^0.8.1", "stylelint-scss": "^6.2.1", "terser-webpack-plugin": "^2.2.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "webpack-merge": "^4.2.2", "workbox-webpack-plugin": "^4.3.1", "ws": "^7.5.9"}}