FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:build-v14 AS build-client
WORKDIR /app
COPY client/package*.json ./
RUN npm ci

#---

FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:build-v14 AS build-server
WORKDIR /app
COPY package*.json ./
RUN npm ci

#---

FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:build-v14

ARG git_commit_id

ENV COMMIT_ID ${git_commit_id}
ENV TZ Asia/Kolkata

WORKDIR /app

RUN apk add --no-cache tini tzdata

COPY . .

COPY --from=build-server /app .
COPY --from=build-client /app ./client

RUN cd client/ && npm run build:prod

WORKDIR /app
ENTRYPOINT ["/sbin/tini","--"]
C<PERSON> [ "npm", "start" ]
