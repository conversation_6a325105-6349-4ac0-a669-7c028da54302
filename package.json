{"name": "equity-web-app", "author": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "http://rupee.paytmmoney.com/frontend/equity-web-app.git"}, "version": "1.0.0", "description": "Web platform for equity trading", "license": "ILC", "scripts": {"start": "node app.js", "sonar-scanner": "node_modules/sonar-scanner/bin/sonar-scanner", "equity": "concurrently --kill-others \"node app.js\" \"npm start --prefix client\""}, "dependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "axios": "^1.10.0", "babel-eslint": "^10.0.3", "babel-plugin-module-resolver": "^4.0.0", "body-parser": "^1.19.0", "circular-json": "^0.5.9", "cookie-parser": "^1.4.5", "csurf": "^1.10.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-jest": "^3.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "express": "^4.17.1", "express-handlebars": "^3.1.0", "express-healthcheck": "^0.1.0", "qrcode": "^1.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-loadable": "^5.5.0", "react-router-dom": "^5.1.2", "sonar-scanner": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/preset-env": "^7.9.6", "@babel/preset-react": "^7.9.4", "@babel/register": "^7.9.0", "concurrently": "^7.3.0"}}