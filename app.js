require('@babel/register')({
  extends: './.babelrc',
});

require.extensions['.scss'] = () => { };
require.extensions['.css'] = () => { };
const path = require('path');
const Loadable = require('react-loadable');
const express = require('express');
const healthCheck = require('express-healthcheck');
const exphbs = require('express-handlebars');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const { serveWithNoCacheHeaders } = require('./utils');
const {
  PORT, ENV, SIGNALS_CONFIG, SIG_URL,
} = require('./config');
const { routes } = require('./routers/client-routes');
const { EQUITY_BASE_URL } = require('./config/ENUMS');
const { default: serverRender } = require('./serverRender');
const { COOKIES } = require('./client/src/utils/enum');
const { csrfToken, csrfError, setXSRFCookie } = require('./middlewares/csrf');

const app = express();
const serverRouter = express.Router();

const version = process.env.COMMIT_ID;

console.info('"/health" shows server health stats'); // eslint-disable-line no-console

app.engine('handlebars', exphbs({ defaultLayout: null }));
app.set('view engine', 'handlebars');
app.set('views', path.join(__dirname, 'client/dist/views'));

/**
 * Internal endpoints
 * /health
 */
app.use('/health', healthCheck());

app.use(EQUITY_BASE_URL, serverRouter);

serverRouter.use(bodyParser.json());
serverRouter.use(cookieParser());
serverRouter.use(csrfToken, csrfError, setXSRFCookie);

serverRouter.use((req, res, next) => {
  try {
    const { headers } = req;
    if (headers[COOKIES.SSO_TOKEN] && headers[COOKIES.USER_AGENT]) {
      const expires = new Date();
      expires.setHours(23, 59, 59, 999);
      res.cookie(COOKIES.SSO_TOKEN, headers[COOKIES.SSO_TOKEN], {
        expires: new Date(expires),
        domain: '.paytmmoney.com',
        secure: true,
      });
      res.cookie(COOKIES.USER_AGENT, headers[COOKIES.USER_AGENT], {
        expires: new Date(expires),
        domain: '.paytmmoney.com',
        secure: true,
      });
      if (headers[COOKIES.TWOFA_TOKEN]) {
        res.cookie(COOKIES.TWOFA_TOKEN, headers[COOKIES.TWOFA_TOKEN], {
          expires: new Date(expires),
          domain: '.paytmmoney.com',
          secure: true,
        });
      }
    }
    next();
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log('Error while setting cookies', err);
  }
});

serverRouter.use('/static', express.static(path.join(__dirname, 'client/dist')));

serverRouter.get('/charts/v2/equity', (_, res) => {
  res.setHeader('Origin-Agent-Cluster', '?1');
  res.render('charts');
});

serverRouter.get('/service-worker.js', (_, res) => {
  res.header('service-worker-allowed', '/stocks');
  serveWithNoCacheHeaders('client/dist/workers/service-worker.js', res);
});

serverRouter.get('/workers/shared-worker.js', (_, res) => {
  serveWithNoCacheHeaders('client/dist/workers/shared-worker.js', res);
});

serverRouter.get('/sitemap.xml', (_, res) => {
  serveWithNoCacheHeaders('client/dist/sitemap.xml', res);
});

serverRouter.get('/get-version', (_, res) => res.status(200).send(version));

serverRouter.get('/time', (_, res) => {
  res.status(200).send({
    current: Date.now(),
  });
});
// Redirects
serverRouter.use('/kyc', (_, res) => {
  res.redirect('/dashboard');
});

serverRouter.get('/early-access', (_, res) => {
  res.redirect('/dashboard');
});

serverRouter.get('/edis-redirect', (_, res) => res.render('EDSLRedirect', {
  layout: false,
  version,
  env: ENV,
}));

serverRouter.get('/profile/margin-pledge/redirect', (_, res) => res.render('PledgeRedirect', {
  layout: false,
  version,
  env: ENV,
}));

serverRouter.get('/ipo-bank-redirect', (_, res) => res.render('HniAndEmployeeRedirect', {
  layout: false,
  version,
  env: ENV,
}));

serverRouter.post('/log-error', (req, res) => {
  try {
    const { body: error, headers } = req;
    // eslint-disable-next-line no-console
    console.log(JSON.stringify({ error, headers }));
    res.status(200).send('Successfully Logged');
  } catch (err) {
    res.status(400).send('Failed to log');
  }
});

const templateData = {
  version,
  env: ENV,
  markup: '',
  signalsConfigEnv: SIGNALS_CONFIG.environment,
  signalsConfigKey: SIGNALS_CONFIG.hmacKey,
  signalsUrl: SIG_URL,
};

// eslint-disable-next-line no-restricted-syntax
for (const { link, ssr } of routes) {
  if (ssr) {
    serverRouter.get(link, (req, res) => serverRender(req, res, templateData));
  } else {
    serverRouter.get(link, (_, res) => {
      res.render('index', {
        layout: false, ...templateData,
      });
    });
  }
}

Loadable.preloadAll().then(() => {
  // eslint-disable-next-line no-console
  app.listen(PORT, () => console.info(`Server running [port: ${PORT}, env: ${ENV}]`));
});
